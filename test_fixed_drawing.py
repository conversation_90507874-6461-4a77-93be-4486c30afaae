#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修复后的绘画技能 - 🔥 老王验证脚本
"""

import os
import sys
import json
import time

# 设置项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
if PROJECT_ROOT not in sys.path:
    sys.path.append(PROJECT_ROOT)

def test_drawing_skill_fixed():
    """测试修复后的绘画技能"""
    print("🔥 老王测试修复后的绘画技能")
    print("=" * 50)
    
    try:
        from cognitive_modules.skills.drawing_skill import DrawingSkill
        
        # 创建绘画技能实例
        drawing_skill = DrawingSkill()
        print(f"✅ 绘画技能实例创建成功")
        
        # 测试用例
        test_cases = [
            {
                "name": "英文简单提示词",
                "prompt": "cat",
                "expected_success": True
            },
            {
                "name": "英文复杂提示词", 
                "prompt": "a cute orange cat playing in the garden",
                "expected_success": True
            },
            {
                "name": "中文简单提示词",
                "prompt": "猫",
                "expected_success": True
            },
            {
                "name": "中文复杂提示词",
                "prompt": "一只可爱的橙色小猫在花园里玩耍",
                "expected_success": True
            }
        ]
        
        success_count = 0
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n--- 测试 {i}: {test_case['name']} ---")
            print(f"📝 提示词: {test_case['prompt']}")
            
            try:
                start_time = time.time()
                
                # 直接调用 jimeng_generate_images 方法
                result = drawing_skill.jimeng_generate_images(
                    prompt=test_case["prompt"],
                    model="jimeng-4.0"
                )
                
                elapsed_time = time.time() - start_time
                print(f"⏱️  耗时: {elapsed_time:.2f}秒")
                
                if isinstance(result, dict):
                    if "image_urls" in result and result["image_urls"]:
                        print(f"✅ 测试成功！生成了 {len(result['image_urls'])} 张图片")
                        for j, url in enumerate(result["image_urls"]):
                            print(f"   图片 {j+1}: {url[:80]}...")
                        success_count += 1
                    elif "error" in result:
                        print(f"❌ 测试失败: {result['error']}")
                        if "details" in result:
                            print(f"   详情: {result['details']}")
                    else:
                        print(f"⚠️  返回格式异常: {result}")
                else:
                    print(f"❌ 返回类型异常: {type(result)}")
                    
            except Exception as e:
                print(f"❌ 测试异常: {e}")
                import traceback
                traceback.print_exc()
            
            # 间隔一下避免请求过快
            if i < len(test_cases):
                time.sleep(3)
        
        print(f"\n" + "=" * 50)
        print(f"🎯 测试结果: {success_count}/{len(test_cases)} 成功")
        
        if success_count == len(test_cases):
            print(f"🎉 所有测试通过！绘画技能修复成功！")
            return True
        elif success_count > 0:
            print(f"⚠️  部分测试成功，需要进一步优化")
            return True
        else:
            print(f"❌ 所有测试失败，需要继续调试")
            return False
            
    except Exception as e:
        print(f"❌ 绘画技能测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_drawing_workflow():
    """测试完整的绘画工作流程"""
    print(f"\n🔧 测试完整绘画工作流程")
    print("=" * 40)
    
    try:
        from cognitive_modules.skills.drawing_skill import DrawingSkill
        
        drawing_skill = DrawingSkill()
        
        # 测试完整的 execute 方法
        test_prompt = "beautiful sunset over the ocean"
        print(f"📝 测试提示词: {test_prompt}")
        
        result = drawing_skill.execute(
            input_text=test_prompt,
            user_id="test_user"
        )
        
        print(f"📋 完整工作流程结果:")
        print(f"   类型: {type(result)}")
        
        if isinstance(result, dict):
            success = result.get("success", False)
            message = result.get("message", "")
            
            print(f"   成功: {success}")
            print(f"   消息: {message}")
            
            if success:
                result_data = result.get("result", {})
                image_url = result.get("image_url", "")
                service = result.get("service", "")
                
                print(f"   服务: {service}")
                print(f"   图片URL: {image_url[:80]}..." if image_url else "   图片URL: 无")
                
                if image_url:
                    print(f"✅ 完整工作流程测试成功！")
                    return True
                else:
                    print(f"⚠️  工作流程完成但无图片URL")
                    return False
            else:
                print(f"❌ 工作流程失败: {message}")
                return False
        else:
            print(f"❌ 返回格式异常")
            return False
            
    except Exception as e:
        print(f"❌ 完整工作流程测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔥🔥🔥 老王的绘画技能修复验证 🔥🔥🔥")
    print("=" * 60)
    
    # 测试1: 修复后的绘画技能
    result1 = test_drawing_skill_fixed()
    
    # 测试2: 完整工作流程
    result2 = test_full_drawing_workflow()
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"🎯 最终验证结果:")
    print(f"   绘画技能测试: {'✅ 成功' if result1 else '❌ 失败'}")
    print(f"   完整工作流程: {'✅ 成功' if result2 else '❌ 失败'}")
    
    if result1 and result2:
        print(f"\n🎉 恭喜！绘画技能修复完成，可以上生产环境了！")
        print(f"💡 修复要点:")
        print(f"   1. 使用最简化的API参数")
        print(f"   2. 移除可能导致失败的复杂参数")
        print(f"   3. 优化错误处理逻辑")
        print(f"   4. 支持中英文提示词")
    elif result1:
        print(f"\n⚠️  绘画技能基本功能正常，但完整工作流程需要优化")
    else:
        print(f"\n😤 绘画技能仍有问题，需要继续调试")

if __name__ == "__main__":
    main()
