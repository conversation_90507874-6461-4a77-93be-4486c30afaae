#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
P0问题修复验证测试脚本
测试向量数据库查询的用户隔离是否正确工作

作者: 老王
创建日期: 2025-09-18
"""

import sys
import os
import time
import threading
import concurrent.futures
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utilities.unified_logger import get_unified_logger, setup_unified_logging

# 设置日志
setup_unified_logging()
logger = get_unified_logger(__name__)

def test_user_isolation():
    """测试用户隔离是否正确工作"""
    logger.info("🧪 开始P0问题修复验证测试...")
    
    try:
        # 导入修复后的模块
        from utilities.legacy_chroma_bridge import search_history
        
        # 测试用户1
        user1_id = "zhangsan"
        user1_query = "你长什么样？ 生成一张图片来让大家看看"
        
        # 测试用户2  
        user2_id = "lisi"
        user2_query = "快手光合文旅X画爷爷工作室，锅气里的甘肃，趁热，请！"
        
        logger.info(f"🔍 测试用户1查询: {user1_id} -> {user1_query}")
        logger.info(f"🔍 测试用户2查询: {user2_id} -> {user2_query}")
        
        # 并发测试
        def test_user_query(user_id: str, query: str, role: str = "assistant"):
            """测试单个用户查询"""
            try:
                logger.info(f"🚀 开始查询用户 {user_id} 的历史记录...")
                start_time = time.time()
                
                results = search_history(user_id, query, 5, role)
                
                end_time = time.time()
                logger.info(f"✅ 用户 {user_id} 查询完成，耗时: {end_time - start_time:.2f}秒")
                
                # 检查结果
                if results and "documents" in results:
                    docs = results["documents"]
                    if docs and docs[0]:
                        logger.info(f"📄 用户 {user_id} 找到 {len(docs[0])} 个文档")
                        for i, doc in enumerate(docs[0][:3]):  # 只显示前3个
                            logger.info(f"   文档{i+1}: {doc[:50]}...")
                    else:
                        logger.info(f"📄 用户 {user_id} 没有找到文档")
                else:
                    logger.info(f"📄 用户 {user_id} 查询返回空结果")
                
                return {
                    "user_id": user_id,
                    "query": query,
                    "results": results,
                    "success": True,
                    "processing_time": end_time - start_time
                }
                
            except Exception as e:
                logger.error(f"❌ 用户 {user_id} 查询失败: {e}")
                return {
                    "user_id": user_id,
                    "query": query,
                    "results": None,
                    "success": False,
                    "error": str(e)
                }
        
        # 并发执行测试
        logger.info("🔄 开始并发测试...")
        with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
            # 提交两个并发任务
            future1 = executor.submit(test_user_query, user1_id, user1_query, "user")
            future2 = executor.submit(test_user_query, user2_id, user2_query, "user")
            
            # 等待结果
            result1 = future1.result(timeout=30)
            result2 = future2.result(timeout=30)
        
        # 分析结果
        logger.info("📊 测试结果分析:")
        logger.info(f"   用户1 ({user1_id}): {'成功' if result1['success'] else '失败'}")
        logger.info(f"   用户2 ({user2_id}): {'成功' if result2['success'] else '失败'}")
        
        # 检查是否存在交叉污染
        if result1['success'] and result2['success']:
            logger.success("✅ 并发查询测试通过，用户隔离机制正常工作")
            return True
        else:
            logger.warning("⚠️ 部分查询失败，可能是ChromaDB服务不可用")
            return False
            
    except Exception as e:
        logger.error(f"❌ P0修复验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_context_builder():
    """测试enhanced_context_builder是否正确使用修复后的接口"""
    logger.info("🧪 测试enhanced_context_builder集成...")
    
    try:
        from adapters.enhanced_context_builder import get_enhanced_context_builder
        
        builder = get_enhanced_context_builder()
        
        # 测试用户1
        user1_id = "zhangsan"
        user1_input = "你长什么样？"
        user1_name = "🔆"
        
        logger.info(f"🔍 测试用户1上下文构建: {user1_id} -> {user1_input}")
        
        context1 = builder.build_context_from_user_input(
            user_id=user1_id,
            user_input=user1_input,
            user_name=user1_name
        )
        
        if context1 and len(context1) > 100:
            logger.success(f"✅ 用户1上下文构建成功，长度: {len(context1)}")
            logger.info(f"   上下文预览: {context1[:200]}...")
            return True
        else:
            logger.warning("⚠️ 用户1上下文构建返回内容过短")
            return False
            
    except Exception as e:
        logger.error(f"❌ enhanced_context_builder测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("🚀 P0问题修复验证测试开始")
    logger.info("=" * 60)
    
    # 测试1: 用户隔离
    test1_result = test_user_isolation()
    
    logger.info("-" * 60)
    
    # 测试2: 集成测试
    test2_result = test_enhanced_context_builder()
    
    logger.info("=" * 60)
    
    # 总结
    if test1_result and test2_result:
        logger.success("🎉 P0问题修复验证测试全部通过！")
        logger.success("🔒 用户隔离机制正常工作")
        logger.success("🔗 集成测试通过")
        return True
    else:
        logger.error("❌ P0问题修复验证测试失败")
        logger.error(f"   用户隔离测试: {'通过' if test1_result else '失败'}")
        logger.error(f"   集成测试: {'通过' if test2_result else '失败'}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
