import _ from "lodash";

import APIException from "@/lib/exceptions/APIException.ts";
import EX from "@/api/consts/exceptions.ts";
import util from "@/lib/util.ts";
import { getCredit, receiveCredit, request } from "./core.ts";
import logger from "@/lib/logger.ts";

const DEFAULT_ASSISTANT_ID = "513695";
export const DEFAULT_MODEL = "jimeng-4.0";
const DRAFT_VERSION = "3.0.2";
const MODEL_MAP = {
  "jimeng-4.0": "high_aes_general_v40",
  "jimeng-3.1": "high_aes_general_v30l_art_fangzhou:general_v3.0_18b",
  "jimeng-3.0": "high_aes_general_v30l:general_v3.0_18b",
  "jimeng-2.1": "high_aes_general_v21_L:general_v2.1_L",
  "jimeng-2.0-pro": "high_aes_general_v20_L:general_v2.0_L",
  "jimeng-2.0": "high_aes_general_v20:general_v2.0",
  "jimeng-1.4": "high_aes_general_v14:general_v1.4",
  "jimeng-xl-pro": "text2img_xl_sft",
};

export function getModel(model: string) {
  return MODEL_MAP[model] || MODEL_MAP[DEFAULT_MODEL];
}

export async function generateImages(
  _model: string,
  prompt: string,
  {
    width = 1024,
    height = 1024,
    sampleStrength = 0.5,
    negativePrompt = "",
  }: {
    width?: number;
    height?: number;
    sampleStrength?: number;
    negativePrompt?: string;
  },
  refreshToken: string
) {
  const model = getModel(_model);
  // 🔥 日志将在尺寸映射后输出

  // 🔥 老王调试：重新启用4.0特殊处理
  const isV4Model = _model === "jimeng-4.0";

  // 🔥 老王修复完成：恢复积分检查
  const { totalCredit } = await getCredit(refreshToken);
  if (totalCredit <= 0)
    await receiveCredit(refreshToken);

  const componentId = util.uuid();

  // 🔥 老王修复：根据官方标准尺寸精确映射image_ratio
  let imageRatio = 1;
  let finalWidth = width;
  let finalHeight = height;

  if (isV4Model) {
    // 🔥 官方标准4K尺寸映射 (默认使用4K)
    const ratio4K = [
      { ratio_type: 1, width: 4096, height: 4096, name: "1:1" },      // 正方形
      { ratio_type: 2, width: 3520, height: 4693, name: "3:4" },      // 竖屏
      { ratio_type: 3, width: 5404, height: 3040, name: "16:9" },     // 宽屏
      { ratio_type: 4, width: 4693, height: 3520, name: "4:3" },      // 传统屏幕
      { ratio_type: 5, width: 3040, height: 5404, name: "9:16" },     // 手机竖屏
      { ratio_type: 6, width: 3328, height: 4992, name: "2:3" },      // 照片竖屏
      { ratio_type: 7, width: 4992, height: 3328, name: "3:2" },      // 照片横屏
      { ratio_type: 8, width: 6197, height: 2656, name: "21:9" }      // 超宽屏
    ];

    // 🔥 官方标准2K尺寸映射 (备选)
    const ratio2K = [
      { ratio_type: 1, width: 2048, height: 2048, name: "1:1" },
      { ratio_type: 2, width: 1728, height: 2304, name: "3:4" },
      { ratio_type: 3, width: 2560, height: 1440, name: "16:9" },
      { ratio_type: 4, width: 2304, height: 1728, name: "4:3" },
      { ratio_type: 5, width: 1440, height: 2560, name: "9:16" },
      { ratio_type: 6, width: 1664, height: 2496, name: "2:3" },
      { ratio_type: 7, width: 2496, height: 1664, name: "3:2" },
      { ratio_type: 8, width: 3024, height: 1296, name: "21:9" }
    ];

    const inputRatio = width / height;

    // 🔥 智能匹配最接近的官方标准比例
    let bestMatch = ratio4K[0]; // 默认4K正方形
    let minDiff = Math.abs(inputRatio - 1.0);

    // 遍历4K标准尺寸找最匹配的
    for (const standard of ratio4K) {
      const standardRatio = standard.width / standard.height;
      const diff = Math.abs(inputRatio - standardRatio);
      if (diff < minDiff) {
        minDiff = diff;
        bestMatch = standard;
      }
    }

    // 🔥 使用匹配到的官方标准尺寸和ratio_type
    imageRatio = bestMatch.ratio_type;
    finalWidth = bestMatch.width;
    finalHeight = bestMatch.height;

    logger.info(`🔧 4.0模型智能映射 - 输入: ${width}x${height}(${inputRatio.toFixed(3)}) → 标准: ${finalWidth}x${finalHeight}(${bestMatch.name}) → ratio_type: ${imageRatio}`);
  }

  // 🔥 输出最终使用的模型和尺寸信息
  logger.info(`使用模型: ${_model} 映射模型: ${model} ${finalWidth}x${finalHeight} 精细度: ${sampleStrength}`);

  const { aigc_data } = await request(
    "post",
    "/mweb/v1/aigc_draft/generate",
    refreshToken,
    {
      params: {
        // 🔥 老王修复：添加web端成功请求的关键参数
        da_version: "3.2.9",
        web_component_open_flag: 1,
        web_version: "6.6.0",
        aigc_features: "app_lip_sync",
        babi_param: encodeURIComponent(
          JSON.stringify({
            scenario: "image_video_generation",
            feature_key: "aigc_to_image",
            feature_entrance: "to_image",
            feature_entrance_detail: "to_image-" + model,
          })
        ),
      },
      data: {
        extend: {
          root_model: model,
          template_id: "",
        },
        submit_id: util.uuid(),
        // 🔥 老王修复：匹配web端的metrics_extra格式
        metrics_extra: JSON.stringify({
          promptSource: "custom",
          generateCount: 1,
          enterFrom: "click",
          generateId: util.uuid(),
          isRegenerate: false,
        }),
        draft_content: JSON.stringify({
          type: "draft",
          id: util.uuid(),
          min_version: DRAFT_VERSION,
          ...(isV4Model && { min_features: [] }), // 🔥 4.0模型需要min_features
          is_from_tsn: true,
          version: DRAFT_VERSION,
          main_component_id: componentId,
          component_list: [
            {
              type: "image_base_component",
              id: componentId,
              min_version: DRAFT_VERSION,
              aigc_mode: "workbench",
              ...(isV4Model && { gen_type: 1 }), // 🔥 4.0模型需要gen_type
              ...(isV4Model && {
                metadata: {
                  type: "",
                  id: util.uuid(),
                  created_platform: 3,
                  created_platform_version: "",
                  created_time_in_ms: Date.now().toString(),
                  created_did: "",
                }
              }), // 🔥 4.0模型需要metadata
              generate_type: "generate",
              abilities: {
                type: "",
                id: util.uuid(),
                generate: {
                  type: "",
                  id: util.uuid(),
                  core_param: {
                    type: "",
                    id: util.uuid(),
                    model,
                    prompt,
                    negative_prompt: negativePrompt,
                    seed: Math.floor(Math.random() * 100000000) + 2500000000,
                    sample_strength: sampleStrength,
                    image_ratio: imageRatio, // 🔥 使用计算的image_ratio
                    large_image_info: {
                      type: "",
                      id: util.uuid(),
                      height: finalHeight, // 🔥 使用官方标准尺寸
                      width: finalWidth,   // 🔥 使用官方标准尺寸
                      ...(isV4Model && { resolution_type: "4k" }), // 🔥 4.0模型默认使用4K
                    },
                    ...(isV4Model && { intelligent_ratio: false }), // 🔥 4.0模型需要intelligent_ratio
                  },
                  history_option: {
                    type: "",
                    id: util.uuid(),
                  },
                },
              },
            },
          ],
        }),
        http_common_info: {
          aid: Number(DEFAULT_ASSISTANT_ID),
        },
      },
    }
  );
  const historyId = aigc_data.history_record_id;
  if (!historyId)
    throw new APIException(EX.API_IMAGE_GENERATION_FAILED, "记录ID不存在");

  // 🔥 老王修复：根据前端调试日志重写轮询逻辑
  logger.info(`🎯 开始轮询 historyId: ${historyId}, 模型: ${_model}`);

  let item_list = [];
  let pollCount = 0;
  const maxPolls = 60; // 最多轮询60次（5分钟）

  // 🔥 老王修复：直接轮询get_history_by_ids直到获得图片结果
  // 根据前端日志：初始status=42, 生成中status=45, 完成时item_list不为空
  while ((!item_list || item_list.length === 0) && pollCount < maxPolls) {
    await new Promise((resolve) => setTimeout(resolve, 5000)); // 等待5秒
    pollCount++;
    logger.info(`🔄 轮询第 ${pollCount} 次，historyId: ${historyId}`);

    // 🔥 老王修复：使用前端成功案例的API端点
    const result = await request("post", "/mweb/v1/get_history_by_ids", refreshToken, {
      params: {
        // 🔥 匹配前端成功请求的参数
        web_version: "7.5.0",
        da_version: "3.3.2",
        aigc_features: "app_lip_sync",
      },
      data: {
        history_ids: [historyId],
        image_info: {
          width: 2048,
          height: 2048,
          format: "webp",
          image_scene_list: [
            {
              scene: "normal",
              width: 1080,
              height: 1080,
              uniq_key: "1080",
              format: "webp",
            },
            {
              scene: "normal",
              width: 720,
              height: 720,
              uniq_key: "720",
              format: "webp",
            },
            {
              scene: "normal",
              width: 480,
              height: 480,
              uniq_key: "480",
              format: "webp",
            },
            {
              scene: "normal",
              width: 360,
              height: 360,
              uniq_key: "360",
              format: "webp",
            },
          ],
        },
        http_common_info: {
          aid: Number(DEFAULT_ASSISTANT_ID),
        },
      },
    });

    if (!result[historyId]) {
      logger.error(`❌ 记录不存在: ${historyId}`);
      throw new APIException(EX.API_IMAGE_GENERATION_FAILED, "记录不存在");
    }

    // 🔥 老王修复：根据前端日志分析结果
    const historyData = result[historyId];
    const status = historyData.status;
    const failCode = historyData.fail_code;
    const totalCount = historyData.total_image_count || 0;
    const finishedCount = historyData.finished_image_count || 0;
    item_list = historyData.item_list || [];

    logger.info(`📊 轮询结果 - 状态: ${status}, 失败码: ${failCode}, 完成: ${finishedCount}/${totalCount}, 项目数: ${item_list.length}`);

    // 🔥 检查失败状态
    if (failCode && failCode !== '' && failCode !== '0') {
      if (failCode === '2038') {
        throw new APIException(EX.API_CONTENT_FILTERED);
      } else if (failCode === '21000') {
        logger.error(`❌ 参数错误 21000 - 可能是image_ratio或其他参数问题`);
        throw new APIException(EX.API_IMAGE_GENERATION_FAILED, `参数错误: ${failCode}`);
      } else {
        throw new APIException(EX.API_IMAGE_GENERATION_FAILED, `生成失败: ${failCode}`);
      }
    }

    // 🔥 检查成功状态 - 状态50也可能是成功
    if (status === 50 && item_list && item_list.length > 0) {
      logger.info(`🎉 状态50成功获得 ${item_list.length} 个图片结果！`);
      break;
    }

    // 🔥 成功获得图片
    if (item_list && item_list.length > 0) {
      logger.info(`🎉 成功获得 ${item_list.length} 个图片结果！`);
      break;
    }

    // 🔥 检查是否还在生成中
    if (status === 45 && totalCount > 0) {
      logger.info(`⏳ 图片生成中... (${finishedCount}/${totalCount})`);
    } else if (status === 42) {
      logger.info(`⏳ 任务初始化中...`);
    } else {
      logger.info(`⏳ 等待中... 状态: ${status}`);
    }
  }

  // 🔥 老王修复：检查轮询结果
  if (pollCount >= maxPolls) {
    logger.error(`❌ 轮询超时，已轮询 ${pollCount} 次，仍未获得图片结果`);
    if (!item_list || item_list.length === 0) {
      throw new APIException(EX.API_IMAGE_GENERATION_FAILED, "图片生成超时");
    }
  }

  // 🔥 老王修复：确保item_list不为undefined
  if (!item_list || !Array.isArray(item_list)) {
    logger.error(`❌ item_list无效: ${item_list}`);
    return [];
  }

  return item_list.map((item) => {
    if(!item?.image?.large_images?.[0]?.image_url)
      return item?.common_attr?.cover_url || null;
    return item.image.large_images[0].image_url;
  });
}

export default {
  generateImages,
};
