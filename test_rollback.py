#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试回滚后的ChromaDB查询
验证回滚到原始简单格式后是否正常工作

作者: 老王
创建日期: 2025-09-18
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utilities.unified_logger import get_unified_logger, setup_unified_logging

# 设置日志
setup_unified_logging()
logger = get_unified_logger(__name__)

def test_rollback_chromadb():
    """测试回滚后的ChromaDB查询"""
    logger.info("🧪 测试回滚后的ChromaDB查询...")
    
    try:
        from utilities.legacy_chroma_bridge import search_history
        
        # 测试参数
        user_id = "liu_defei_cool"
        query_text = "你好"
        n_results = 5
        
        logger.info(f"🔍 测试参数:")
        logger.info(f"   用户ID: {user_id}")
        logger.info(f"   查询内容: {query_text}")
        logger.info(f"   结果数量: {n_results}")
        
        # 测试1: 查询用户消息
        logger.info("=" * 50)
        logger.info("📝 测试查询用户消息 (role='user')...")
        user_result = search_history(user_id, query_text, n_results, "user")
        
        logger.info(f"用户消息查询结果类型: {type(user_result)}")
        if isinstance(user_result, dict) and "documents" in user_result:
            docs = user_result["documents"]
            if docs and len(docs) > 0 and docs[0]:
                logger.info(f"找到 {len(docs[0])} 个用户消息:")
                for i, doc in enumerate(docs[0]):
                    logger.info(f"   用户消息{i+1}: {doc}")
                
                # 检查是否是真实数据
                real_docs = [doc for doc in docs[0] if doc != "无记忆数据"]
                if real_docs:
                    logger.success(f"✅ 找到 {len(real_docs)} 个真实用户消息")
                else:
                    logger.warning("⚠️ 只找到空数据")
            else:
                logger.info("没有找到用户消息")
        
        # 测试2: 查询助手消息
        logger.info("=" * 50)
        logger.info("🤖 测试查询助手消息 (role='assistant')...")
        assistant_result = search_history(user_id, query_text, n_results, "assistant")
        
        logger.info(f"助手消息查询结果类型: {type(assistant_result)}")
        if isinstance(assistant_result, dict) and "documents" in assistant_result:
            docs = assistant_result["documents"]
            if docs and len(docs) > 0 and docs[0]:
                logger.info(f"找到 {len(docs[0])} 个助手消息:")
                for i, doc in enumerate(docs[0]):
                    logger.info(f"   助手消息{i+1}: {doc}")
                
                # 检查是否是真实数据
                real_docs = [doc for doc in docs[0] if doc != "无记忆数据"]
                if real_docs:
                    logger.success(f"✅ 找到 {len(real_docs)} 个真实助手消息")
                else:
                    logger.warning("⚠️ 只找到空数据")
            else:
                logger.info("没有找到助手消息")
        
        # 测试3: 无角色查询
        logger.info("=" * 50)
        logger.info("🔍 测试无角色查询 (role=None)...")
        no_role_result = search_history(user_id, query_text, n_results, None)
        
        logger.info(f"无角色查询结果类型: {type(no_role_result)}")
        if isinstance(no_role_result, dict) and "documents" in no_role_result:
            docs = no_role_result["documents"]
            if docs and len(docs) > 0 and docs[0]:
                logger.info(f"找到 {len(docs[0])} 个消息:")
                for i, doc in enumerate(docs[0]):
                    logger.info(f"   消息{i+1}: {doc}")
                
                # 检查是否是真实数据
                real_docs = [doc for doc in docs[0] if doc != "无记忆数据"]
                if real_docs:
                    logger.success(f"✅ 找到 {len(real_docs)} 个真实消息")
                else:
                    logger.warning("⚠️ 只找到空数据")
            else:
                logger.info("没有找到消息")
        
        logger.success("🎉 回滚后ChromaDB查询测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 回滚后ChromaDB查询测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_collection_isolation():
    """测试collection隔离机制"""
    logger.info("🧪 测试collection隔离机制...")
    
    try:
        from utilities.legacy_chroma_bridge import get_collection_id
        
        # 测试不同用户的collection_id
        test_users = ["liu_defei_cool", "test_user", "nonexistent_user"]
        
        for user_id in test_users:
            collection_id = get_collection_id(user_id)
            logger.info(f"用户 {user_id} 的collection_id: {collection_id}")
            
            if collection_id:
                logger.success(f"✅ 用户 {user_id} 有专属collection")
            else:
                logger.info(f"ℹ️ 用户 {user_id} 没有collection（正常情况）")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ collection隔离测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("🚀 回滚后ChromaDB查询测试开始")
    logger.info("=" * 60)
    
    # 测试1: 基本查询功能
    test1_result = test_rollback_chromadb()
    
    logger.info("-" * 60)
    
    # 测试2: collection隔离
    test2_result = test_collection_isolation()
    
    logger.info("=" * 60)
    
    # 总结
    if test1_result and test2_result:
        logger.success("🎉 回滚后ChromaDB查询测试全部通过！")
        logger.success("🔄 回滚到原始简单格式成功")
        logger.success("🔒 collection隔离机制正常")
        return True
    else:
        logger.error("❌ 回滚后ChromaDB查询测试失败")
        logger.error(f"   基本查询测试: {'通过' if test1_result else '失败'}")
        logger.error(f"   collection隔离测试: {'通过' if test2_result else '失败'}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
