#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试不同宽高比的支持 - 🔥 老王验证脚本
"""

import requests
import json
import time

def test_aspect_ratios():
    """测试不同宽高比的支持"""
    print("🔥 老王测试不同宽高比支持")
    print("=" * 50)
    
    # 后端API地址
    api_url = "http://127.0.0.1:47653/v1/images/generations"
    
    # 根据前端调试日志中的image_ratio_sizes定义测试用例
    test_cases = [
        {
            "name": "正方形 1:1",
            "width": 1024,
            "height": 1024,
            "expected_ratio_type": 1,
            "description": "标准正方形"
        },
        {
            "name": "竖屏 3:4", 
            "width": 768,
            "height": 1024,
            "expected_ratio_type": 2,
            "description": "手机竖屏比例"
        },
        {
            "name": "横屏 16:9",
            "width": 1024,
            "height": 576,
            "expected_ratio_type": 3,
            "description": "宽屏比例"
        },
        {
            "name": "横屏 4:3",
            "width": 1024,
            "height": 768,
            "expected_ratio_type": 4,
            "description": "传统屏幕比例"
        },
        {
            "name": "竖屏 9:16",
            "width": 576,
            "height": 1024,
            "expected_ratio_type": 5,
            "description": "手机竖屏比例"
        },
        {
            "name": "竖屏 2:3",
            "width": 683,
            "height": 1024,
            "expected_ratio_type": 6,
            "description": "照片比例"
        },
        {
            "name": "横屏 3:2",
            "width": 1024,
            "height": 683,
            "expected_ratio_type": 7,
            "description": "照片比例"
        },
        {
            "name": "超宽屏 21:9",
            "width": 1024,
            "height": 439,
            "expected_ratio_type": 8,
            "description": "电影屏幕比例"
        }
    ]
    
    # 测试session_id
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {test_case['name']} ---")
        print(f"📐 尺寸: {test_case['width']}x{test_case['height']}")
        print(f"📝 描述: {test_case['description']}")
        
        # 计算实际比例
        actual_ratio = test_case['width'] / test_case['height']
        print(f"📊 实际比例: {actual_ratio:.3f}")
        
        data = {
            "model": "jimeng-4.0",
            "prompt": f"beautiful landscape {test_case['name']}",
            "width": test_case['width'],
            "height": test_case['height'],
            "sample_strength": 0.5
        }
        
        try:
            start_time = time.time()
            print(f"🚀 发送请求...")
            
            response = requests.post(api_url, headers=headers, json=data, timeout=300)
            elapsed_time = time.time() - start_time
            
            print(f"⏱️  请求耗时: {elapsed_time:.2f}秒")
            print(f"📡 状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                if "data" in result and isinstance(result["data"], list):
                    data_list = result["data"]
                    created = result.get("created")
                    
                    print(f"📋 创建时间: {created}")
                    print(f"📋 数据长度: {len(data_list)}")
                    
                    if data_list and len(data_list) > 0:
                        print(f"✅ 成功生成 {len(data_list)} 张图片！")
                        for j, item in enumerate(data_list):
                            if isinstance(item, dict) and "url" in item:
                                print(f"   图片 {j+1}: {item['url'][:80]}...")
                        success_count += 1
                    else:
                        print(f"⚠️  数据为空")
                else:
                    print(f"⚠️  响应格式异常")
                    
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   错误详情: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
                except:
                    print(f"   响应文本: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        # 间隔一下避免请求过快
        if i < len(test_cases):
            print(f"⏳ 等待15秒后进行下一个测试...")
            time.sleep(15)
    
    print(f"\n" + "=" * 50)
    print(f"🎯 测试结果: {success_count}/{len(test_cases)} 成功")
    
    if success_count == len(test_cases):
        print(f"🎉 所有宽高比测试通过！")
        return True
    elif success_count > 0:
        print(f"⚠️  部分宽高比测试成功")
        return True
    else:
        print(f"❌ 所有宽高比测试失败")
        return False

def test_extreme_ratios():
    """测试极端宽高比"""
    print(f"\n🧪 测试极端宽高比")
    print("=" * 30)
    
    api_url = "http://127.0.0.1:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    extreme_cases = [
        {
            "name": "超宽屏 32:9",
            "width": 1024,
            "height": 288,
            "description": "极端宽屏"
        },
        {
            "name": "超竖屏 9:32",
            "width": 288,
            "height": 1024,
            "description": "极端竖屏"
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(extreme_cases, 1):
        print(f"\n--- 极端测试 {i}: {test_case['name']} ---")
        print(f"📐 尺寸: {test_case['width']}x{test_case['height']}")
        print(f"📝 描述: {test_case['description']}")
        
        actual_ratio = test_case['width'] / test_case['height']
        print(f"📊 实际比例: {actual_ratio:.3f}")
        
        data = {
            "model": "jimeng-4.0",
            "prompt": f"abstract art {test_case['name']}",
            "width": test_case['width'],
            "height": test_case['height'],
            "sample_strength": 0.5
        }
        
        try:
            start_time = time.time()
            response = requests.post(api_url, headers=headers, json=data, timeout=300)
            elapsed_time = time.time() - start_time
            
            print(f"⏱️  请求耗时: {elapsed_time:.2f}秒")
            print(f"📡 状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if "data" in result and result["data"]:
                    print(f"✅ 极端比例测试成功！")
                    success_count += 1
                else:
                    print(f"⚠️  极端比例测试无结果")
            else:
                print(f"❌ 极端比例测试失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 极端比例测试异常: {e}")
        
        if i < len(extreme_cases):
            time.sleep(15)
    
    print(f"\n🎯 极端比例测试结果: {success_count}/{len(extreme_cases)} 成功")
    return success_count > 0

def main():
    """主函数"""
    print("🔥🔥🔥 老王的宽高比支持验证 🔥🔥🔥")
    print("=" * 60)
    
    # 测试1: 标准宽高比
    result1 = test_aspect_ratios()
    
    # 测试2: 极端宽高比
    result2 = test_extreme_ratios()
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"🎯 最终验证结果:")
    print(f"   标准宽高比: {'✅ 成功' if result1 else '❌ 失败'}")
    print(f"   极端宽高比: {'✅ 成功' if result2 else '❌ 失败'}")
    
    if result1 and result2:
        print(f"\n🎉 恭喜！所有宽高比都支持！")
        print(f"💡 支持的比例:")
        print(f"   1. 正方形 1:1")
        print(f"   2. 竖屏 3:4, 9:16, 2:3")
        print(f"   3. 横屏 16:9, 4:3, 3:2")
        print(f"   4. 超宽屏 21:9, 32:9")
        print(f"   5. 超竖屏 9:32")
    elif result1:
        print(f"\n⚠️  标准宽高比支持良好，极端比例需要优化")
    else:
        print(f"\n😤 宽高比支持有问题，需要调试")

if __name__ == "__main__":
    main()
