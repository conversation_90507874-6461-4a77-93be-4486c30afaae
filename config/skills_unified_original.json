{"skill_manager": {"auto_load_skills": true, "skills_path": "cognitive_modules.skills", "intent_to_skill_map": {"query": "search_skill", "recommendation": "search_skill", "navigation": "search_skill", "drawing": "drawing_skill", "entertainment": "drawing_skill", "url": "search_skill", "business": "search_skill", "learning": "search_skill", "support": "search_skill", "task": "search_skill", "chat": "chat_skill"}, "keyword_to_skill_map": {"画图": "drawing_skill", "画一张": "drawing_skill", "画一幅": "drawing_skill"}, "event_handlers": {"autonomy.decision": true, "perception.intent": true}, "fallback_skill": "chat_skill"}, "chat_skill": {"default_responses": {"greeting": "你好，有什么我可以帮助你的吗？", "farewell": "再见，期待下次与你交流！", "unknown": "我不太明白你的意思，能否换个方式表达？", "error": "抱歉，我遇到了一些问题，请稍后再试。"}, "use_llm": false, "llm_model": "abab6.5s-chat", "temperature": 0.7, "max_tokens": 2048}, "drawing_skill": {"pre_image_model": "abab6.5s-chat", "pre_create_image": "你是一个专业的图像prompt生成器。我会向你提供一个场景描述，你需要将其转换为详细的文本图像生成提示。请确保你的提示能充分描述用户想要的图像，包括风格、质量、细节等，以生成高质量图像。输出格式为：\n\nimage_prompt: [详细的图像生成提示，包括主题、样式等]\naspect_ratio: [图像比例，通常为9:16或16:9，还有宽屏21:9可以更宽的大尺寸，酌情选择]", "jimeng": {"enabled": true, "url": "http://124.221.30.195:47653/v1/images/generations", "session_id": "1e6aa5b4800b56a3e345bacacfaa7c09,3714d0ec74234a6f797c2e9d32d539d3,60b8e545a59ff3fbd478cdba53ae7676", "model": "jimeng-4.0", "negative_prompt": "低质量，模糊画面，混乱布局，扭曲的场景，畸形的肢体，不协调的颜色，不真实"}, "keling": {"enabled": false, "module_path": "keling.kling_pic"}, "minimax": {"enabled": false, "api_key": "", "api_url": ""}, "default_aspect_ratio": "9:16", "max_memory_size": 6}, "search_skill": {"search_models": {"primary": "WPSAI", "fallback": "<PERSON>_<PERSON><PERSON><PERSON>"}, "max_memory_size": 2, "format_search_model": "abab6.5s-chat", "format_search_prompt": "你作为用户的私人助理嫣然，协助用户处理如下事务：以下是用户需要百度一下，或者Google一下的搜索的口语化语言。接下来需要分析语意，然后将用户语言转换成搜索引擎用的描述，过滤跟搜索不相关的信息，以提高搜索效率。输出要求：只允许输出搜索用的描述，其它的信息一个字符都不被允许！！！", "api_timeout": 600, "temperature": 0.9, "max_tokens": 4096, "api_key": "sk-HJVDtjSNhs0cYIE_K15PmVHHxSbiYaOf0BhHFj1WOXoI0CfWdP2pnE7RbC4", "api_base": "https://oneapi.xiongmaodaxia.online/v1", "default_model": "gpt-3.5-turbo-128k"}, "music_skill": {"enabled": true, "api_key": "sk-HJVDtjSNhs0cYIE_K15PmVHHxSbiYaOf0BhHFj1WOXoI0CfWdP2pnE7RbC4", "api_endpoint": "https://api.music.example.com/v1", "max_memory_size": 2, "default_settings": {"format": "mp3", "quality": "high"}, "timeout": 15, "cache_enabled": true, "cache_time": 1800}, "global_skill_settings": {"cache_enabled": true, "cache_ttl": 3600, "default_confidence_threshold": 0.6, "log_level": "INFO", "max_retry_attempts": 3, "timeout": 30}, "api_configs": {"openai": {"api_key": "sk-lJ1R156TicBrrU9G2a789f03F5C14515993256Fe306d4691", "api_base": "https://oneapi.xiongmaodaxia.online/v1", "model": "deepseek-preview", "temperature": 0.3, "max_tokens": 2000, "options": {"intention_analysis": {"model": "deepseek-preview", "temperature": 0.3}, "knowledge_generation": {"model": "deepseek-preview", "temperature": 0.7}}}, "ai_services": {"default_service": "openai", "services": {"openai": {"api_key": "sk-HJVDtjSNhs0cYIE_K15PmVHHxSbiYaOf0BhHFj1WOXoI0CfWdP2pnE7RbC4", "base_url": "https://oneapi.xiongmaodaxia.online/v1", "model": "gpt-3.5-turbo-128k", "max_tokens": 4096, "temperature": 0.7, "timeout": 60}, "zhipu": {"api_key": "af5e338a79c8fb3cee0fabbce890940f.oaivs9yIiWnqYOGk", "base_url": "https://open.bigmodel.cn/api/paas/v4", "model": "glm-4", "max_tokens": 2048, "temperature": 0.7, "timeout": 60}, "qianwen": {"api_key": "sk_1c1d60bf4b1149bdbb732fc9b0f0d736", "base_url": "https://dashscope.aliyuncs.com/api/v1", "model": "qwen-max", "max_tokens": 2048, "temperature": 0.7, "timeout": 60}, "compatible_service": {"api_key": "sk-HJVDtjSNhs0cYIE_K15PmVHHxSbiYaOf0BhHFj1WOXoI0CfWdP2pnE7RbC4", "base_url": "https://oneapi.xiongmaodaxia.online/v1", "model": "gpt-3.5-turbo-128k", "max_tokens": 4096, "temperature": 0.7, "timeout": 60}}}}}