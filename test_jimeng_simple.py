#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 老王适配异步jimeng API的测试脚本
完全按照drawing_skill.py的逻辑，测试异步轮询机制
"""

import requests
import json
import time
from datetime import datetime

def test_jimeng_simple():
    """简化版jimeng API测试"""
    
    print("🔥 老王异步jimeng API轮询测试")
    print("=" * 60)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("📋 测试逻辑：完全按照drawing_skill.py的异步轮询机制")
    
    # API配置
    url = "http://124.221.30.195:47653/v1/images/generations"
    
    # 🔥 按照README要求：多session_id用逗号分隔，API会自动选择
    session_ids = "1e6aa5b4800b56a3e345bacacfaa7c09,3714d0ec74234a6f797c2e9d32d539d3,60b8e545a59ff3fbd478cdba53ae7676"
    
    headers = {
        "Authorization": f"Bearer {session_ids}",
        "Content-Type": "application/json"
    }
    
    # 测试用例
    test_cases = [
        {
            "name": "复杂提示词测试",
            "data": {
                "model": "jimeng-4.0", 
                "prompt": "（镜头脏渍形成左上角光斑），画面仅截取下巴到胸口：奶杏色针织开衫垮至右肩（肩头三道0.5cm勾丝），汗湿项链吊坠因手抖糊成银色光团。背景虚焦厨房：左侧妈妈碎花围裙身影（颠锅动作拖出6cm运动残影），右侧冰箱过曝成纯白（仅见“설렁탕”韩文便签边角）。窗光斜劈锁骨（形成锯齿状曝光明暗分界线），两缕棕发粘在汗湿颈侧（发梢虚化如毛玻璃）。镜头畸变使微波炉绿灯在开衫领口映出椭圆霉斑色光晕。",
                "negative_prompt": "低质量，模糊画面",
                "width": 1024,
                "height": 1024,
                "sample_strength": 0.7,
                "response_format": "url"
            }
        }
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试 {i}/{total_tests}: {test_case['name']}")
        print("-" * 50)
        
        test_data = test_case['data']
        print(f"📋 请求参数:")
        for key, value in test_data.items():
            print(f"   {key}: {value}")
        
        print(f"\n🔗 请求URL: {url}")
        print(f"🔑 Authorization: Bearer {session_ids[:50]}...")
        
        try:
            # 记录请求开始时间
            start_time = time.time()
            
            print(f"\n📤 发送请求...")
            response = requests.post(url, headers=headers, json=test_data, timeout=300)
            
            # 记录请求结束时间
            end_time = time.time()
            request_time = end_time - start_time
            
            print(f"📥 响应状态码: {response.status_code}")
            print(f"⏱️ 请求耗时: {request_time:.2f}秒")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    
                    print(f"\n📋 完整响应内容:")
                    print(json.dumps(result, indent=2, ensure_ascii=False))
                    
                    print(f"\n🔍 响应分析:")
                    print(f"   响应类型: {type(result)}")
                    
                    if isinstance(result, dict):
                        print(f"   包含字段: {list(result.keys())}")
                        
                        created = result.get("created")
                        data_list = result.get("data", [])
                        
                        print(f"   created: {created}")
                        print(f"   data类型: {type(data_list)}")
                        print(f"   data长度: {len(data_list) if isinstance(data_list, list) else 'N/A'}")
                        
                        if data_list and isinstance(data_list, list):
                            print(f"\n✅ 立即生成成功！获得 {len(data_list)} 张图片:")
                            for idx, item in enumerate(data_list):
                                print(f"   图片 {idx+1}:")
                                if isinstance(item, dict):
                                    for k, v in item.items():
                                        if k == "url":
                                            # 显示URL的前80个字符
                                            url_preview = v[:80] + "..." if len(v) > 80 else v
                                            print(f"     {k}: {url_preview}")
                                        else:
                                            print(f"     {k}: {v}")
                                else:
                                    print(f"     内容: {item}")

                            success_count += 1
                            print(f"\n🎉 测试用例 '{test_case['name']}' 立即成功！")

                        elif created:
                            # 🔥 按照drawing_skill.py的逻辑：如果data为空但有created，开始轮询
                            print(f"\n🔄 data为空但有created时间戳，开始轮询等待生成完成...")
                            print(f"   任务ID: {created}")

                            max_polls = 30  # 最多轮询30次
                            poll_interval = 10  # 每10秒轮询一次
                            poll_success = False

                            for poll_count in range(1, max_polls + 1):
                                print(f"🔄 轮询 {poll_count}/{max_polls} (任务: {created})...")
                                time.sleep(poll_interval)

                                try:
                                    # 重新请求检查结果 - 使用相同参数
                                    poll_response = requests.post(url, headers=headers, json=test_data, timeout=60)

                                    if poll_response.status_code == 200:
                                        poll_result = poll_response.json()
                                        poll_data = poll_result.get("data", [])
                                        poll_created = poll_result.get("created")

                                        print(f"   轮询响应: created={poll_created}, data长度={len(poll_data)}")

                                        # 检查是否有结果
                                        if poll_data and len(poll_data) > 0:
                                            print(f"\n✅ 轮询成功！生成 {len(poll_data)} 张图片:")
                                            for idx, item in enumerate(poll_data):
                                                if isinstance(item, dict) and "url" in item:
                                                    url_preview = item["url"][:80] + "..." if len(item["url"]) > 80 else item["url"]
                                                    print(f"   {idx+1}. {url_preview}")

                                            success_count += 1
                                            poll_success = True
                                            print(f"\n🎉 测试用例 '{test_case['name']}' 轮询成功！")
                                            break
                                        else:
                                            print(f"   任务仍在生成中...")
                                    else:
                                        print(f"   轮询失败: HTTP {poll_response.status_code}")

                                except Exception as poll_e:
                                    print(f"   轮询异常: {poll_e}")

                            if not poll_success:
                                print(f"\n❌ 轮询超时，任务可能失败或需要更长时间")

                        else:
                            print(f"\n⚠️ 无效响应：无data且无created时间戳")
                            print(f"   data内容: {data_list}")
                            print(f"   created: {created}")
                    else:
                        print(f"   ⚠️ 响应不是字典格式: {result}")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"原始响应内容:")
                    print(response.text)
                    
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"错误响应:")
                print(response.text)
                
        except requests.exceptions.Timeout:
            print(f"⏰ 请求超时（5分钟）")
        except Exception as e:
            print(f"💥 请求异常: {e}")
        
        print(f"\n{'='*50}")
    
    # 测试总结
    print(f"\n📊 测试总结:")
    print(f"   总测试用例: {total_tests}")
    print(f"   成功用例: {success_count}")
    print(f"   失败用例: {total_tests - success_count}")
    print(f"   成功率: {(success_count/total_tests)*100:.1f}%")
    
    print(f"\n⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success_count > 0:
        print("🎉 至少有部分测试成功！")
        if success_count == total_tests:
            print("✅ 所有测试都成功，API工作正常！")
        else:
            print("⚠️ 部分测试失败，需要进一步分析")
    else:
        print("❌ 所有测试都失败，需要检查API配置")
    
    print("🔥 简化版jimeng API测试完成！")
    
    return success_count, total_tests

if __name__ == "__main__":
    print("🚀 开始简化版jimeng API测试...")
    
    success, total = test_jimeng_simple()
    
    print(f"\n🏁 最终结果: {success}/{total} 测试成功")
    
    if success > 0:
        print("💡 分析结果:")
        if success == total:
            print("   ✅ 异步轮询机制工作正常！")
            print("   ✅ drawing_skill.py的逻辑是正确的")
            print("   ✅ 可以安全使用新版API")
        else:
            print("   ⚠️ 部分测试成功，部分失败")
            print("   🔍 可能是特定参数或网络问题")
            print("   💡 建议检查失败的测试用例")
    else:
        print("💡 问题诊断:")
        print("   ❌ 所有测试都失败")
        print("   🔍 可能原因:")
        print("     - session_id无效或过期")
        print("     - API服务未正常运行")
        print("     - 网络连接问题")
        print("     - 轮询超时时间不够")
