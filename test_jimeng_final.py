#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
最终测试 jimeng API - 🔥 老王终极调试
"""

import requests
import json
import time

def test_with_different_session_ids():
    """测试不同的 session_id"""
    print("🔥 老王的 jimeng 终极测试")
    print("=" * 50)
    
    url = "http://124.221.30.195:47653/v1/images/generations"
    
    # 测试所有有效的 session_id
    session_ids = [
        "1e6aa5b4800b56a3e345bacacfaa7c09",
        "3714d0ec74234a6f797c2e9d32d539d3", 
        "60b8e545a59ff3fbd478cdba53ae7676"
    ]
    
    # 简单的测试数据
    data = {
        "model": "jimeng-4.0",
        "prompt": "一只可爱的小猫",
        "width": 512,
        "height": 512,
        "sample_strength": 0.5,
        "response_format": "url"
    }
    
    print(f"📝 测试提示词: {data['prompt']}")
    
    for i, session_id in enumerate(session_ids, 1):
        print(f"\n--- 测试 Session ID {i} ---")
        print(f"🔑 Session ID: {session_id[:20]}...")
        
        headers = {
            "Authorization": f"Bearer {session_id}",
            "Content-Type": "application/json"
        }
        
        try:
            start_time = time.time()
            response = requests.post(url, headers=headers, json=data, timeout=60)
            elapsed_time = time.time() - start_time
            
            print(f"⏱️  请求耗时: {elapsed_time:.2f}秒")
            print(f"📡 状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                # 检查错误码
                if "code" in result:
                    code = result.get("code")
                    message = result.get("message", "")
                    
                    if code == 0:
                        print(f"✅ 请求成功 (code: {code})")
                    elif code == -2001:
                        print(f"❌ 登录验证失败 (code: {code}): {message}")
                        continue
                    else:
                        print(f"⚠️  其他错误 (code: {code}): {message}")
                        continue
                
                # 检查数据
                data_list = result.get("data", [])
                created = result.get("created")
                
                print(f"📋 创建时间: {created}")
                print(f"📋 数据长度: {len(data_list)}")
                
                if data_list and len(data_list) > 0:
                    print(f"🎉 成功获得图片！")
                    for j, item in enumerate(data_list):
                        if isinstance(item, dict) and "url" in item:
                            print(f"   图片 {j+1}: {item['url']}")
                    return True
                else:
                    print(f"⚠️  数据为空")
                    
                    # 尝试等待一段时间后再次请求同一个任务
                    print(f"🔄 等待30秒后重新检查...")
                    time.sleep(30)
                    
                    retry_response = requests.post(url, headers=headers, json=data, timeout=60)
                    if retry_response.status_code == 200:
                        retry_result = retry_response.json()
                        retry_data = retry_result.get("data", [])
                        retry_created = retry_result.get("created")
                        
                        print(f"   重试结果: created={retry_created}, data长度={len(retry_data)}")
                        
                        if retry_data and len(retry_data) > 0:
                            print(f"🎉 重试成功获得图片！")
                            for j, item in enumerate(retry_data):
                                if isinstance(item, dict) and "url" in item:
                                    print(f"   图片 {j+1}: {item['url']}")
                            return True
                        else:
                            print(f"   重试仍无结果")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"   响应: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        time.sleep(5)  # 间隔5秒测试下一个
    
    return False

def test_minimal_request():
    """测试最小化请求"""
    print(f"\n🧪 测试最小化请求")
    print("=" * 30)
    
    url = "http://124.221.30.195:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    # 最小化数据
    data = {
        "model": "jimeng-4.0",
        "prompt": "cat"
    }
    
    print(f"📝 最小化提示词: {data['prompt']}")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=60)
        
        print(f"📡 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📋 响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            data_list = result.get("data", [])
            if data_list and len(data_list) > 0:
                print(f"🎉 最小化请求成功！")
                return True
            else:
                print(f"⚠️  最小化请求也无结果")
        else:
            print(f"❌ 最小化请求失败: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ 最小化请求异常: {e}")
    
    return False

def test_combined_session_ids():
    """测试组合的 session_id"""
    print(f"\n🔗 测试组合 session_id")
    print("=" * 30)
    
    url = "http://124.221.30.195:47653/v1/images/generations"
    
    # 组合所有有效的 session_id
    combined_session_ids = "1e6aa5b4800b56a3e345bacacfaa7c09,3714d0ec74234a6f797c2e9d32d539d3,60b8e545a59ff3fbd478cdba53ae7676"
    
    headers = {
        "Authorization": f"Bearer {combined_session_ids}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "jimeng-4.0",
        "prompt": "一朵红玫瑰",
        "width": 512,
        "height": 512,
        "sample_strength": 0.5,
        "response_format": "url"
    }
    
    print(f"📝 提示词: {data['prompt']}")
    print(f"🔑 组合 Session IDs: {combined_session_ids[:50]}...")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=60)
        
        print(f"📡 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            # 检查错误
            if "code" in result and result.get("code") != 0:
                print(f"❌ API错误: {result.get('message', '未知错误')}")
                return False
            
            data_list = result.get("data", [])
            created = result.get("created")
            
            print(f"📋 创建时间: {created}")
            print(f"📋 数据长度: {len(data_list)}")
            
            if data_list and len(data_list) > 0:
                print(f"🎉 组合 session_id 成功！")
                for i, item in enumerate(data_list):
                    if isinstance(item, dict) and "url" in item:
                        print(f"   图片 {i+1}: {item['url']}")
                return True
            else:
                print(f"⚠️  组合 session_id 也无结果")
        else:
            print(f"❌ 组合请求失败: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ 组合请求异常: {e}")
    
    return False

def main():
    """主函数"""
    print("🔥🔥🔥 老王的 jimeng API 终极测试 🔥🔥🔥")
    print("=" * 60)
    
    results = []
    
    # 测试1: 不同的 session_id
    print("📋 测试1: 不同的 session_id")
    result1 = test_with_different_session_ids()
    results.append(("不同session_id", result1))
    
    # 测试2: 最小化请求
    print("📋 测试2: 最小化请求")
    result2 = test_minimal_request()
    results.append(("最小化请求", result2))
    
    # 测试3: 组合 session_id
    print("📋 测试3: 组合 session_id")
    result3 = test_combined_session_ids()
    results.append(("组合session_id", result3))
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"🎯 最终测试结果:")
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n📊 总体结果: {success_count}/{len(results)} 成功")
    
    if success_count > 0:
        print(f"🎉 有测试成功！jimeng API 可以工作")
        print(f"💡 建议: 使用成功的配置进行生产环境部署")
    else:
        print(f"😤 所有测试都失败了！")
        print(f"💡 可能的原因:")
        print(f"   1. API服务器问题")
        print(f"   2. session_id权限不足")
        print(f"   3. API接口变更")
        print(f"   4. 网络连接问题")
        print(f"   5. 需要特殊的请求参数")

if __name__ == "__main__":
    main()
