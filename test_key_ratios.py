#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试关键宽高比 - 🔥 老王快速验证
"""

import requests
import json
import time

def test_key_ratios():
    """测试关键宽高比"""
    print("🔥 老王测试关键宽高比")
    print("=" * 50)
    
    api_url = "http://127.0.0.1:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    # 测试关键比例
    test_cases = [
        {
            "name": "正方形 1:1",
            "width": 1024,
            "height": 1024,
            "expected_ratio": 1,
            "prompt": "beautiful square garden"
        },
        {
            "name": "手机竖屏 9:16",
            "width": 576,
            "height": 1024,
            "expected_ratio": 5,
            "prompt": "mobile portrait photo"
        },
        {
            "name": "宽屏 16:9",
            "width": 1024,
            "height": 576,
            "expected_ratio": 3,
            "prompt": "widescreen landscape"
        },
        {
            "name": "传统屏幕 4:3",
            "width": 1024,
            "height": 768,
            "expected_ratio": 4,
            "prompt": "classic screen ratio"
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {test_case['name']} ---")
        print(f"📐 尺寸: {test_case['width']}x{test_case['height']}")
        print(f"🎯 期望ratio: {test_case['expected_ratio']}")
        
        data = {
            "model": "jimeng-4.0",
            "prompt": test_case['prompt'],
            "width": test_case['width'],
            "height": test_case['height'],
            "sample_strength": 0.5
        }
        
        try:
            start_time = time.time()
            print(f"🚀 发送请求...")
            
            response = requests.post(api_url, headers=headers, json=data, timeout=300)
            elapsed_time = time.time() - start_time
            
            print(f"⏱️  请求耗时: {elapsed_time:.2f}秒")
            print(f"📡 状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                if "data" in result and result["data"]:
                    data_list = result["data"]
                    print(f"✅ 成功生成 {len(data_list)} 张图片！")
                    for j, item in enumerate(data_list):
                        if isinstance(item, dict) and "url" in item:
                            print(f"   图片 {j+1}: {item['url'][:80]}...")
                    success_count += 1
                else:
                    print(f"⚠️  数据为空")
                    
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   错误详情: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
                except:
                    print(f"   响应文本: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        # 间隔一下
        if i < len(test_cases):
            print(f"⏳ 等待10秒...")
            time.sleep(10)
    
    print(f"\n" + "=" * 50)
    print(f"🎯 关键比例测试结果: {success_count}/{len(test_cases)} 成功")
    
    return success_count == len(test_cases)

def test_drawing_skill_format():
    """测试drawing_skill.py的调用格式"""
    print(f"\n🎨 测试drawing_skill.py调用格式")
    print("=" * 40)
    
    api_url = "http://127.0.0.1:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    # 模拟drawing_skill.py的调用格式
    data = {
        "model": "jimeng-4.0",
        "prompt": "美丽的风景画，夕阳下的湖泊",
        "negative_prompt": "",
        "width": 1024,
        "height": 1024,
        "sample_strength": 0.5,
        "response_format": "url"
    }
    
    print(f"📝 提示词: {data['prompt']}")
    print(f"📐 尺寸: {data['width']}x{data['height']}")
    print(f"🎛️ 精细度: {data['sample_strength']}")
    print(f"📋 响应格式: {data['response_format']}")
    
    try:
        start_time = time.time()
        response = requests.post(api_url, headers=headers, json=data, timeout=300)
        elapsed_time = time.time() - start_time
        
        print(f"⏱️  请求耗时: {elapsed_time:.2f}秒")
        print(f"📡 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if "data" in result and result["data"]:
                data_list = result["data"]
                print(f"✅ drawing_skill格式测试成功！生成 {len(data_list)} 张图片")
                for j, item in enumerate(data_list):
                    if isinstance(item, dict) and "url" in item:
                        print(f"   图片 {j+1}: {item['url'][:80]}...")
                return True
            else:
                print(f"⚠️  drawing_skill格式测试无结果")
        else:
            print(f"❌ drawing_skill格式测试失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ drawing_skill格式测试异常: {e}")
    
    return False

def main():
    """主函数"""
    print("🔥🔥🔥 老王的关键比例验证 🔥🔥🔥")
    print("=" * 60)
    
    # 测试1: 关键宽高比
    result1 = test_key_ratios()
    
    # 测试2: drawing_skill调用格式
    result2 = test_drawing_skill_format()
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"🎯 最终验证结果:")
    print(f"   关键宽高比: {'✅ 成功' if result1 else '❌ 失败'}")
    print(f"   drawing_skill格式: {'✅ 成功' if result2 else '❌ 失败'}")
    
    if result1 and result2:
        print(f"\n🎉 恭喜！宽高比支持完美，可以集成到drawing_skill.py！")
        print(f"💡 支持的功能:")
        print(f"   1. ✅ 精确的image_ratio映射")
        print(f"   2. ✅ 支持width/height参数")
        print(f"   3. ✅ 支持sample_strength参数")
        print(f"   4. ✅ 支持negative_prompt参数")
        print(f"   5. ✅ 支持response_format参数")
        print(f"   6. ✅ 完全兼容drawing_skill.py调用格式")
    elif result1:
        print(f"\n⚠️  宽高比支持良好，drawing_skill格式需要优化")
    else:
        print(f"\n😤 还有问题需要继续调试")

if __name__ == "__main__":
    main()
