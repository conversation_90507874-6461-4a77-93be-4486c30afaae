#!/bin/bash

# 🔥 林嫣然数字生命体系统 - 生产环境专用启动脚本
# 版本: v4.0.0-production
# 老王出品，专为生产环境优化

set -euo pipefail

# ==================== 生产环境配置 ====================

PROJECT_NAME="yanran-digital-life"
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MAIN_SCRIPT="main.py"

# 🔥 生产环境固定配置
ENVIRONMENT="production"
LOG_LEVEL="${LOG_LEVEL:-INFO}"
API_HOST="${API_HOST:-127.0.0.1}"
API_PORT="${API_PORT:-56839}"

# 🔥 生产环境路径
PID_FILE="$PROJECT_DIR/yanran_production.pid"
LOG_DIR="$PROJECT_DIR/logs"
CONFIG_DIR="$PROJECT_DIR/config"

# 🔥 老王修复：日志文件路径优化
MAIN_LOG_FILE="$LOG_DIR/yanran_production.log"
ERROR_LOG_FILE="$LOG_DIR/yanran_production_error.log"

# 🔥 Conda环境
CONDA_ENV_NAME="${CONDA_ENV_NAME:-linyanran}"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# ==================== 工具函数 ====================

log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" >&2
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 显示生产环境横幅
show_production_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              🌸   嫣然数字生命   -  生产环境 🌸              ║"
    echo "║                     Production Environment                   ║"
    echo "║                        Version 4.0.0                         ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 🔥 老王修复：检查是否运行 - 更强的进程检查
is_running() {
    if [[ ! -f "$PID_FILE" ]]; then
        return 1
    fi

    local pid=$(cat "$PID_FILE" 2>/dev/null)
    if [[ -z "$pid" || ! "$pid" =~ ^[0-9]+$ ]]; then
        return 1
    fi

    # 检查进程是否存在
    if ! kill -0 "$pid" 2>/dev/null; then
        return 1
    fi

    # 🔥 关键修复：检查进程是否真的是我们的Python进程
    local process_info=$(ps -p "$pid" -o comm= 2>/dev/null || echo "")
    if [[ "$process_info" != "python"* && "$process_info" != "Python"* ]]; then
        log_warn "PID文件中的进程不是Python进程，清理PID文件"
        rm -f "$PID_FILE"
        return 1
    fi

    return 0
}

# 🔥 老王修复：获取PID - 增强验证
get_pid() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE" 2>/dev/null)
        if [[ -n "$pid" && "$pid" =~ ^[0-9]+$ ]]; then
            echo "$pid"
        else
            echo ""
        fi
    else
        echo ""
    fi
}

# 🔥 老王修复：强制清理所有相关进程 - 增强版
force_kill_all() {
    log_info "强制清理所有相关进程..."

    # 🔥 方法1：使用pgrep查找Python进程
    local pids=$(pgrep -f "python.*main.py" 2>/dev/null || echo "")
    if [[ -n "$pids" ]]; then
        log_info "pgrep发现相关进程: $pids"
        for pid in $pids; do
            log_info "强制停止进程: $pid ($(ps -p "$pid" -o comm= 2>/dev/null || echo 'unknown'))"
            kill -KILL "$pid" 2>/dev/null || true
        done
    else
        log_info "pgrep未发现相关进程"
    fi

    # 🔥 方法2：使用pkill直接杀死
    log_info "使用pkill清理Python进程..."
    pkill -KILL -f "python.*main.py" 2>/dev/null || true
    pkill -KILL -f "yanran.*digital.*life" 2>/dev/null || true

    # 🔥 方法3：如果PID文件存在，直接杀死该进程
    if [[ -f "$PID_FILE" ]]; then
        local file_pid=$(cat "$PID_FILE" 2>/dev/null)
        if [[ -n "$file_pid" && "$file_pid" =~ ^[0-9]+$ ]]; then
            log_info "从PID文件杀死进程: $file_pid"
            kill -KILL "$file_pid" 2>/dev/null || true
        fi
    fi

    # 清理PID文件
    rm -f "$PID_FILE"

    # 🔥 等待进程完全退出并验证
    log_info "等待进程完全退出..."
    sleep 3

    # 验证清理结果
    local remaining_pids=$(pgrep -f "python.*main.py" 2>/dev/null || echo "")
    if [[ -n "$remaining_pids" ]]; then
        log_error "仍有进程残留: $remaining_pids"
        log_error "请手动执行: kill -KILL $remaining_pids"
    else
        log_success "所有相关进程已清理"
    fi
}

# 设置conda环境
setup_conda_environment() {
    log_info "设置生产环境Conda环境..."
    
    if ! command -v conda &> /dev/null; then
        log_error "Conda未安装"
        return 1
    fi
    
    eval "$(conda shell.bash hook)" 2>/dev/null || {
        local conda_base=$(conda info --linyanran 2>/dev/null)
        if [[ -n "$conda_base" && -f "$conda_base/etc/profile.d/conda.sh" ]]; then
            source "$conda_base/etc/profile.d/conda.sh"
        else
            log_error "无法初始化conda"
            return 1
        fi
    }
    
    local current_env="${CONDA_DEFAULT_ENV:-linyanran}"
    if [[ "$current_env" != "$CONDA_ENV_NAME" ]]; then
        conda activate "$CONDA_ENV_NAME" || {
            log_error "无法激活环境: $CONDA_ENV_NAME"
            return 1
        }
    fi
    
    log_success "Conda环境已就绪: $CONDA_ENV_NAME"
    return 0
}

# 🔥 生产环境优化设置
setup_production_environment() {
    log_info "应用生产环境优化设置..."
    
    # Python优化
    export PYTHONOPTIMIZE=1                    # 启用Python优化
    export PYTHONDONTWRITEBYTECODE=1           # 禁用.pyc文件生成
    export PYTHONUNBUFFERED=1                  # 禁用输出缓冲
    export PYTHONRECURSIONLIMIT=3000           # 增加递归限制
    export PYTHONHASHSEED=0                    # 固定哈希种子
    
    # MySQL优化
    export MYSQL_OPT_MAX_ALLOWED_PACKET=16777216
    export MYSQL_OPT_CONNECT_TIMEOUT=10
    export MYSQL_OPT_READ_TIMEOUT=30
    export MYSQL_OPT_WRITE_TIMEOUT=30
    export MYSQL_OPT_RECONNECT=1
    
    # 系统环境
    export PYTHONPATH="$PROJECT_DIR:${PYTHONPATH:-}"
    export YANRAN_CONFIG_DIR="$CONFIG_DIR"
    export YANRAN_ENV="production"
    
    # 🔥 禁用不必要的功能
    export YANRAN_DISABLE_CLI=1                # 禁用CLI
    export YANRAN_DISABLE_DEBUG=1              # 禁用调试
    export YANRAN_DISABLE_WEBSOCKET=1          # 禁用WebSocket（使用WeChat推送）
    
    log_success "生产环境优化设置完成"
}

# 🔥 老王修复：创建必要目录和日志轮转配置
create_directories() {
    mkdir -p "$LOG_DIR" "$CONFIG_DIR"

    # 🔥 创建logrotate配置，解决日志轮转问题
    create_logrotate_config

    log_info "目录结构已就绪"
}

# 🔥 老王新增：创建logrotate配置，解决@^@^@填充问题
create_logrotate_config() {
    local logrotate_config="/etc/logrotate.d/yanran-digital-life"

    # 如果有root权限，创建系统级logrotate配置
    if [[ $EUID -eq 0 ]]; then
        cat > "$logrotate_config" << EOF
$MAIN_LOG_FILE $ERROR_LOG_FILE {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 $(whoami) $(whoami)
    copytruncate
    postrotate
        # 发送USR1信号让程序重新打开日志文件
        if [ -f $PID_FILE ]; then
            kill -USR1 \$(cat $PID_FILE) 2>/dev/null || true
        fi
    endscript
}
EOF
        log_info "系统级logrotate配置已创建: $logrotate_config"
    else
        # 创建用户级logrotate配置
        local user_logrotate_config="$PROJECT_DIR/logrotate.conf"
        cat > "$user_logrotate_config" << EOF
$MAIN_LOG_FILE $ERROR_LOG_FILE {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    copytruncate
}
EOF
        log_info "用户级logrotate配置已创建: $user_logrotate_config"
        log_info "请手动执行: logrotate -f $user_logrotate_config"
    fi
}

# 🔥 生产环境健康检查
production_health_check() {
    log_info "执行生产环境健康检查..."
    
    local max_attempts=30000
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -s "http://$API_HOST:$API_PORT/api/health" >/dev/null 2>&1; then
            log_success "API健康检查通过"
            
            # 检查关键服务
            local status_response=$(curl -s "http://$API_HOST:$API_PORT/api/status" 2>/dev/null || echo "")
            if [[ -n "$status_response" ]]; then
                log_success "系统状态检查通过"
                return 0
            fi
        fi
        
        log_info "健康检查 $attempt/$max_attempts..."
        sleep 2
        ((attempt++))
    done
    
    log_error "生产环境健康检查失败"
    return 1
}

# ==================== 主要功能 ====================

# 🔥 生产环境启动
start_production() {
    show_production_banner
    log_info "启动林嫣然数字生命体系统 - 生产环境模式"
    
    if is_running; then
        local pid=$(get_pid)
        log_warn "服务已在运行 (PID: $pid)"
        return 0
    fi
    
    # 设置环境
    if ! setup_conda_environment; then
        log_error "Conda环境设置失败"
        return 1
    fi
    
    setup_production_environment
    create_directories
    
    cd "$PROJECT_DIR"
    
    # 🔥 生产环境专用启动命令
    local start_cmd="python $MAIN_SCRIPT"
    start_cmd+=" --log-level $LOG_LEVEL"
    start_cmd+=" --api-host $API_HOST"
    start_cmd+=" --api-port $API_PORT"
    start_cmd+=" --api-only"                    # 🔥 关键：纯API模式
    start_cmd+=" --config-dir $CONFIG_DIR"
    
    log_info "启动命令: $start_cmd"
    log_info "🔥 使用API-Only模式，避免CLI交互问题"

    # 🔥 老王修复：优化日志重定向，解决@^@^@填充问题
    # 使用exec重定向而不是nohup，避免二进制填充
    {
        exec $start_cmd
    } > "$MAIN_LOG_FILE" 2> "$ERROR_LOG_FILE" &

    local pid=$!
    echo $pid > "$PID_FILE"

    # 🔥 确保PID文件写入成功
    if [[ ! -f "$PID_FILE" ]] || [[ "$(cat "$PID_FILE" 2>/dev/null)" != "$pid" ]]; then
        log_error "PID文件写入失败"
        kill -KILL "$pid" 2>/dev/null || true
        return 1
    fi
    
    log_info "服务已启动 (PID: $pid)"
    
    # 健康检查
    if production_health_check; then
        log_success "🌸 林嫣然数字生命体系统生产环境启动成功！"
        log_info "API地址: http://$API_HOST:$API_PORT"
        log_info "健康检查: http://$API_HOST:$API_PORT/api/health"
        log_info "系统状态: http://$API_HOST:$API_PORT/api/status"
    else
        log_error "生产环境启动失败"
        stop_production
        return 1
    fi
}

# 🔥 老王修复：停止生产环境 - 超强停止逻辑，增加详细调试
stop_production() {
    log_info "停止生产环境服务..."

    if ! is_running; then
        log_warn "服务未运行"
        # 🔥 即使服务未运行，也要清理可能存在的僵尸进程
        force_kill_all
        return 0
    fi

    local pid=$(get_pid)
    if [[ -z "$pid" ]]; then
        log_warn "无法获取PID，执行强制清理"
        force_kill_all
        return 0
    fi

    log_info "停止进程 $pid"

    # 🔥 显示进程信息
    log_info "进程信息: $(ps -p "$pid" -o pid,ppid,comm,args 2>/dev/null || echo '进程不存在')"

    # 🔥 第1步：优雅停止 (SIGTERM) - 缩短等待时间
    log_info "发送SIGTERM信号..."
    if kill -TERM "$pid" 2>/dev/null; then
        log_info "SIGTERM信号发送成功"
    else
        log_warn "SIGTERM信号发送失败，进程可能已不存在"
    fi

    local count=0
    while [[ $count -lt 5 ]]; do  # 🔥 从15秒改为5秒
        log_info "等待优雅停止... ($((count+1))/5)"

        if ! kill -0 "$pid" 2>/dev/null; then
            log_success "进程已停止 (kill -0 检查)"
            rm -f "$PID_FILE"
            log_success "生产环境服务已优雅停止"
            return 0
        fi

        if ! is_running; then
            log_success "进程已停止 (is_running 检查)"
            rm -f "$PID_FILE"
            log_success "生产环境服务已优雅停止"
            return 0
        fi

        sleep 1
        ((count++))
    done

    # 🔥 第2步：强制停止 (SIGKILL) - 缩短等待时间
    log_warn "优雅停止超时，发送SIGKILL信号..."
    if kill -KILL "$pid" 2>/dev/null; then
        log_info "SIGKILL信号发送成功"
    else
        log_warn "SIGKILL信号发送失败"
    fi

    count=0
    while [[ $count -lt 3 ]]; do  # 🔥 从10秒改为3秒
        log_info "等待强制停止... ($((count+1))/3)"

        if ! kill -0 "$pid" 2>/dev/null; then
            log_success "进程已被强制停止"
            rm -f "$PID_FILE"
            log_success "生产环境服务已强制停止"
            return 0
        fi

        sleep 1
        ((count++))
    done

    # 🔥 第3步：终极清理
    log_error "进程 $pid 仍然顽强存在，执行终极清理..."
    log_info "当前进程状态: $(ps -p "$pid" 2>/dev/null || echo '进程不存在')"
    force_kill_all
    log_success "服务已终极停止"
}

# 🔥 老王新增：快速停止 - 直接使用SIGKILL
quick_stop() {
    log_info "快速停止生产环境服务（直接SIGKILL）..."

    local pid=$(get_pid)
    if [[ -n "$pid" ]]; then
        log_info "直接强制停止进程: $pid"
        kill -KILL "$pid" 2>/dev/null || true
        sleep 1

        if ! kill -0 "$pid" 2>/dev/null; then
            rm -f "$PID_FILE"
            log_success "快速停止成功"
            return 0
        fi
    fi

    # 如果单个PID停止失败，执行全面清理
    force_kill_all
}

# 重启生产环境
restart_production() {
    log_info "重启生产环境服务..."
    stop_production
    sleep 3
    start_production
}

# 查看生产环境状态
status_production() {
    echo -e "${CYAN}🌸 生产环境状态 🌸${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    if is_running; then
        local pid=$(get_pid)
        echo -e "📊 服务状态: ${GREEN}运行中${NC} (PID: $pid)"
        echo -e "🌐 API地址: http://$API_HOST:$API_PORT"
        echo -e "🐍 Python环境: $CONDA_ENV_NAME"
        echo -e "📁 项目目录: $PROJECT_DIR"
        echo -e "📝 日志目录: $LOG_DIR"
        echo -e "⚙️  运行模式: ${GREEN}API-Only (生产环境)${NC}"
    else
        echo -e "📊 服务状态: ${RED}已停止${NC}"
    fi
    
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
}

# 🔥 老王修复：查看生产环境日志 - 使用新的日志文件路径
logs_production() {
    local lines=${1:-50}

    echo -e "${CYAN}🌸 生产环境日志 (最近 $lines 行) 🌸${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

    # 🔥 使用新的日志文件路径
    if [[ -f "$MAIN_LOG_FILE" ]]; then
        echo -e "${GREEN}📤 主日志 ($MAIN_LOG_FILE):${NC}"
        # 🔥 使用cat而不是tail，避免二进制字符显示问题
        if command -v strings >/dev/null 2>&1; then
            strings "$MAIN_LOG_FILE" | tail -n $lines
        else
            tail -n $lines "$MAIN_LOG_FILE" | tr -d '\000-\010\013\014\016-\037'
        fi
        echo
    fi

    if [[ -f "$ERROR_LOG_FILE" ]]; then
        echo -e "${RED}📥 错误日志 ($ERROR_LOG_FILE):${NC}"
        if command -v strings >/dev/null 2>&1; then
            strings "$ERROR_LOG_FILE" | tail -n $lines
        else
            tail -n $lines "$ERROR_LOG_FILE" | tr -d '\000-\010\013\014\016-\037'
        fi
        echo
    fi

    # 🔥 兼容旧日志文件
    local old_out_log="$LOG_DIR/yanran_production.out"
    local old_err_log="$LOG_DIR/yanran_production.err"

    if [[ -f "$old_out_log" ]]; then
        echo -e "${YELLOW}📤 旧输出日志 ($old_out_log):${NC}"
        if command -v strings >/dev/null 2>&1; then
            strings "$old_out_log" | tail -n $lines
        else
            tail -n $lines "$old_out_log" | tr -d '\000-\010\013\014\016-\037'
        fi
        echo
    fi

    if [[ -f "$old_err_log" ]]; then
        echo -e "${YELLOW}📥 旧错误日志 ($old_err_log):${NC}"
        if command -v strings >/dev/null 2>&1; then
            strings "$old_err_log" | tail -n $lines
        else
            tail -n $lines "$old_err_log" | tr -d '\000-\010\013\014\016-\037'
        fi
    fi
}

# 🔥 老王新增：清理日志中的二进制字符
clean_logs() {
    log_info "清理日志文件中的二进制字符..."

    local cleaned=0

    # 清理主日志文件
    if [[ -f "$MAIN_LOG_FILE" ]]; then
        log_info "清理主日志文件: $MAIN_LOG_FILE"
        if command -v strings >/dev/null 2>&1; then
            strings "$MAIN_LOG_FILE" > "$MAIN_LOG_FILE.clean"
            mv "$MAIN_LOG_FILE.clean" "$MAIN_LOG_FILE"
        else
            tr -d '\000-\010\013\014\016-\037' < "$MAIN_LOG_FILE" > "$MAIN_LOG_FILE.clean"
            mv "$MAIN_LOG_FILE.clean" "$MAIN_LOG_FILE"
        fi
        ((cleaned++))
    fi

    # 清理错误日志文件
    if [[ -f "$ERROR_LOG_FILE" ]]; then
        log_info "清理错误日志文件: $ERROR_LOG_FILE"
        if command -v strings >/dev/null 2>&1; then
            strings "$ERROR_LOG_FILE" > "$ERROR_LOG_FILE.clean"
            mv "$ERROR_LOG_FILE.clean" "$ERROR_LOG_FILE"
        else
            tr -d '\000-\010\013\014\016-\037' < "$ERROR_LOG_FILE" > "$ERROR_LOG_FILE.clean"
            mv "$ERROR_LOG_FILE.clean" "$ERROR_LOG_FILE"
        fi
        ((cleaned++))
    fi

    # 清理旧日志文件
    for old_log in "$LOG_DIR/yanran_production.out" "$LOG_DIR/yanran_production.err"; do
        if [[ -f "$old_log" ]]; then
            log_info "清理旧日志文件: $old_log"
            if command -v strings >/dev/null 2>&1; then
                strings "$old_log" > "$old_log.clean"
                mv "$old_log.clean" "$old_log"
            else
                tr -d '\000-\010\013\014\016-\037' < "$old_log" > "$old_log.clean"
                mv "$old_log.clean" "$old_log"
            fi
            ((cleaned++))
        fi
    done

    if [[ $cleaned -gt 0 ]]; then
        log_success "已清理 $cleaned 个日志文件中的二进制字符"
    else
        log_info "没有找到需要清理的日志文件"
    fi
}

# ==================== 主程序 ====================

case "${1:-}" in
    start)
        start_production
        ;;
    stop)
        stop_production
        ;;
    restart)
        restart_production
        ;;
    status)
        status_production
        ;;
    logs)
        logs_production "${2:-50}"
        ;;
    clean-logs)
        clean_logs
        ;;
    force-stop)
        log_warn "执行强制停止..."
        force_kill_all
        ;;
    quick-stop)
        quick_stop
        ;;
    kill)
        # 🔥 老王新增：终极杀死命令
        log_error "执行终极杀死命令..."
        pkill -KILL -f "python.*main.py" || true
        pkill -KILL -f "yanran" || true
        rm -f "$PID_FILE"
        log_success "终极杀死完成"
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs [行数]|clean-logs|force-stop|quick-stop|kill}"
        echo
        echo "🔥 生产环境专用脚本 - 使用API-Only模式"
        echo "   start      - 启动生产环境服务"
        echo "   stop       - 停止生产环境服务（优雅停止）"
        echo "   quick-stop - 快速停止服务（直接SIGKILL）"
        echo "   force-stop - 强制停止所有相关进程"
        echo "   kill       - 终极杀死命令（pkill）"
        echo "   restart    - 重启生产环境服务"
        echo "   status     - 查看服务状态"
        echo "   logs       - 查看日志"
        echo "   clean-logs - 清理日志中的二进制字符"
        exit 1
        ;;
esac 