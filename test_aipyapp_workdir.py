#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试aipyapp工作目录和文件路径
"""

import requests
import json
import time
from urllib.parse import urljoin


def test_workdir():
    """测试aipyapp的工作目录"""
    print("🔍 测试aipyapp工作目录...")
    
    api_base_url = "http://127.0.0.1:8848"
    
    # 提交一个简单的文件生成任务
    task_instruction = """
    请执行以下Python代码来检查工作目录和文件路径：
    
    import os
    import pandas as pd
    import matplotlib.pyplot as plt
    
    # 获取当前工作目录
    current_dir = os.getcwd()
    print(f"当前工作目录: {current_dir}")
    
    # 获取绝对路径
    abs_path = os.path.abspath(".")
    print(f"绝对路径: {abs_path}")
    
    # 创建一个测试文件
    test_data = pd.DataFrame({
        'x': [1, 2, 3, 4, 5],
        'y': [2, 4, 6, 8, 10]
    })
    
    # 保存CSV文件
    csv_file = "test_workdir.csv"
    test_data.to_csv(csv_file, index=False)
    csv_abs_path = os.path.abspath(csv_file)
    print(f"CSV文件保存到: {csv_abs_path}")
    
    # 生成图表
    plt.figure(figsize=(8, 6))
    plt.plot(test_data['x'], test_data['y'], 'bo-')
    plt.title('Test Chart')
    plt.xlabel('X')
    plt.ylabel('Y')
    
    # 保存图片文件
    img_file = "test_workdir.png"
    plt.savefig(img_file, dpi=300, bbox_inches='tight')
    img_abs_path = os.path.abspath(img_file)
    print(f"图片文件保存到: {img_abs_path}")
    
    # 列出当前目录的文件
    files = os.listdir(".")
    print(f"当前目录文件列表: {files}")
    
    # 检查文件是否存在
    print(f"CSV文件存在: {os.path.exists(csv_file)}")
    print(f"图片文件存在: {os.path.exists(img_file)}")
    
    print("工作目录检查完成！")
    """
    
    try:
        # 提交任务
        api_url = urljoin(api_base_url, "/tasks")
        payload = {
            "instruction": task_instruction,
            "metadata": {
                "test_type": "workdir_check",
                "timestamp": time.time()
            }
        }
        
        print("📤 提交工作目录检查任务...")
        response = requests.post(api_url, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get("task_id")
            print(f"✅ 任务提交成功，任务ID: {task_id}")
            
            # 轮询任务状态
            max_polls = 60
            poll_interval = 3
            
            for i in range(max_polls):
                status_url = urljoin(api_base_url, f"/tasks/{task_id}")
                status_response = requests.get(status_url, timeout=30)
                
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    status = status_data.get("status")
                    
                    print(f"📊 轮询 {i+1}/{max_polls}: 状态 = {status}")
                    
                    if status == "completed":
                        # 获取结果
                        result_url = urljoin(api_base_url, f"/tasks/{task_id}/result")
                        result_response = requests.get(result_url, timeout=30)
                        
                        if result_response.status_code == 200:
                            result_data = result_response.json()
                            
                            print("\n🎉 任务执行完成！")
                            print("=" * 60)
                            
                            # 提取输出信息
                            output = result_data.get("output", {})
                            results = output.get("results", [])
                            
                            for result in results:
                                if isinstance(result, dict):
                                    result_info = result.get("result", {})
                                    if isinstance(result_info, dict):
                                        stdout = result_info.get("stdout", "")
                                        if stdout:
                                            print("📋 执行输出:")
                                            print(stdout)
                            
                            print("=" * 60)
                            return True
                        else:
                            print(f"❌ 获取结果失败: {result_response.status_code}")
                            return False
                    elif status == "failed":
                        error = status_data.get("error", "任务执行失败")
                        print(f"❌ 任务执行失败: {error}")
                        return False
                    elif status in ["pending", "running"]:
                        time.sleep(poll_interval)
                        continue
                else:
                    print(f"⚠️ 状态查询失败: {status_response.status_code}")
                    time.sleep(poll_interval)
            
            print("⏰ 任务执行超时")
            return False
        else:
            print(f"❌ 任务提交失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False


if __name__ == "__main__":
    print("🔍 aipyapp工作目录检查程序")
    print("=" * 60)
    
    success = test_workdir()
    
    if success:
        print("\n✅ 工作目录检查完成！")
        print("现在我们知道了aipyapp的工作目录和文件保存位置。")
    else:
        print("\n❌ 工作目录检查失败！")
        print("请确认aipyapp服务正常运行。")
