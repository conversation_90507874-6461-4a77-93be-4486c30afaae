#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实ChromaDB查询测试
测试用户liu_defei_cool查询"你好"的结果

作者: 老王
创建日期: 2025-09-18
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utilities.unified_logger import get_unified_logger, setup_unified_logging

# 设置日志
setup_unified_logging()
logger = get_unified_logger(__name__)

def test_real_chromadb_query():
    """真实测试ChromaDB查询"""
    logger.info("🧪 开始真实ChromaDB查询测试...")
    
    try:
        from utilities.legacy_chroma_bridge import search_history
        
        # 测试参数
        user_id = "liu_defei_cool"
        query_text = "你好"
        n_results = 5
        
        logger.info(f"🔍 测试参数:")
        logger.info(f"   用户ID: {user_id}")
        logger.info(f"   查询内容: {query_text}")
        logger.info(f"   结果数量: {n_results}")
        
        # 测试1: 查询用户消息
        logger.info("=" * 50)
        logger.info("📝 测试查询用户消息 (role='user')...")
        user_result = search_history(user_id, query_text, n_results, "user")
        
        logger.info(f"用户消息查询结果类型: {type(user_result)}")
        if isinstance(user_result, dict):
            logger.info(f"结果字段: {list(user_result.keys())}")
            
            if "documents" in user_result:
                docs = user_result["documents"]
                logger.info(f"documents类型: {type(docs)}")
                if docs and len(docs) > 0:
                    logger.info(f"documents[0]类型: {type(docs[0])}")
                    logger.info(f"documents[0]长度: {len(docs[0])}")
                    
                    # 显示实际内容
                    for i, doc in enumerate(docs[0][:3]):  # 只显示前3个
                        logger.info(f"   文档{i+1}: {doc}")
                else:
                    logger.info("documents为空或None")
            
            if "metadatas" in user_result:
                metas = user_result["metadatas"]
                logger.info(f"metadatas类型: {type(metas)}")
                if metas and len(metas) > 0 and metas[0]:
                    logger.info(f"metadatas[0]长度: {len(metas[0])}")
                    # 显示前3个元数据
                    for i, meta in enumerate(metas[0][:3]):
                        logger.info(f"   元数据{i+1}: {meta}")
            
            if "distances" in user_result:
                dists = user_result["distances"]
                if dists and len(dists) > 0 and dists[0]:
                    logger.info(f"distances: {dists[0][:3]}")  # 只显示前3个距离
        
        # 测试2: 查询助手消息
        logger.info("=" * 50)
        logger.info("🤖 测试查询助手消息 (role='assistant')...")
        assistant_result = search_history(user_id, query_text, n_results, "assistant")
        
        logger.info(f"助手消息查询结果类型: {type(assistant_result)}")
        if isinstance(assistant_result, dict):
            logger.info(f"结果字段: {list(assistant_result.keys())}")
            
            if "documents" in assistant_result:
                docs = assistant_result["documents"]
                if docs and len(docs) > 0 and docs[0]:
                    logger.info(f"助手消息数量: {len(docs[0])}")
                    # 显示实际内容
                    for i, doc in enumerate(docs[0][:3]):  # 只显示前3个
                        logger.info(f"   助手回复{i+1}: {doc}")
                else:
                    logger.info("助手消息为空")
        
        # 测试3: 无角色查询
        logger.info("=" * 50)
        logger.info("🔍 测试无角色查询 (role=None)...")
        no_role_result = search_history(user_id, query_text, n_results, None)
        
        logger.info(f"无角色查询结果类型: {type(no_role_result)}")
        if isinstance(no_role_result, dict):
            if "documents" in no_role_result:
                docs = no_role_result["documents"]
                if docs and len(docs) > 0 and docs[0]:
                    logger.info(f"无角色消息数量: {len(docs[0])}")
                    # 显示实际内容
                    for i, doc in enumerate(docs[0][:3]):  # 只显示前3个
                        logger.info(f"   消息{i+1}: {doc}")
                else:
                    logger.info("无角色消息为空")
        
        logger.success("🎉 真实ChromaDB查询测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 真实ChromaDB查询测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_original_where_format():
    """测试原始的where格式（修改前的格式）"""
    logger.info("🧪 测试原始where格式...")
    
    try:
        from utilities.legacy_chroma_bridge import query_collection, get_collection_id, get_embedding
        
        user_id = "liu_defei_cool"
        query_text = "你好"
        
        # 获取collection_id
        collection_id = get_collection_id(user_id)
        if not collection_id:
            logger.warning("未找到用户集合")
            return False
        
        logger.info(f"找到集合ID: {collection_id}")
        
        # 获取嵌入向量
        query_embedding = get_embedding(query_text)
        if not query_embedding:
            logger.warning("无法获取嵌入向量")
            return False
        
        logger.info(f"获取嵌入向量成功，维度: {len(query_embedding)}")
        
        # 测试原始格式（只有role）
        logger.info("测试原始格式查询（只有role）...")
        result1 = query_collection(collection_id, query_embedding, 3, "user", None)
        
        logger.info(f"原始格式查询结果: {type(result1)}")
        if isinstance(result1, dict) and "documents" in result1:
            docs = result1["documents"]
            if docs and docs[0]:
                logger.info(f"原始格式找到 {len(docs[0])} 个文档")
                for i, doc in enumerate(docs[0]):
                    logger.info(f"   原始格式文档{i+1}: {doc}")
        
        # 测试新格式（role + user_id）
        logger.info("测试新格式查询（role + user_id）...")
        result2 = query_collection(collection_id, query_embedding, 3, "user", user_id)
        
        logger.info(f"新格式查询结果: {type(result2)}")
        if isinstance(result2, dict) and "documents" in result2:
            docs = result2["documents"]
            if docs and docs[0]:
                logger.info(f"新格式找到 {len(docs[0])} 个文档")
                for i, doc in enumerate(docs[0]):
                    logger.info(f"   新格式文档{i+1}: {doc}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 原始where格式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("🚀 真实ChromaDB查询测试开始")
    logger.info("=" * 60)
    
    # 测试1: 真实查询
    test1_result = test_real_chromadb_query()
    
    logger.info("-" * 60)
    
    # 测试2: 原始格式对比
    test2_result = test_original_where_format()
    
    logger.info("=" * 60)
    
    # 总结
    if test1_result and test2_result:
        logger.success("🎉 真实ChromaDB查询测试全部通过！")
        return True
    else:
        logger.error("❌ 真实ChromaDB查询测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
