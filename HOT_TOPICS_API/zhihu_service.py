import httpx
import asyncio
from typing import Any, Dict, List, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class ZhihuService:
    """知乎热榜服务 - 直接集成DailyHotApi的知乎逻辑"""
    
    def __init__(self):
        self.base_url = "https://api.zhihu.com/topstory/hot-lists/total"
        self.timeout = httpx.Timeout(10.0, connect=3.0)  # 缩短超时时间
        # 备用API地址
        self.fallback_urls = [
            "https://www.zhihu.com/api/v3/feed/topstory/hot-lists/total",
            "https://api.zhihu.com/topstory/hot-lists/total"
        ]
        
    async def get_hot_topics(self, limit: int = 50, zhihu_cookie: Optional[str] = None) -> Dict[str, Any]:
        """
        获取知乎热榜数据
        
        Args:
            limit: 获取条数，默认50
            zhihu_cookie: 知乎Cookie，可选
            
        Returns:
            Dict[str, Any]: 知乎热榜数据
        """
        # 构建请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site'
        }
        
        if zhihu_cookie:
            headers['Cookie'] = zhihu_cookie
        
        # 尝试多个API地址
        last_error = None
        for base_url in self.fallback_urls:
            url = f"{base_url}?limit={limit}"
            if base_url == "https://www.zhihu.com/api/v3/feed/topstory/hot-lists/total":
                url += "&desktop=true"  # 旧版API需要desktop参数
            
            try:
                async with httpx.AsyncClient(
                    timeout=self.timeout,
                    verify=False,
                    follow_redirects=True,
                    http2=False  # 禁用HTTP/2
                ) as client:
                    logger.info(f"🌐 [GET] {url}")
                    response = await client.get(url, headers=headers)
                    response.raise_for_status()
                
                    data = response.json()
                    
                    # 检查响应数据结构
                    if not isinstance(data, dict) or 'data' not in data:
                        logger.error("❌ [ERROR] Invalid response structure from Zhihu API")
                        last_error = "Invalid response structure"
                        continue
                    
                    # 处理数据成功，跳出循环
                    break
                    
            except httpx.HTTPStatusError as e:
                logger.error(f"❌ [HTTP ERROR] {url}: {e.response.status_code}")
                last_error = f"HTTP error: {e.response.status_code}"
                continue
            except httpx.RequestError as e:
                logger.error(f"❌ [REQUEST ERROR] {url}: {e}")
                last_error = f"Network error: {str(e)}"
                continue
            except Exception as e:
                logger.error(f"❌ [UNEXPECTED ERROR] {url}: {e}")
                last_error = f"Unexpected error: {str(e)}"
                continue
        
        # 如果所有URL都失败了
        if 'data' not in locals():
            return {
                "error": "All API endpoints failed",
                "details": f"所有知乎API地址都无法访问: {last_error}"
            }
        
        try:
                
                # 处理数据
                hot_list = data['data']
                processed_data = []
                
                for item in hot_list:
                    try:
                        target = item.get('target', {})
                        question_id = target.get('url', '').split('/')[-1] if target.get('url') else ''
                        
                        # 获取封面图片
                        cover = ''
                        if 'children' in item and len(item['children']) > 0:
                            cover = item['children'][0].get('thumbnail', '')
                        
                        # 解析热度值
                        hot_value = 0
                        detail_text = item.get('detail_text', '')
                        if detail_text and ' ' in detail_text:
                            try:
                                hot_str = detail_text.split(' ')[0]
                                hot_value = float(hot_str) * 10000
                            except (ValueError, IndexError):
                                hot_value = 0
                        
                        processed_item = {
                            "id": target.get('id', ''),
                            "title": target.get('title', ''),
                            "desc": target.get('excerpt', ''),
                            "cover": cover,
                            "timestamp": self._get_timestamp(target.get('created', 0)),
                            "hot": hot_value,
                            "url": f"https://www.zhihu.com/question/{question_id}" if question_id else '',
                            "mobileUrl": f"https://www.zhihu.com/question/{question_id}" if question_id else ''
                        }
                        
                        processed_data.append(processed_item)
                        
                    except Exception as e:
                        logger.warning(f"⚠️ [WARNING] Error processing item: {e}")
                        continue
                
                # 构建返回数据
                result = {
                    "code": 200,
                    "name": "zhihu",
                    "title": "知乎",
                    "type": "热榜",
                    "link": "https://www.zhihu.com/hot",
                    "total": len(processed_data),
                    "updateTime": datetime.now().isoformat(),
                    "data": processed_data,
                    "fromCache": False
                }
                
                logger.info(f"✅ [SUCCESS] Successfully fetched {len(processed_data)} items from Zhihu")
                return result
            
        except Exception as e:
            logger.error(f"❌ [DATA PROCESSING ERROR] {e}")
            return {
                "error": "Data processing error",
                "details": f"数据处理失败: {str(e)}"
            }
    
    def _get_timestamp(self, created_time: int) -> str:
        """
        将时间戳转换为ISO格式字符串
        
        Args:
            created_time: Unix时间戳
            
        Returns:
            str: ISO格式时间字符串
        """
        try:
            if created_time:
                return datetime.fromtimestamp(created_time).isoformat()
            else:
                return datetime.now().isoformat()
        except Exception:
            return datetime.now().isoformat()

# 创建服务实例
zhihu_service = ZhihuService()