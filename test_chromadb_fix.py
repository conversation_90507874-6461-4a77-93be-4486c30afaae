#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ChromaDB where子句语法修复验证测试

作者: 老王
创建日期: 2025-09-18
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utilities.unified_logger import get_unified_logger, setup_unified_logging

# 设置日志
setup_unified_logging()
logger = get_unified_logger(__name__)

def test_chromadb_where_clause():
    """测试ChromaDB where子句语法修复"""
    logger.info("🧪 开始ChromaDB where子句语法修复验证测试...")
    
    try:
        from utilities.legacy_chroma_bridge import search_history
        
        # 测试用户ID
        test_user_id = "liu_defei_cool"
        test_query = "测试查询"
        
        logger.info(f"🔍 测试用户ID: {test_user_id}")
        logger.info(f"🔍 测试查询: {test_query}")
        
        # 测试1: 用户消息查询
        logger.info("📝 测试用户消息查询...")
        user_result = search_history(test_user_id, test_query, 3, "user")
        
        if isinstance(user_result, dict):
            logger.success("✅ 用户消息查询成功，返回字典格式")
            if "documents" in user_result:
                docs = user_result["documents"]
                if docs and docs[0]:
                    logger.info(f"   找到 {len(docs[0])} 个用户消息")
                else:
                    logger.info("   没有找到用户消息（可能是空集合）")
            else:
                logger.warning("   返回结果缺少documents字段")
        else:
            logger.error(f"❌ 用户消息查询返回非字典格式: {type(user_result)}")
            return False
        
        # 测试2: 助手消息查询
        logger.info("🤖 测试助手消息查询...")
        assistant_result = search_history(test_user_id, test_query, 3, "assistant")
        
        if isinstance(assistant_result, dict):
            logger.success("✅ 助手消息查询成功，返回字典格式")
            if "documents" in assistant_result:
                docs = assistant_result["documents"]
                if docs and docs[0]:
                    logger.info(f"   找到 {len(docs[0])} 个助手消息")
                else:
                    logger.info("   没有找到助手消息（可能是空集合）")
            else:
                logger.warning("   返回结果缺少documents字段")
        else:
            logger.error(f"❌ 助手消息查询返回非字典格式: {type(assistant_result)}")
            return False
        
        # 测试3: 无角色查询
        logger.info("🔍 测试无角色查询...")
        no_role_result = search_history(test_user_id, test_query, 3, None)
        
        if isinstance(no_role_result, dict):
            logger.success("✅ 无角色查询成功，返回字典格式")
        else:
            logger.error(f"❌ 无角色查询返回非字典格式: {type(no_role_result)}")
            return False
        
        logger.success("🎉 ChromaDB where子句语法修复验证测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ ChromaDB where子句语法修复验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_query_collection_directly():
    """直接测试query_collection函数"""
    logger.info("🧪 直接测试query_collection函数...")
    
    try:
        from utilities.legacy_chroma_bridge import query_collection
        
        # 模拟参数
        collection_id = "test_collection"
        query_embedding = [0.1] * 384  # 模拟嵌入向量
        n_results = 3
        role = "user"
        user_id = "liu_defei_cool"
        
        logger.info(f"🔍 测试参数: collection_id={collection_id}, role={role}, user_id={user_id}")
        
        # 测试1: 同时有role和user_id
        logger.info("📝 测试同时有role和user_id...")
        result1 = query_collection(collection_id, query_embedding, n_results, role, user_id)
        
        if isinstance(result1, dict):
            logger.success("✅ 同时有role和user_id的查询成功")
        else:
            logger.error(f"❌ 同时有role和user_id的查询失败: {type(result1)}")
        
        # 测试2: 只有role
        logger.info("🤖 测试只有role...")
        result2 = query_collection(collection_id, query_embedding, n_results, role, None)
        
        if isinstance(result2, dict):
            logger.success("✅ 只有role的查询成功")
        else:
            logger.error(f"❌ 只有role的查询失败: {type(result2)}")
        
        # 测试3: 只有user_id
        logger.info("👤 测试只有user_id...")
        result3 = query_collection(collection_id, query_embedding, n_results, None, user_id)
        
        if isinstance(result3, dict):
            logger.success("✅ 只有user_id的查询成功")
        else:
            logger.error(f"❌ 只有user_id的查询失败: {type(result3)}")
        
        # 测试4: 都没有
        logger.info("🔍 测试都没有...")
        result4 = query_collection(collection_id, query_embedding, n_results, None, None)
        
        if isinstance(result4, dict):
            logger.success("✅ 都没有的查询成功")
        else:
            logger.error(f"❌ 都没有的查询失败: {type(result4)}")
        
        logger.success("🎉 query_collection函数测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ query_collection函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("🚀 ChromaDB where子句语法修复验证测试开始")
    logger.info("=" * 60)
    
    # 测试1: search_history函数
    test1_result = test_chromadb_where_clause()
    
    logger.info("-" * 60)
    
    # 测试2: query_collection函数
    test2_result = test_query_collection_directly()
    
    logger.info("=" * 60)
    
    # 总结
    if test1_result and test2_result:
        logger.success("🎉 ChromaDB where子句语法修复验证测试全部通过！")
        logger.success("🔒 where子句语法正确")
        logger.success("📡 降级机制有效")
        return True
    else:
        logger.error("❌ ChromaDB where子句语法修复验证测试失败")
        logger.error(f"   search_history测试: {'通过' if test1_result else '失败'}")
        logger.error(f"   query_collection测试: {'通过' if test2_result else '失败'}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
