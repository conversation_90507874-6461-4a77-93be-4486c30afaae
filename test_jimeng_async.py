#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试 jimeng API 异步机制 - 🔥 老王专用
"""

import requests
import json
import time

def test_async_generation():
    """测试异步生成机制"""
    print("🔥 老王的 jimeng 异步测试")
    print("=" * 40)
    
    url = "http://**************:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "jimeng-4.0",
        "prompt": "一只橙色的小猫咪",
        "width": 512,
        "height": 512,
        "sample_strength": 0.5,
        "response_format": "url"
    }
    
    print(f"📝 提示词: {data['prompt']}")
    
    try:
        # 第一次请求 - 提交任务
        print(f"\n⏳ 提交生成任务...")
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            created_time = result.get("created")
            data_list = result.get("data", [])
            
            print(f"✅ 任务提交成功")
            print(f"   创建时间: {created_time}")
            print(f"   初始数据长度: {len(data_list)}")
            
            if data_list:
                print(f"🎉 立即获得结果！")
                for i, item in enumerate(data_list):
                    if isinstance(item, dict) and "url" in item:
                        print(f"   图片 {i+1}: {item['url']}")
                return True
            
            # 如果没有立即结果，开始轮询
            print(f"\n🔄 开始轮询等待结果...")
            
            max_polls = 30  # 最多轮询30次
            poll_interval = 10  # 每10秒轮询一次
            
            for poll_count in range(1, max_polls + 1):
                print(f"🔄 轮询 {poll_count}/{max_polls}...")
                time.sleep(poll_interval)
                
                try:
                    # 使用相同的请求参数轮询
                    poll_response = requests.post(url, headers=headers, json=data, timeout=30)
                    
                    if poll_response.status_code == 200:
                        poll_result = poll_response.json()
                        poll_data = poll_result.get("data", [])
                        poll_created = poll_result.get("created")
                        
                        print(f"   轮询结果: created={poll_created}, data长度={len(poll_data)}")
                        
                        if poll_data and len(poll_data) > 0:
                            print(f"🎉 轮询成功获得结果！")
                            for i, item in enumerate(poll_data):
                                if isinstance(item, dict) and "url" in item:
                                    print(f"   图片 {i+1}: {item['url']}")
                            return True
                        
                        # 检查是否是新任务（created时间不同）
                        if poll_created != created_time:
                            print(f"   ⚠️  获得新任务时间戳: {poll_created} (原: {created_time})")
                    else:
                        print(f"   ❌ 轮询失败: HTTP {poll_response.status_code}")
                        
                except Exception as poll_e:
                    print(f"   ❌ 轮询异常: {poll_e}")
            
            print(f"❌ 轮询超时，未获得结果")
            return False
        else:
            print(f"❌ 任务提交失败: HTTP {response.status_code}")
            print(f"   响应: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_multiple_requests():
    """测试多个请求是否会有不同结果"""
    print(f"\n🧪 测试多个连续请求")
    print("=" * 40)
    
    url = "http://**************:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    prompts = [
        "一只黑色的小狗",
        "一朵蓝色的花",
        "一座高山",
        "一片海洋",
        "一只鸟儿"
    ]
    
    results = []
    
    for i, prompt in enumerate(prompts, 1):
        print(f"\n--- 请求 {i}: {prompt} ---")
        
        data = {
            "model": "jimeng-4.0",
            "prompt": prompt,
            "width": 512,
            "height": 512,
            "sample_strength": 0.5,
            "response_format": "url"
        }
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                created_time = result.get("created")
                data_list = result.get("data", [])
                
                print(f"   创建时间: {created_time}")
                print(f"   数据长度: {len(data_list)}")
                
                if data_list:
                    print(f"   ✅ 有结果！")
                    for j, item in enumerate(data_list):
                        if isinstance(item, dict) and "url" in item:
                            print(f"      图片 {j+1}: {item['url'][:50]}...")
                else:
                    print(f"   ⚠️  无结果")
                
                results.append({
                    "prompt": prompt,
                    "created": created_time,
                    "data_count": len(data_list),
                    "has_result": len(data_list) > 0
                })
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")
        
        time.sleep(3)  # 间隔3秒
    
    # 总结
    print(f"\n📊 总结:")
    success_count = sum(1 for r in results if r["has_result"])
    print(f"   总请求: {len(results)}")
    print(f"   有结果: {success_count}")
    print(f"   无结果: {len(results) - success_count}")
    
    if success_count > 0:
        print(f"   ✅ 有些请求成功了！")
    else:
        print(f"   ❌ 所有请求都没有立即结果")

def test_wait_and_check():
    """提交任务后等待较长时间再检查"""
    print(f"\n⏰ 测试长时间等待机制")
    print("=" * 40)
    
    url = "http://**************:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "jimeng-4.0",
        "prompt": "一只紫色的蝴蝶在花丛中飞舞",
        "width": 512,
        "height": 512,
        "sample_strength": 0.5,
        "response_format": "url"
    }
    
    print(f"📝 提示词: {data['prompt']}")
    
    try:
        # 提交任务
        print(f"⏳ 提交任务...")
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            created_time = result.get("created")
            
            print(f"✅ 任务提交成功，创建时间: {created_time}")
            
            # 等待较长时间
            wait_times = [30, 60, 120, 180]  # 30秒、1分钟、2分钟、3分钟
            
            for wait_time in wait_times:
                print(f"\n⏰ 等待 {wait_time} 秒后检查...")
                time.sleep(wait_time)
                
                # 检查结果
                check_response = requests.post(url, headers=headers, json=data, timeout=30)
                
                if check_response.status_code == 200:
                    check_result = check_response.json()
                    check_data = check_result.get("data", [])
                    check_created = check_result.get("created")
                    
                    print(f"   检查结果: created={check_created}, data长度={len(check_data)}")
                    
                    if check_data and len(check_data) > 0:
                        print(f"🎉 等待 {wait_time} 秒后获得结果！")
                        for i, item in enumerate(check_data):
                            if isinstance(item, dict) and "url" in item:
                                print(f"   图片 {i+1}: {item['url']}")
                        return True
                    else:
                        print(f"   ⚠️  仍无结果")
                else:
                    print(f"   ❌ 检查失败: HTTP {check_response.status_code}")
            
            print(f"❌ 等待所有时间后仍无结果")
            return False
        else:
            print(f"❌ 任务提交失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

if __name__ == "__main__":
    # 测试异步生成
    success1 = test_async_generation()
    
    # 测试多个请求
    test_multiple_requests()
    
    # 如果前面都没成功，测试长时间等待
    if not success1:
        test_wait_and_check()
