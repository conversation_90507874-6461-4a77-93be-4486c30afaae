#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试降级修复是否有效
验证当数据中没有user_id字段时，能否正确降级到只使用role查询

作者: 老王
创建日期: 2025-09-18
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utilities.unified_logger import get_unified_logger, setup_unified_logging

# 设置日志
setup_unified_logging()
logger = get_unified_logger(__name__)

def test_fallback_mechanism():
    """测试降级机制"""
    logger.info("🧪 测试降级机制...")
    
    try:
        from utilities.legacy_chroma_bridge import search_history
        
        # 测试参数
        user_id = "liu_defei_cool"
        query_text = "你好"
        n_results = 3
        
        logger.info(f"🔍 测试参数:")
        logger.info(f"   用户ID: {user_id}")
        logger.info(f"   查询内容: {query_text}")
        logger.info(f"   结果数量: {n_results}")
        
        # 测试用户消息查询（应该会降级）
        logger.info("=" * 50)
        logger.info("📝 测试用户消息查询（期望降级到role查询）...")
        user_result = search_history(user_id, query_text, n_results, "user")
        
        logger.info(f"查询结果类型: {type(user_result)}")
        if isinstance(user_result, dict) and "documents" in user_result:
            docs = user_result["documents"]
            if docs and len(docs) > 0 and docs[0]:
                logger.info(f"找到 {len(docs[0])} 个文档:")
                for i, doc in enumerate(docs[0]):
                    logger.info(f"   文档{i+1}: {doc}")
                
                # 检查是否是真实数据（不是"无记忆数据"）
                real_docs = [doc for doc in docs[0] if doc != "无记忆数据"]
                if real_docs:
                    logger.success(f"✅ 降级机制成功！找到 {len(real_docs)} 个真实文档")
                    return True
                else:
                    logger.warning("⚠️ 只找到空数据，降级可能失败")
                    return False
            else:
                logger.warning("⚠️ 没有找到任何文档")
                return False
        else:
            logger.error("❌ 查询结果格式异常")
            return False
        
    except Exception as e:
        logger.error(f"❌ 降级机制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_comparison():
    """直接对比有无user_id的查询结果"""
    logger.info("🧪 直接对比有无user_id的查询结果...")
    
    try:
        from utilities.legacy_chroma_bridge import query_collection, get_collection_id, get_embedding
        
        user_id = "liu_defei_cool"
        query_text = "你好"
        
        # 获取collection_id和嵌入向量
        collection_id = get_collection_id(user_id)
        query_embedding = get_embedding(query_text)
        
        if not collection_id or not query_embedding:
            logger.error("无法获取collection_id或嵌入向量")
            return False
        
        logger.info(f"集合ID: {collection_id}")
        logger.info(f"嵌入向量维度: {len(query_embedding)}")
        
        # 测试1: 只有role的查询
        logger.info("=" * 30)
        logger.info("🔍 测试只有role的查询...")
        result_role_only = query_collection(collection_id, query_embedding, 3, "user", None)
        
        if isinstance(result_role_only, dict) and "documents" in result_role_only:
            docs = result_role_only["documents"]
            if docs and docs[0]:
                logger.info(f"只有role查询找到 {len(docs[0])} 个文档:")
                for i, doc in enumerate(docs[0]):
                    logger.info(f"   文档{i+1}: {doc}")
        
        # 测试2: role + user_id的查询
        logger.info("=" * 30)
        logger.info("🔍 测试role + user_id的查询...")
        result_with_user_id = query_collection(collection_id, query_embedding, 3, "user", user_id)
        
        if isinstance(result_with_user_id, dict) and "documents" in result_with_user_id:
            docs = result_with_user_id["documents"]
            if docs and docs[0]:
                logger.info(f"role + user_id查询找到 {len(docs[0])} 个文档:")
                for i, doc in enumerate(docs[0]):
                    logger.info(f"   文档{i+1}: {doc}")
                
                # 检查是否是空数据
                real_docs = [doc for doc in docs[0] if doc != "无记忆数据"]
                if not real_docs:
                    logger.info("🔄 role + user_id查询返回空数据，这是预期的（数据中没有user_id字段）")
                else:
                    logger.info("✅ role + user_id查询找到真实数据")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 直接对比测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("🚀 降级修复验证测试开始")
    logger.info("=" * 60)
    
    # 测试1: 降级机制
    test1_result = test_fallback_mechanism()
    
    logger.info("-" * 60)
    
    # 测试2: 直接对比
    test2_result = test_direct_comparison()
    
    logger.info("=" * 60)
    
    # 总结
    if test1_result and test2_result:
        logger.success("🎉 降级修复验证测试通过！")
        logger.success("🔄 降级机制正常工作")
        logger.success("📊 查询对比结果正确")
        return True
    else:
        logger.error("❌ 降级修复验证测试失败")
        logger.error(f"   降级机制测试: {'通过' if test1_result else '失败'}")
        logger.error(f"   直接对比测试: {'通过' if test2_result else '失败'}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
