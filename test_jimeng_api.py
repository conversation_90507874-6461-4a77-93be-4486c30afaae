#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试 jimeng API 绘画服务
🔥 老王专用测试脚本 - 验证修复后的 jimeng API 功能
"""

import os
import sys
import json
import time
import requests
from typing import Dict, Any

# 设置项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
if PROJECT_ROOT not in sys.path:
    sys.path.append(PROJECT_ROOT)

def load_config() -> Dict[str, Any]:
    """加载配置文件"""
    config_path = os.path.join(PROJECT_ROOT, "config", "skills", "drawing_skill.json")
    if os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"✅ 配置文件加载成功: {config_path}")
            return config
        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
    else:
        print(f"❌ 配置文件不存在: {config_path}")
    
    return {}

def test_jimeng_api_direct(prompt: str, session_ids: str, **kwargs) -> Dict[str, Any]:
    """
    直接测试 jimeng API
    
    Args:
        prompt: 提示词
        session_ids: session_id字符串（多个用逗号分隔）
        **kwargs: 其他参数
    
    Returns:
        API响应结果
    """
    print(f"\n🎨 开始测试 jimeng API...")
    print(f"📝 提示词: {prompt}")
    print(f"🔑 Session IDs: {session_ids[:50]}...")
    
    # API配置
    url = "http://124.221.30.195:47653/v1/images/generations"
    
    headers = {
        "Authorization": f"Bearer {session_ids}",
        "Content-Type": "application/json"
    }
    
    # 请求数据
    data = {
        "model": kwargs.get("model", "jimeng-4.0"),
        "prompt": prompt,
        "negative_prompt": kwargs.get("negative_prompt", ""),
        "width": kwargs.get("width", 1024),
        "height": kwargs.get("height", 1024),
        "sample_strength": kwargs.get("sample_strength", 0.5),
        "response_format": "url"
    }
    
    print(f"📡 请求URL: {url}")
    print(f"📋 请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
    
    try:
        start_time = time.time()
        response = requests.post(url, headers=headers, json=data, timeout=120)
        elapsed_time = time.time() - start_time
        
        print(f"⏱️  请求耗时: {elapsed_time:.2f}秒")
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📋 响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            # 检查响应格式
            if "data" in result and isinstance(result["data"], list):
                data_list = result["data"]
                if data_list:
                    print(f"✅ 成功生成 {len(data_list)} 张图片!")
                    for i, item in enumerate(data_list):
                        if isinstance(item, dict) and "url" in item:
                            print(f"   图片 {i+1}: {item['url']}")
                    return {
                        "success": True,
                        "result": result,
                        "elapsed_time": elapsed_time
                    }
                else:
                    print(f"⚠️  data字段为空，可能需要等待生成完成")
                    return {
                        "success": False,
                        "message": "data字段为空",
                        "result": result,
                        "elapsed_time": elapsed_time
                    }
            else:
                print(f"❌ 响应格式异常，缺少data字段或格式错误")
                return {
                    "success": False,
                    "message": "响应格式异常",
                    "result": result,
                    "elapsed_time": elapsed_time
                }
        else:
            error_text = response.text
            print(f"❌ HTTP错误 {response.status_code}: {error_text}")
            return {
                "success": False,
                "message": f"HTTP错误 {response.status_code}",
                "error": error_text,
                "elapsed_time": elapsed_time
            }
            
    except requests.exceptions.Timeout:
        print(f"❌ 请求超时")
        return {
            "success": False,
            "message": "请求超时",
            "elapsed_time": time.time() - start_time
        }
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return {
            "success": False,
            "message": f"请求异常: {e}",
            "elapsed_time": time.time() - start_time
        }

def test_drawing_skill_integration():
    """测试绘画技能集成"""
    print(f"\n🔧 测试绘画技能集成...")
    
    try:
        from cognitive_modules.skills.drawing_skill import DrawingSkill
        
        # 创建绘画技能实例
        drawing_skill = DrawingSkill()
        print(f"✅ 绘画技能实例创建成功")
        
        # 测试 jimeng_generate_images 方法
        prompt = "一只可爱的小猫咪在花园里玩耍"
        print(f"📝 测试提示词: {prompt}")
        
        result = drawing_skill.jimeng_generate_images(
            prompt=prompt,
            model="jimeng-4.0",
            negative_prompt="低质量，模糊画面",
            width=1024,
            height=1024,
            sample_strength=0.5
        )
        
        print(f"📋 绘画技能返回结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if isinstance(result, dict):
            if "image_urls" in result and result["image_urls"]:
                print(f"✅ 绘画技能测试成功！生成了 {len(result['image_urls'])} 张图片")
                for i, url in enumerate(result["image_urls"]):
                    print(f"   图片 {i+1}: {url}")
                return True
            elif "error" in result:
                print(f"❌ 绘画技能返回错误: {result['error']}")
                return False
            else:
                print(f"⚠️  绘画技能返回格式异常: {result}")
                return False
        else:
            print(f"❌ 绘画技能返回类型异常: {type(result)}")
            return False
            
    except Exception as e:
        print(f"❌ 绘画技能集成测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔥 老王的 jimeng API 测试脚本启动！")
    print("=" * 60)
    
    # 加载配置
    config = load_config()
    if not config:
        print("❌ 无法加载配置文件，测试终止")
        return
    
    # 获取 jimeng 配置
    jimeng_config = config.get("jimeng", {})
    session_ids = jimeng_config.get("session_id", "")
    
    if not session_ids:
        print("❌ 配置文件中未找到 session_id，测试终止")
        return
    
    print(f"🔑 使用的 Session IDs: {session_ids}")
    
    # 测试用例
    test_cases = [
        {
            "name": "基础测试",
            "prompt": "一只可爱的小猫咪在花园里玩耍",
            "width": 1024,
            "height": 1024,
            "sample_strength": 0.5
        },
        {
            "name": "高精度测试",
            "prompt": "美丽的风景画，夕阳下的湖泊",
            "width": 1024,
            "height": 1024,
            "sample_strength": 0.8
        },
        {
            "name": "竖屏测试",
            "prompt": "现代都市夜景，霓虹灯闪烁",
            "width": 1080,
            "height": 1920,
            "sample_strength": 0.6
        }
    ]
    
    # 执行直接API测试
    print(f"\n📡 开始直接API测试...")
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i}: {test_case['name']} ---")
        
        result = test_jimeng_api_direct(
            prompt=test_case["prompt"],
            session_ids=session_ids,
            width=test_case["width"],
            height=test_case["height"],
            sample_strength=test_case["sample_strength"],
            negative_prompt=jimeng_config.get("negative_prompt", "")
        )
        
        if result.get("success"):
            success_count += 1
            print(f"✅ 测试用例 {i} 成功")
        else:
            print(f"❌ 测试用例 {i} 失败: {result.get('message', '未知错误')}")
    
    print(f"\n📊 直接API测试结果: {success_count}/{len(test_cases)} 成功")
    
    # 执行绘画技能集成测试
    print(f"\n🔧 开始绘画技能集成测试...")
    integration_success = test_drawing_skill_integration()
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"🎯 测试总结:")
    print(f"   直接API测试: {success_count}/{len(test_cases)} 成功")
    print(f"   集成测试: {'✅ 成功' if integration_success else '❌ 失败'}")
    
    if success_count == len(test_cases) and integration_success:
        print(f"🎉 所有测试通过！jimeng API 修复成功！")
    else:
        print(f"⚠️  部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
