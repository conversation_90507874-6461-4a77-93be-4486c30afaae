#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试官方标准尺寸 - 🔥 老王验证4K标准尺寸
"""

import requests
import json
import time

def test_official_4k_ratios():
    """测试官方4K标准尺寸"""
    print("🔥 老王测试官方4K标准尺寸")
    print("=" * 50)
    
    api_url = "http://127.0.0.1:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    # 官方4K标准尺寸测试
    test_cases = [
        {
            "name": "4K正方形 1:1",
            "width": 4096,
            "height": 4096,
            "expected_ratio": 1,
            "prompt": "beautiful square garden 4K"
        },
        {
            "name": "4K宽屏 16:9", 
            "width": 5404,
            "height": 3040,
            "expected_ratio": 3,
            "prompt": "widescreen landscape 4K"
        },
        {
            "name": "4K竖屏 9:16",
            "width": 3040,
            "height": 5404,
            "expected_ratio": 5,
            "prompt": "mobile portrait 4K"
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {test_case['name']} ---")
        print(f"📐 尺寸: {test_case['width']}x{test_case['height']}")
        print(f"🎯 期望ratio: {test_case['expected_ratio']}")
        
        data = {
            "model": "jimeng-4.0",
            "prompt": test_case['prompt'],
            "width": test_case['width'],
            "height": test_case['height'],
            "sample_strength": 0.5
        }
        
        try:
            start_time = time.time()
            print(f"🚀 发送请求...")
            
            response = requests.post(api_url, headers=headers, json=data, timeout=300)
            elapsed_time = time.time() - start_time
            
            print(f"⏱️  请求耗时: {elapsed_time:.2f}秒")
            print(f"📡 状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                if "data" in result and result["data"]:
                    data_list = result["data"]
                    print(f"✅ 成功生成 {len(data_list)} 张图片！")
                    for j, item in enumerate(data_list):
                        if isinstance(item, dict) and "url" in item:
                            print(f"   图片 {j+1}: {item['url'][:80]}...")
                    success_count += 1
                else:
                    print(f"⚠️  数据为空")
                    
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   错误详情: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
                except:
                    print(f"   响应文本: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        # 间隔一下避免频繁请求
        if i < len(test_cases):
            print(f"⏳ 等待30秒...")
            time.sleep(30)
    
    print(f"\n" + "=" * 50)
    print(f"🎯 官方4K标准尺寸测试结果: {success_count}/{len(test_cases)} 成功")
    
    return success_count >= 2  # 至少2个成功就算通过

def test_input_mapping():
    """测试输入尺寸到官方标准的映射"""
    print(f"\n🎨 测试输入尺寸映射")
    print("=" * 40)
    
    api_url = "http://127.0.0.1:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    # 测试各种输入尺寸的映射
    test_cases = [
        {
            "name": "输入1024x1024 → 应映射到4K正方形",
            "width": 1024,
            "height": 1024,
            "prompt": "square mapping test"
        },
        {
            "name": "输入1920x1080 → 应映射到4K宽屏",
            "width": 1920,
            "height": 1080,
            "prompt": "widescreen mapping test"
        },
        {
            "name": "输入1080x1920 → 应映射到4K竖屏",
            "width": 1080,
            "height": 1920,
            "prompt": "portrait mapping test"
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 映射测试 {i}: {test_case['name']} ---")
        print(f"📝 输入尺寸: {test_case['width']}x{test_case['height']}")
        
        data = {
            "model": "jimeng-4.0",
            "prompt": test_case['prompt'],
            "width": test_case['width'],
            "height": test_case['height'],
            "sample_strength": 0.5
        }
        
        try:
            start_time = time.time()
            response = requests.post(api_url, headers=headers, json=data, timeout=300)
            elapsed_time = time.time() - start_time
            
            print(f"⏱️  请求耗时: {elapsed_time:.2f}秒")
            print(f"📡 状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                if "data" in result and result["data"]:
                    data_list = result["data"]
                    print(f"✅ 映射测试成功！生成 {len(data_list)} 张图片")
                    success_count += 1
                else:
                    print(f"⚠️  映射测试无结果")
            else:
                print(f"❌ 映射测试失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 映射测试异常: {e}")
        
        if i < len(test_cases):
            time.sleep(30)
    
    print(f"\n🎯 输入映射测试结果: {success_count}/{len(test_cases)} 成功")
    return success_count >= 2

def main():
    """主函数"""
    print("🔥🔥🔥 老王的官方标准尺寸验证 🔥🔥🔥")
    print("=" * 60)
    
    # 测试1: 官方4K标准尺寸
    result1 = test_official_4k_ratios()
    
    # 测试2: 输入尺寸映射
    result2 = test_input_mapping()
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"🎯 最终验证结果:")
    print(f"   官方4K标准尺寸: {'✅ 成功' if result1 else '❌ 失败'}")
    print(f"   输入尺寸映射: {'✅ 成功' if result2 else '❌ 失败'}")
    
    if result1 and result2:
        print(f"\n🎉 恭喜！后端API完全支持官方标准尺寸！")
        print(f"💡 支持的功能:")
        print(f"   1. ✅ 4K正方形 4096x4096 (ratio_type: 1)")
        print(f"   2. ✅ 4K宽屏 5404x3040 (ratio_type: 3)")
        print(f"   3. ✅ 4K竖屏 3040x5404 (ratio_type: 5)")
        print(f"   4. ✅ 智能输入尺寸映射到官方标准")
        print(f"   5. ✅ 完全兼容drawing_skill.py调用格式")
        print(f"\n🚀 可以正式集成到生产环境！")
    elif result1:
        print(f"\n⚠️  官方标准尺寸支持良好，输入映射需要优化")
    else:
        print(f"\n😤 还有问题需要继续调试")

if __name__ == "__main__":
    main()
