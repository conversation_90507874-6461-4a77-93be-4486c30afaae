2025-09-18 14:52:24,209 - Digital - [config_loader] - ℹ️  使用主配置目录: /root/yanran_digital_life/config
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:28,903 - Digital - [intelligence] - ℹ️  AI智能化系统模块已加载
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:29,286 - Digital - [main.success] - ✅ 数字生命体系统启动程序开始执行...
2025-09-18 14:52:29,299 - Digital - [unified_system_config_manager.start_watching] - ℹ️  配置文件监视器已启动
2025-09-18 14:52:29,299 - Digital - [unified_system_config_manager.__init__] - ℹ️  统一系统配置管理器初始化完成
2025-09-18 14:52:29,300 - Digital - [main.main] - ℹ️  使用统一配置管理器，配置目录: /root/yanran_digital_life/config
2025-09-18 14:52:29,300 - Digital - [main.success] - ✅ 初始化系统...
2025-09-18 14:52:29,300 - Digital - [main.success] - ✅ OpenAI配置加载成功: config/openai_config.json
2025-09-18 14:52:29,300 - Digital - [main._setup_signal_handlers] - ℹ️  🔧 信号处理器设置完成
2025-09-18 14:52:29,300 - Digital - [main.__init__] - ℹ️  数字生命体系统实例已创建
2025-09-18 14:52:29,300 - Digital - [main.success] - ✅ 开始初始化数字生命体系统...
2025-09-18 14:52:29,301 - Digital - [main._load_config] - ℹ️  已加载开发配置: /root/yanran_digital_life/config/system_dev.json
2025-09-18 14:52:29,305 - Digital - [dependency_checker.load_dependency_config] - ℹ️  已启用依赖检查跳过模式
2025-09-18 14:52:29,305 - Digital - [dependency_checker.load_dependency_config] - ℹ️  已标记 5 个模块为可选: requests, mysql, openai, networkx, psutil
2025-09-18 14:52:29,305 - Digital - [dependency_checker.set_skip_dependency_checks] - ℹ️  启用依赖检查跳过模式
2025-09-18 14:52:29,305 - Digital - [main._load_config] - ℹ️  已标记 5 个模块为可选依赖
2025-09-18 14:52:29,305 - Digital - [main._load_config] - ℹ️  已加载统一系统配置
2025-09-18 14:52:29,305 - Digital - [main.initialize] - ℹ️  系统名称: 林嫣然数字生命体
2025-09-18 14:52:29,305 - Digital - [singleton_manager.success] - ✅ 已清除重复初始化的详细数据
2025-09-18 14:52:29,306 - Digital - [main.success] - ✅ 单例管理器初始化完成
2025-09-18 14:52:29,306 - Digital - [singleton_manager.get_or_create] - ℹ️  通过工厂函数创建实例: 'enhanced_event_bus'
2025-09-18 14:52:29,306 - Digital - [event_bus.success] - ✅ 事件总线处理线程已启动
2025-09-18 14:52:29,306 - Digital - [enhanced_event_bus.success] - ✅ 增强型事件总线初始化完成
2025-09-18 14:52:29,311 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'enhanced_event_bus'
2025-09-18 14:52:29,312 - Digital - [main.success] - ✅ 事件总线初始化完成
2025-09-18 14:52:29,312 - Digital - [core.exception.unified_exception_handler.__init__] - ℹ️  🔧 统一异常处理器初始化完成
2025-09-18 14:52:29,312 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'exception_handler'
2025-09-18 14:52:29,312 - Digital - [main.success] - ✅ 🔧 统一异常处理器初始化完成
2025-09-18 14:52:29,312 - Digital - [core.life_context.success] - ✅ 初始化生命上下文...
2025-09-18 14:52:29,312 - Digital - [core.life_context.success] - ✅ 生命上下文初始化完成
2025-09-18 14:52:29,312 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'life_context'
2025-09-18 14:52:29,312 - Digital - [main.success] - ✅ 生命上下文初始化完成
2025-09-18 14:52:29,312 - Digital - [main.initialize] - ℹ️  已更新搜索技能API配置
2025-09-18 14:52:29,313 - Digital - [main.initialize] - ℹ️  已更新音乐技能API配置
2025-09-18 14:52:29,313 - Digital - [main.initialize] - ℹ️  已更新drawing_skillAPI配置
2025-09-18 14:52:29,313 - Digital - [main.initialize] - ℹ️  已更新chat_skillAPI配置
2025-09-18 14:52:29,313 - Digital - [main.success] - ✅ 统一技能配置加载完成
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:29,317 - Digital - [adapters.unified_ai_adapter.success] - ✅ 初始化统一AI适配器
2025-09-18 14:52:29,317 - Digital - [adapters.unified_ai_adapter._initialize] - ℹ️  加载AI服务配置: config/ai_services.json
2025-09-18 14:52:29,318 - Digital - [adapters.unified_ai_adapter._initialize] - ℹ️  设置默认AI服务: openai
2025-09-18 14:52:29,318 - Digital - [adapters.unified_ai_adapter._initialize] - ℹ️  已加载AI服务: deepseek, openai, zhipu, qianwen, compatible_service
2025-09-18 14:52:29,318 - Digital - [adapters.unified_ai_adapter.success] - ✅ 统一AI适配器已初始化
2025-09-18 14:52:29,318 - Digital - [main.success] - ✅ AI适配器初始化完成
2025-09-18 14:52:29,318 - Digital - [adapters.ai_service_adapter.__init__] - ℹ️  AI服务适配器已创建
2025-09-18 14:52:29,318 - Digital - [adapters.ai_service_adapter.__init__] - ℹ️  服务可用性: {'openai': True, 'zhipuai': False, 'dashscope': False, 'qianfan': False, 'baidu': False}
2025-09-18 14:52:29,318 - Digital - [main.success] - ✅ AI服务适配器直接初始化成功
2025-09-18 14:52:29,318 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'ai_service_adapter'
2025-09-18 14:52:29,319 - Digital - [main.success] - ✅ AI服务适配器初始化完成
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:29,332 - Digital - [thinking_chain.success] - ✅ 初始化思维链路...
2025-09-18 14:52:29,332 - Digital - [thinking_chain.__init__] - ℹ️  已加载思维链路配置: config/thinking_chain.json
2025-09-18 14:52:29,333 - Digital - [thinking_chain.success] - ✅ 已初始化 10 个思维步骤
2025-09-18 14:52:29,333 - Digital - [thinking_chain.success] - ✅ 思维链路初始化完成
2025-09-18 14:52:29,333 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'thinking_chain'
2025-09-18 14:52:29,333 - Digital - [main.success] - ✅ 思维链路初始化完成
2025-09-18 14:52:29,333 - Digital - [main.success] - ✅ 初始化中间件服务...
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:29,337 - Digital - [middleware.success] - ✅ 初始化中间件
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:29,342 - Digital - [middleware.system_adapter] - ℹ️  已加载开发配置，启用兼容模式
2025-09-18 14:52:29,342 - Digital - [middleware.system_adapter.__init__] - ℹ️  系统适配器已创建
2025-09-18 14:52:29,342 - Digital - [middleware.system_adapter.success] - ✅ 初始化系统适配器
2025-09-18 14:52:29,342 - Digital - [middleware.system_adapter.initialize] - ℹ️  已创建模拟数字生命体
2025-09-18 14:52:29,342 - Digital - [middleware.system_adapter._register_event_handlers] - ℹ️  已注册系统适配器事件处理器
2025-09-18 14:52:29,343 - Digital - [middleware.system_adapter.success] - ✅ 系统适配器初始化成功
2025-09-18 14:52:29,343 - Digital - [middleware.success] - ✅ 中间件初始化成功
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:29,431 - Digital - [startup_hook.success] - ✅ 启动钩子已创建
2025-09-18 14:52:29,431 - Digital - [main.success] - ✅ 启动钩子初始化完成
2025-09-18 14:52:29,432 - Digital - [api_service.success] - ✅ 初始化API服务...
2025-09-18 14:52:29,432 - Digital - [api_service.success] - ✅ 已连接事件总线
2025-09-18 14:52:29,432 - Digital - [api_service.success] - ✅ API服务初始化完成
2025-09-18 14:52:29,432 - Digital - [main.success] - ✅ API服务初始化完成
2025-09-18 14:52:29,432 - Digital - [main.success] - ✅ 中间件 system_adapter 初始化完成
2025-09-18 14:52:29,434 - Digital - [cognitive_integration.__init__] - ℹ️  认知模块集成器已创建
2025-09-18 14:52:29,434 - Digital - [cognitive_integration.success] - ✅ 已连接事件总线
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:29,450 - Digital - [cognitive.module_manager.__init__] - ℹ️  认知模块管理器已创建
2025-09-18 14:52:29,450 - Digital - [cognitive.module_manager.success] - ✅ 初始化认知模块管理器
2025-09-18 14:52:29,450 - Digital - [cognitive.module_manager._register_event_handlers] - ℹ️  认知模块管理器事件处理器已注册
2025-09-18 14:52:29,450 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描认知模块目录
2025-09-18 14:52:29,450 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: emotion
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:30,214 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.emotion.autonomous_emotion_system
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:30,224 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.emotion.emotion_system_integration
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:30,230 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.emotion.emotional_intelligence
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:30,236 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.emotion.empathy_understanding
2025-09-18 14:52:30,236 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: emotion.EmpathyUnderstanding
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:30,242 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.emotion.autonomous_emotion_manager
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:30,249 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.emotion.emotion_analyzer
2025-09-18 14:52:30,250 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: emotion.EmotionAnalyzer
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:30,254 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.emotion.emotion_regulation
2025-09-18 14:52:30,254 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: emotion.EmotionRegulation
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:30,269 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.emotion.emotion_simulation
2025-09-18 14:52:30,269 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: emotion.EmotionSimulation
2025-09-18 14:52:30,272 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.emotion.response_modulation
2025-09-18 14:52:30,274 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.emotion.state_analysis
2025-09-18 14:52:30,276 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.emotion.emotion_engine
2025-09-18 14:52:30,276 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: physiology
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:30,282 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.physiology.health_system
2025-09-18 14:52:30,282 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: physiology.HealthSystem
2025-09-18 14:52:30,282 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: skills
2025-09-18 14:52:30,289 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.skills.search_skill
2025-09-18 14:52:30,299 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.skills.drawing_skill
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:30,314 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.skills.chat_skill
2025-09-18 14:52:30,315 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.skills.skill_template
2025-09-18 14:52:30,332 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.skills.skill_manager
2025-09-18 14:52:30,339 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.skills.chat_skill_template
2025-09-18 14:52:30,346 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.skills.enhanced_greeting_skill
2025-09-18 14:52:30,353 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.skills.assistant_reminder_skill
2025-09-18 14:52:30,359 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.skills.enhanced_greeting_skill_simple
2025-09-18 14:52:30,363 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.skills.enhanced_activity_skill
2025-09-18 14:52:30,363 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: autonomy
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:30,379 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.autonomy.conscious_awareness
2025-09-18 14:52:30,379 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: autonomy.ConsciousAwareness
2025-09-18 14:52:30,397 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.autonomy.conscious_decision
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:30,403 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.autonomy.decision_making
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:30,411 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.autonomy.values_system
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:30,416 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.autonomy.motivation_system
2025-09-18 14:52:30,416 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: perception
2025-09-18 14:52:32,442 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.trend_intelligence
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:32,448 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.multimodal_perception
2025-09-18 14:52:32,448 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: perception.MultimodalPerception
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:32,454 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.social_perception
2025-09-18 14:52:32,454 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: perception.SocialPerception
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:32,463 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.enhanced_user_perception
2025-09-18 14:52:32,464 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: perception.EnhancedUserPerception
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:32,473 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.user_model_builder
2025-09-18 14:52:32,474 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: perception.UserModelBuilder
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:32,479 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.intention_analysis
2025-09-18 14:52:32,492 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.intention_system
2025-09-18 14:52:32,504 - Digital - [intent_recognition] - ℹ️  ✅ 意图上下文管理器导入成功
2025-09-18 14:52:32,505 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.intent_recognition
2025-09-18 14:52:32,529 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.activity_perception
2025-09-18 14:52:32,532 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.entity_extraction
2025-09-18 14:52:32,534 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.data_perception
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:32,539 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.perception.user_perception
2025-09-18 14:52:32,543 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.perception_engine
2025-09-18 14:52:32,550 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.intelligent_scheduler
2025-09-18 14:52:32,560 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.environmental_perception
2025-09-18 14:52:33,668 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.perception.trend_perception
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:33,675 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.perception.user_model
2025-09-18 14:52:33,675 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: perception.UserModel
2025-09-18 14:52:33,675 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: behavior
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:33,732 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.behavior.action_planning
2025-09-18 14:52:33,733 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: behavior.ActionPlanning
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:33,738 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.behavior.response_generator
2025-09-18 14:52:33,738 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: behavior.ResponseGenerator
2025-09-18 14:52:33,739 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.behavior.response_generation
2025-09-18 14:52:33,743 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.behavior.behavior_manager
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:33,756 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.behavior.creative_content_generator
2025-09-18 14:52:33,756 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: behavior.CreativeContentGenerator
2025-09-18 14:52:33,765 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.behavior.activity_executor
2025-09-18 14:52:33,766 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: behavior.ActivityExecutor
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:33,771 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.behavior.multimodal_expression
2025-09-18 14:52:33,772 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: behavior.MultimodalExpression
2025-09-18 14:52:33,772 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: memory
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:33,776 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.memory.memory_retrieval
2025-09-18 14:52:33,777 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.EpisodicMemory
2025-09-18 14:52:33,777 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.MemoryRetrieval
2025-09-18 14:52:33,777 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.ProceduralMemory
2025-09-18 14:52:33,779 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.memory.association
2025-09-18 14:52:33,781 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.memory.memory_manager
2025-09-18 14:52:33,788 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.memory.procedural_memory
2025-09-18 14:52:33,788 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.ProceduralMemory
2025-09-18 14:52:33,790 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.memory.retrieval
2025-09-18 14:52:33,796 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.memory.long_term_memory_manager
2025-09-18 14:52:33,796 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.EpisodicMemory
2025-09-18 14:52:33,796 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.LongTermMemoryManager
2025-09-18 14:52:33,796 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.MemoryIntegration
2025-09-18 14:52:33,806 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.memory.memory_integration
2025-09-18 14:52:33,806 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.EpisodicMemory
2025-09-18 14:52:33,806 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.MemoryIntegration
2025-09-18 14:52:33,806 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.ProceduralMemory
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:33,811 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.memory.memory_consolidation
2025-09-18 14:52:33,811 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.EpisodicMemory
2025-09-18 14:52:33,811 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.MemoryConsolidation
2025-09-18 14:52:33,811 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.ProceduralMemory
2025-09-18 14:52:33,820 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.memory.episodic_memory
2025-09-18 14:52:33,821 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: memory.EpisodicMemory
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:33,827 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.memory.semantic_memory
2025-09-18 14:52:33,827 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: neural
2025-09-18 14:52:33,850 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.neural.organ_neural_network
2025-09-18 14:52:33,850 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: ai
2025-09-18 14:52:33,866 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.ai.yanran_decision_engine
2025-09-18 14:52:33,867 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: cognition
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:33,882 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.cognition.autonomous_decision
2025-09-18 14:52:33,882 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: cognition.AutonomousDecision
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:33,894 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.cognition.adaptive_dialog
2025-09-18 14:52:33,894 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: cognition.AdaptiveDialogModule
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:33,915 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.cognition.conscious_decision
2025-09-18 14:52:33,916 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: cognition.ConsciousDecision
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:33,925 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.cognition.reasoning
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:33,941 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.cognition.autonomous_thinking
2025-09-18 14:52:33,942 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: cognition.AutonomousThinking
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:33,946 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.cognition.self_reflection
2025-09-18 14:52:33,947 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: cognition.SelfReflection
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:34,556 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.cognition.social_network
2025-09-18 14:52:34,556 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: cognition.SocialNetwork
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:34,564 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.cognition.self_model
2025-09-18 14:52:34,564 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: cognition.SelfModel
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:34,572 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.cognition.knowledge_graph
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:34,579 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.cognition.intention_network
2025-09-18 14:52:34,579 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: cognition.IntentionNetwork
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:34,586 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.cognition.decision_making
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:34,607 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.cognition.creative_thinking
2025-09-18 14:52:34,607 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: cognition.CreativeThinking
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:34,616 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.cognition.learning
2025-09-18 14:52:34,617 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: decision
2025-09-18 14:52:34,627 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.decision.yanran_response_decision
2025-09-18 14:52:34,628 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: society
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:34,655 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.society.social_community
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:34,658 - Digital - [cognitive.society.multi_agent_system_patch] - ℹ️  多代理系统补丁已应用
2025-09-18 14:52:34,658 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.society.multi_agent_system_patch
2025-09-18 14:52:34,658 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: society.MultiAgentSystem
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:34,666 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.society.multi_agent_system
2025-09-18 14:52:34,666 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: society.MultiAgentSystem
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:34,668 - Digital - [cognitive.society.social_network_patch] - ℹ️  社交网络补丁已应用
2025-09-18 14:52:34,668 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.society.social_network_patch
2025-09-18 14:52:34,669 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: society.SocialNetwork
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:34,680 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.society.social_network
2025-09-18 14:52:34,680 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: society.SocialNetwork
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:34,686 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.society.social_interaction
2025-09-18 14:52:34,686 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  发现模块类: society.SocialInteraction
2025-09-18 14:52:34,687 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: organs
2025-09-18 14:52:34,715 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.world_perception_organ
2025-09-18 14:52:34,721 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.social_interaction_organ
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:34,727 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.organ_manager
2025-09-18 14:52:34,737 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.relationship_guardian_organ
2025-09-18 14:52:34,747 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.personalized_service_organ
2025-09-18 14:52:34,756 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.relationship_coordination_organ
2025-09-18 14:52:34,762 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.skill_coordination_organ
2025-09-18 14:52:34,771 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.enhanced_expression_types
2025-09-18 14:52:34,779 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.data_perception_organ
2025-09-18 14:52:34,855 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.proactive_expression_organ
2025-09-18 14:52:34,861 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.learning_adaptation_organ
2025-09-18 14:52:34,872 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.enhanced_proactive_expression_organ
2025-09-18 14:52:34,877 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  重新加载模块: cognitive_modules.organs.base_organ
2025-09-18 14:52:34,880 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.emotional_processing_organ
2025-09-18 14:52:34,888 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.creative_expression_organ
2025-09-18 14:52:34,896 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.safety_protection_organ
2025-09-18 14:52:34,902 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.wealth_management_organ
2025-09-18 14:52:34,909 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.world_perception_data_manager
2025-09-18 14:52:34,914 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.intelligent_expression_triggers
2025-09-18 14:52:34,918 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.organs.decision_making_organ
2025-09-18 14:52:34,918 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  扫描模块类型: thinking
2025-09-18 14:52:34,923 - Digital - [cognitive.module_manager._scan_modules] - ℹ️  首次加载模块: cognitive_modules.thinking.proactive_thinking_chain
2025-09-18 14:52:34,923 - Digital - [cognitive.module_manager.success] - ✅ 模块扫描完成，发现 34 个模块类
2025-09-18 14:52:34,923 - Digital - [cognitive.module_manager.success] - ✅ 认知模块管理器初始化成功
2025-09-18 14:52:34,923 - Digital - [cognitive_integration.success] - ✅ 已连接认知模块管理器
2025-09-18 14:52:34,924 - Digital - [main.success] - ✅ 中间件 cognitive_integration 初始化完成
2025-09-18 14:52:34,924 - Digital - [main.success] - ✅ 中间件服务初始化完成
2025-09-18 14:52:34,924 - Digital - [main.success] - ✅ 🧠 老王：草，终于用现有的自主学习系统了！
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:34,942 - Digital - [core.ai_enhanced_evolution.success] - ✅ 初始化AI增强进化系统...
2025-09-18 14:52:34,942 - Digital - [core_evolution.success] - ✅ 初始化进化系统...
2025-09-18 14:52:34,942 - Digital - [event_bus.success] - ✅ 事件总线处理线程已启动
2025-09-18 14:52:34,943 - Digital - [core_evolution.__init__] - ℹ️  ✅ 能力集初始化完成
2025-09-18 14:52:34,943 - Digital - [core_evolution.__init__] - ℹ️  ✅ 进化目标初始化完成
2025-09-18 14:52:34,943 - Digital - [core_evolution.__init__] - ℹ️  ✅ 事件处理器注册完成
2025-09-18 14:52:34,943 - Digital - [core_evolution.__init__] - ℹ️  ✅ 进化监控启动完成
2025-09-18 14:52:34,943 - Digital - [core_evolution.success] - ✅ 进化系统初始化完成
2025-09-18 14:52:34,943 - Digital - [core.ai_enhanced_evolution.__init__] - ℹ️  ✅ 父类初始化成功，所有必要属性存在
2025-09-18 14:52:35,178 - Digital - [core.evolution.evolution_engine._load_config_direct] - ℹ️  ✅ 进化引擎配置加载成功: config/evolution_engine.json
2025-09-18 14:52:35,179 - Digital - [core.evolution.evolution_engine._init_compatibility_attributes] - ℹ️  🧬 自主进化引擎初始化完成 - 准备让数字生命进化！
2025-09-18 14:52:35,179 - Digital - [core.evolution.evolution_engine._load_persistent_state] - ℹ️  ✅ 已恢复进化状态: 代数=7117, 成功次数=2909, 累计改进=-2.120
2025-09-18 14:52:35,179 - Digital - [core.evolution.evolution_engine.__init__] - ℹ️  🧬 自主进化引擎初始化完成 - 准备让数字生命进化！
2025-09-18 14:52:35,180 - Digital - [core.monitoring.metrics_collector.__init__] - ℹ️  📊 指标收集器初始化完成
2025-09-18 14:52:35,180 - Digital - [core.monitoring.metrics_collector._collection_loop] - ℹ️  📈 开始指标收集循环
2025-09-18 14:52:35,180 - Digital - [core.monitoring.metrics_collector.start_collection] - ℹ️  🚀 指标收集已启动，间隔: 30秒
2025-09-18 14:52:37,181 - Digital - [core.ai_enhanced_evolution._validate_metrics_collector] - ℹ️  ✅ 指标收集器验证成功，当前指标数量: 6
2025-09-18 14:52:37,181 - Digital - [core.ai_enhanced_evolution._validate_ai_adapter] - ℹ️  ✅ AI适配器验证成功
2025-09-18 14:52:37,182 - Digital - [core.ai_enhanced_evolution._initialize_evolution_dependencies] - ℹ️  ✅ 进化引擎真实依赖项初始化完成
2025-09-18 14:52:37,182 - Digital - [core.ai_enhanced_evolution.success] - ✅ ✅ 进化引擎已连接到AI增强进化系统
2025-09-18 14:52:37,183 - Digital - [core.ai_enhanced_evolution.success] - ✅ AI增强进化系统初始化完成
2025-09-18 14:52:37,183 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'ai_enhanced_evolution'
2025-09-18 14:52:37,183 - Digital - [core.ai_enhanced_evolution.activate] - ℹ️  激活AI增强进化系统...
2025-09-18 14:52:37,183 - Digital - [core.evolution.evolution_engine._load_evolution_strategies] - ℹ️  📋 加载进化策略: performance_optimization - 性能优化策略 - 优化CPU和内存使用
2025-09-18 14:52:37,183 - Digital - [core.evolution.evolution_engine._load_evolution_strategies] - ℹ️  📋 加载进化策略: user_experience_enhancement - 用户体验提升策略 - 优化响应时间和错误处理
2025-09-18 14:52:37,183 - Digital - [core.evolution.evolution_engine._load_evolution_strategies] - ℹ️  📋 加载进化策略: ai_model_optimization - AI模型优化策略 - 优化模型选择和参数
2025-09-18 14:52:37,183 - Digital - [core.evolution.evolution_engine._load_evolution_strategies] - ℹ️  📋 加载进化策略: resource_management - 资源管理优化策略 - 优化内存和存储使用
2025-09-18 14:52:37,183 - Digital - [core.evolution.evolution_engine._load_evolution_strategies] - ℹ️  📋 加载进化策略: advanced_ai_enhancement - 高级AI增强策略 - 多模型融合和智能决策
2025-09-18 14:52:37,183 - Digital - [core.evolution.evolution_engine._load_evolution_strategies] - ℹ️  ✅ 共加载 5 个进化策略
2025-09-18 14:52:37,183 - Digital - [core.evolution.evolution_engine._establish_baseline] - ℹ️  📊 基线指标建立完成，综合评分: 0.876
2025-09-18 14:52:37,183 - Digital - [core.evolution.evolution_engine.initialize] - ℹ️  🚀 进化引擎组件初始化完成，准备开始进化！
2025-09-18 14:52:37,183 - Digital - [core.ai_enhanced_evolution.activate] - ℹ️  ✅ 进化引擎初始化完成
2025-09-18 14:52:37,184 - Digital - [core.ai_enhanced_evolution.activate] - ℹ️  🧬 进化循环已作为后台任务启动
2025-09-18 14:52:37,184 - Digital - [core.ai_enhanced_evolution.activate] - ℹ️  AI增强进化系统已激活
2025-09-18 14:52:37,184 - Digital - [main.success] - ✅ 🧬 进化引擎已在activate()中启动
2025-09-18 14:52:37,184 - Digital - [core_evolution.add_evolution_goal] - ℹ️  已添加进化目标: 消除所有模拟数据使用，实现真实数据驱动
2025-09-18 14:52:37,184 - Digital - [main.success] - ✅ ✅ 进化目标添加成功
2025-09-18 14:52:37,184 - Digital - [main.success] - ✅ ✅ AI增强进化系统激活成功
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:37,250 - Digital - [health_checker.success] - ✅ 健康检查器初始化完成
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:37,306 - Digital - [core.ai_enhanced_consciousness.success] - ✅ 🧠 神经网络增强模块加载成功
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:37,333 - Digital - [digital_life.success] - ✅ 初始化数字生命体...
2025-09-18 14:52:37,333 - Digital - [core.ai_enhanced_consciousness.success] - ✅ 初始化AI增强意识系统...
2025-09-18 14:52:37,333 - Digital - [core.consciousness.success] - ✅ 初始化意识系统...
2025-09-18 14:52:37,333 - Digital - [core.consciousness._load_consciousness_state] - ℹ️  意识状态已从 data/consciousness/consciousness_state.json 加载
2025-09-18 14:52:37,333 - Digital - [core.consciousness._load_consciousness_state] - ℹ️  意识清醒度: 1.00
2025-09-18 14:52:37,333 - Digital - [core.consciousness._load_consciousness_state] - ℹ️  认知负载: 0.00
2025-09-18 14:52:37,334 - Digital - [core.consciousness._load_consciousness_state] - ℹ️  历史记录: 0 条
2025-09-18 14:52:37,334 - Digital - [core.consciousness.success] - ✅ 意识系统初始化完成
2025-09-18 14:52:37,335 - Digital - [core.ai_enhanced_consciousness.success] - ✅ 初始化自我模型...
2025-09-18 14:52:37,335 - Digital - [core.ai_enhanced_consciousness.success] - ✅ AI增强意识系统初始化完成
2025-09-18 14:52:37,335 - Digital - [core.neural_network.neural_core.success] - ✅ 初始化神经网络核心...
2025-09-18 14:52:37,336 - Digital - [core.neural_network.neural_core.success] - ✅ 神经网络核心初始化完成
2025-09-18 14:52:37,337 - Digital - [resilience_system.success] - ✅ 初始化韧性自愈系统...
2025-09-18 14:52:37,337 - Digital - [resilience_system.success] - ✅ 韧性自愈系统已初始化
2025-09-18 14:52:37,337 - Digital - [digital_life._initialize_hot_topics_components] - ℹ️  🔥 开始初始化热搜感知智能化组件...
2025-09-18 14:52:37,338 - Digital - [cognitive_modules.perception.trend_perception.__init__] - ℹ️  趋势感知引擎初始化完成
2025-09-18 14:52:37,338 - Digital - [digital_life.success] - ✅ ✅ 趋势感知引擎初始化完成
2025-09-18 14:52:37,338 - Digital - [cognitive_modules.perception.trend_intelligence._initialize_trend_engine] - ℹ️  趋势感知引擎引用初始化完成
2025-09-18 14:52:37,338 - Digital - [cognitive_modules.perception.trend_intelligence.__init__] - ℹ️  智能分析引擎初始化完成
2025-09-18 14:52:37,338 - Digital - [digital_life.success] - ✅ ✅ 智能分析引擎初始化完成
2025-09-18 14:52:37,351 - Digital - [intelligent_scheduler.success] - ✅ 初始化了 67 个平台的调度信息
2025-09-18 14:52:37,351 - Digital - [intelligent_scheduler.success] - ✅ 智能调度器初始化完成
2025-09-18 14:52:37,352 - Digital - [digital_life.success] - ✅ ✅ 智能调度器初始化完成
2025-09-18 14:52:37,366 - Digital - [real_time_data_collector.success] - ✅ 智能调度器初始化完成
2025-09-18 14:52:37,366 - Digital - [real_time_data_collector.success] - ✅ 趋势感知引擎初始化完成
2025-09-18 14:52:37,366 - Digital - [real_time_data_collector.success] - ✅ 智能分析引擎初始化完成
2025-09-18 14:52:37,366 - Digital - [real_time_data_collector.success] - ✅ 实时数据收集器初始化完成
2025-09-18 14:52:37,366 - Digital - [digital_life.success] - ✅ ✅ 实时数据收集器初始化完成
2025-09-18 14:52:37,366 - Digital - [digital_life._subscribe_hot_topics_events] - ℹ️  热搜感知事件订阅完成
2025-09-18 14:52:37,366 - Digital - [digital_life.success] - ✅ 🔥 热搜感知智能化组件初始化完成
2025-09-18 14:52:37,366 - Digital - [intent_recognition.success] - ✅ 开始初始化意图识别器...
2025-09-18 14:52:37,367 - Digital - [intent_recognition._load_config] - ℹ️  已加载意图识别配置: /root/yanran_digital_life/config/perception/intent_recognition.json
2025-09-18 14:52:37,367 - Digital - [intent_recognition.success] - ✅ OpenAI配置初始化成功
2025-09-18 14:52:37,367 - Digital - [intent_recognition.success] - ✅ OpenAI配置初始化成功
2025-09-18 14:52:37,367 - Digital - [intent_recognition.success] - ✅ AI服务适配器初始化成功
2025-09-18 14:52:37,367 - Digital - [intent_recognition._register_event_handlers] - ℹ️  意图识别器已注册事件处理器
2025-09-18 14:52:37,367 - Digital - [intent_recognition.success] - ✅ 意图识别器连接到事件总线和生命上下文
2025-09-18 14:52:37,367 - Digital - [intent_recognition.success] - ✅ 意图识别器初始化完成
2025-09-18 14:52:37,369 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'intent_recognizer'
2025-09-18 14:52:37,369 - Digital - [intent_recognition.get_instance] - ℹ️  ✅ 意图识别器已注册到单例管理器
2025-09-18 14:52:37,369 - Digital - [digital_life._initialize_intent_recognizer] - ℹ️  意图识别器初始化成功
2025-09-18 14:52:37,370 - Digital - [digital_life.success] - ✅ 数字生命体初始化完成
2025-09-18 14:52:37,370 - Digital - [core.consciousness.success] - ✅ 初始化意识系统...
2025-09-18 14:52:37,370 - Digital - [core.consciousness._load_consciousness_state] - ℹ️  意识状态已从 data/consciousness/consciousness_state.json 加载
2025-09-18 14:52:37,370 - Digital - [core.consciousness._load_consciousness_state] - ℹ️  意识清醒度: 1.00
2025-09-18 14:52:37,370 - Digital - [core.consciousness._load_consciousness_state] - ℹ️  认知负载: 0.00
2025-09-18 14:52:37,370 - Digital - [core.consciousness._load_consciousness_state] - ℹ️  历史记录: 0 条
2025-09-18 14:52:37,371 - Digital - [core.consciousness.success] - ✅ 意识系统初始化完成
2025-09-18 14:52:37,371 - Digital - [connectors.database.mysql._load_config] - ℹ️  从 /root/yanran_digital_life/config/database.json 加载MySQL配置
2025-09-18 14:52:37,674 - Digital - [mysql.connector.get_auth_plugin] - ℹ️  package: mysql.connector.plugins
2025-09-18 14:52:37,674 - Digital - [mysql.connector.get_auth_plugin] - ℹ️  plugin_name: mysql_native_password
2025-09-18 14:52:37,676 - Digital - [mysql.connector.get_auth_plugin] - ℹ️  AUTHENTICATION_PLUGIN_CLASS: MySQLNativePasswordAuthPlugin
2025-09-18 14:52:39,788 - Digital - [connectors.database.mysql.success] - ✅ MySQL连接池初始化成功 (重试 1/5, 池大小: 5)
2025-09-18 14:52:39,789 - Digital - [resilience_system.register_service] - ℹ️  已注册服务: mysql_connector (类型: database)
2025-09-18 14:52:39,789 - Digital - [connectors.database.mysql.success] - ✅ ✅ MySQL连接器已注册到韧性系统
2025-09-18 14:52:39,791 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'mysql_connector'
2025-09-18 14:52:39,791 - Digital - [connectors.database.mysql.success] - ✅ ✅ MySQL连接器已注册到单例管理器
2025-09-18 14:52:39,791 - Digital - [connectors.database.mysql.success] - ✅ MySQL连接器初始化完成，已连接到 **************:3306/linyanran
2025-09-18 14:52:39,794 - Digital - [connectors.database.redis.success] - ✅ Redis连接初始化成功，连接到 localhost:6379/0
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:39,808 - Digital - [adapters.legacy.success] - ✅ MySQL连接器已成功导入
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:39,813 - Digital - [adapters.legacy.success] - ✅ 🔗 LegacyAdapter使用统一MySQL连接器: config/database.json
2025-09-18 14:52:39,813 - Digital - [adapters.legacy.success] - ✅ ✅ LegacyAdapter MySQL连接器初始化成功
2025-09-18 14:52:39,813 - Digital - [adapters.legacy.__init__] - ℹ️  LegacyAdapter已创建
2025-09-18 14:52:39,813 - Digital - [adapters.legacy.success] - ✅ ✅ 统一MySQL连接器已就绪
2025-09-18 14:52:39,813 - Digital - [adapters.legacy.success] - ✅ LegacyAdapter初始化完成
2025-09-18 14:52:39,813 - Digital - [utilities.storage_manager.success] - ✅ 从统一配置管理器加载数据库配置，优先级: remote
2025-09-18 14:52:39,814 - Digital - [utilities.storage_manager.data_status] - 📊 本地数据库路径: /root/yanran_digital_life/data/digital_life.db
2025-09-18 14:52:39,815 - Digital - [utilities.storage_manager.success] - ✅ 数据表创建或验证成功
2025-09-18 14:52:39,815 - Digital - [utilities.storage_manager.success] - ✅ 已连接到本地SQLite数据库
2025-09-18 14:52:40,145 - Digital - [utilities.storage_manager.success] - ✅ 已成功连接到MySQL数据库: **************:3306/linyanran
2025-09-18 14:52:40,287 - Digital - [utilities.storage_manager.success] - ✅ 已连接到远程MySQL数据库: **************:3306
2025-09-18 14:52:40,287 - Digital - [utilities.storage_manager.data_status] - 📊 向量数据库未启用
2025-09-18 14:52:40,287 - Digital - [utilities.storage_manager.success] - ✅ 存储管理器已初始化
2025-09-18 14:52:40,287 - Digital - [health_checker.success] - ✅ 系统健康检查完成，状态: critical
2025-09-18 14:52:40,287 - Digital - [main.success] - ✅ ✅ 健康检查未发现模拟数据问题
2025-09-18 14:52:40,287 - Digital - [main.success] - ✅ ✅ 健康检查系统激活成功
2025-09-18 14:52:40,287 - Digital - [main._activate_autonomous_systems] - ℹ️  🔧 系统协调器未在singleton_manager中找到，尝试创建...
2025-09-18 14:52:40,303 - Digital - [enhanced_event_bus_v2.success] - ✅ 增强型事件总线 V2 初始化完成
2025-09-18 14:52:40,304 - Digital - [enhanced_event_bus_v2.success] - ✅ 事件总线已启动
2025-09-18 14:52:40,305 - Digital - [data_flow_manager.register_flow_chain] - ℹ️  注册数据流链: hot_topics_processing -> ['intelligent_scheduler', 'trend_perception', 'trend_intelligence']
2025-09-18 14:52:40,305 - Digital - [data_flow_manager.register_flow_chain] - ℹ️  注册数据流链: decision_enhancement -> ['autonomous_thinking', 'trend_analysis', 'decision_engine']
2025-09-18 14:52:40,305 - Digital - [data_flow_manager.register_flow_chain] - ℹ️  注册数据流链: emergency_response -> ['emergency_module', 'alert_system', 'recovery_system']
2025-09-18 14:52:40,305 - Digital - [system_coordinator.success] - ✅ 系统协调器初始化完成
2025-09-18 14:52:40,305 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'system_coordinator'
2025-09-18 14:52:40,305 - Digital - [main.success] - ✅ ✅ 系统协调器创建并注册成功
2025-09-18 14:52:40,305 - Digital - [system_coordinator.register_module] - ℹ️  注册模块: intelligence_integration_manager
2025-09-18 14:52:40,305 - Digital - [system_coordinator.register_module] - ℹ️  注册模块: enhanced_proactive_expression_organ
2025-09-18 14:52:40,306 - Digital - [system_coordinator.register_module] - ℹ️  注册模块: digital_life_intelligence_coordinator
2025-09-18 14:52:40,307 - Digital - [system_coordinator.register_module] - ℹ️  注册模块: perception_engine
2025-09-18 14:52:40,307 - Digital - [system_coordinator.register_module] - ℹ️  注册模块: datasource_manager
2025-09-18 14:52:40,308 - Digital - [system_coordinator.register_module] - ℹ️  注册模块: ai_enhanced_consciousness
2025-09-18 14:52:40,308 - Digital - [system_coordinator.register_module] - ℹ️  注册模块: neural_core
2025-09-18 14:52:40,308 - Digital - [system_coordinator.register_module] - ℹ️  注册模块: resilience
2025-09-18 14:52:40,310 - Digital - [system_coordinator.success] - ✅ 系统协调器已启动
2025-09-18 14:52:40,311 - Digital - [main.success] - ✅ ✅ 系统协调器激活成功，已注册8个核心模块
2025-09-18 14:52:40,385 - Digital - [main._activate_autonomous_systems] - ℹ️  📋 已配置模拟数据监控告警，监控关键词: ['random', 'simulate', 'mock', 'fake', '模拟', '随机']
2025-09-18 14:52:40,385 - Digital - [main.success] - ✅ ✅ 系统监控器激活成功
2025-09-18 14:52:40,386 - Digital - [main.success] - ✅ ✅ 自主清理配置已保存到: data/autonomous_cleanup_config.json
2025-09-18 14:52:40,386 - Digital - [main.success] - ✅ 🧠 老王：艹，开始组装高级智能学习零件！
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:40,392 - Digital - [intelligence.auto_tuning.success] - ✅ 初始化自动调参引擎...
2025-09-18 14:52:40,392 - Digital - [integrated_event_bus.success] - ✅ 集成事件总线初始化完成
2025-09-18 14:52:40,393 - Digital - [intelligence.auto_tuning._load_parameter_history] - ℹ️  已加载参数历史: 3 个参数
2025-09-18 14:52:40,393 - Digital - [intelligence.auto_tuning.success] - ✅ 自动调参引擎初始化完成
2025-09-18 14:52:40,393 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'auto_tuning'
2025-09-18 14:52:40,393 - Digital - [main.success] - ✅ ✅ 参数自动调优模块激活成功
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:40,400 - Digital - [feedback_learning._load_weights] - ℹ️  从 data/feedback_learning/weights.json 加载权重
2025-09-18 14:52:40,401 - Digital - [feedback_learning.success] - ✅ 反馈学习模块初始化完成
2025-09-18 14:52:40,402 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'feedback_learning'
2025-09-18 14:52:40,402 - Digital - [main.success] - ✅ ✅ 反馈学习模块激活成功
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:52:40,417 - Digital - [intelligence.behavior_learning.success] - ✅ 初始化行为学习系统...
2025-09-18 14:52:40,418 - Digital - [intelligence.learning_system.success] - ✅ 初始化学习系统...
2025-09-18 14:52:40,418 - Digital - [intelligence.learning_system.success] - ✅ 学习数据加载完成
2025-09-18 14:52:40,419 - Digital - [intelligence.learning_system.success] - ✅ 学习系统初始化完成
2025-09-18 14:52:40,419 - Digital - [intelligence.behavior_learning.success] - ✅ 行为数据加载完成
2025-09-18 14:52:40,420 - Digital - [intelligence.behavior_learning.success] - ✅ 行为学习系统初始化完成
2025-09-18 14:52:40,420 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'behavior_learning'
2025-09-18 14:52:40,420 - Digital - [main.success] - ✅ ✅ 行为学习模块激活成功
2025-09-18 14:52:40,420 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'learning_system'
2025-09-18 14:52:40,420 - Digital - [intelligence.learning_system.start_learning] - ℹ️  开始学习模式
2025-09-18 14:52:40,420 - Digital - [main.success] - ✅ ✅ 学习系统激活成功
2025-09-18 14:52:40,420 - Digital - [main.success] - ✅ 🎉 智能学习模块集群激活完成！所有高级零件已组装到汽车上！
2025-09-18 14:52:40,421 - Digital - [main.success] - ✅ 🎉 数字生命自主学习系统激活完成！
2025-09-18 14:52:40,421 - Digital - [main.success] - ✅ 🔥 初始化Enhanced Context Builder 2.0所需的新系统...
2025-09-18 14:52:40,421 - Digital - [main._init_new_systems] - ℹ️  🔧 初始化 通用调度器...
2025-09-18 14:52:40,454 - Digital - [main._init_new_systems] - ⚠️  ⚠️ MySQL连接器未初始化，跳过通用调度器
2025-09-18 14:52:40,455 - Digital - [main._init_new_systems] - ℹ️  🔧 初始化 情感权重系统...
2025-09-18 14:52:40,467 - Digital - [core.emotional_weight_system.success] - ✅ 情感关系权重配置加载成功: config/emotional_relationship_weights.json
2025-09-18 14:52:40,468 - Digital - [LazyComponentLoader.__init__] - ℹ️  懒加载组件管理器初始化完成
2025-09-18 14:52:40,468 - Digital - [PerformanceOptimizer.__init__] - ℹ️  性能优化器初始化完成
2025-09-18 14:52:40,469 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'performance_optimizer'
2025-09-18 14:52:40,469 - Digital - [core.emotional_weight_system.success] - ✅ 情感关系权重系统初始化完成
2025-09-18 14:52:40,469 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'emotional_weight_system'
2025-09-18 14:52:40,469 - Digital - [main.success] - ✅ ✅ 情感权重系统 初始化完成
2025-09-18 14:52:40,469 - Digital - [core.evolution.evolution_engine.start_evolution] - ℹ️  🧬 启动自主进化引擎 - 数字生命开始进化！
2025-09-18 14:52:41,471 - Digital - [core.evolution.evolution_engine._evolution_cycle] - ℹ️  🔄 开始第 7118 代进化
2025-09-18 14:52:41,629 - Digital - [wechat_friend_validator.success] - ✅ 🔐 MySQL连接初始化成功
2025-09-18 14:52:41,631 - Digital - [wechat_friend_validator.__init__] - ℹ️  🔐 WeChat好友验证器初始化完成 (缓存TTL: 300秒)
2025-09-18 14:52:41,631 - Digital - [wechat_message_pusher._init_wechat_client] - ℹ️  📤 正在初始化WeChat客户端: appid=wx_574bN70yW1q0vTSFaaSHU
2025-09-18 14:52:41,632 - Digital - [wechat_client.success] - ✅ ✅ 统一WeChat配置加载成功: config/wechat_config.json
2025-09-18 14:52:41,633 - Digital - [wechat_client.success] - ✅ WeChat消息客户端初始化完成: wx_574bN70yW1q0vTSFaaSHU
2025-09-18 14:52:41,634 - Digital - [wechat_message_pusher.success] - ✅ 📤 WeChat客户端初始化成功: wx_574bN70yW1q0vTSFaaSHU
2025-09-18 14:52:41,634 - Digital - [services.unified_websocket_service.__init__] - ℹ️  WebSocket服务初始化: 127.0.0.1:8766
2025-09-18 14:52:41,634 - Digital - [wechat_message_pusher.success] - ✅ 📤 WebSocket服务连接成功
2025-09-18 14:52:41,634 - Digital - [wechat_message_pusher.__init__] - ℹ️  📤 WeChat消息推送管理器初始化完成
2025-09-18 14:52:41,634 - Digital - [wechat_unified_push.success] - ✅ 📱 WeChat推送器初始化成功
2025-09-18 14:52:41,634 - Digital - [wechat_unified_push.success] - ✅ 📱 WeChat客户端状态正常
2025-09-18 14:52:41,635 - Digital - [wechat_unified_push.success] - ✅ 📱 好友验证器初始化成功
2025-09-18 14:52:41,643 - Digital - [wechat_humanized.__init__] - ℹ️  🤖 WeChat人性化交互控制器初始化完成
2025-09-18 14:52:41,677 - Digital - [intelligent_dispatch._load_user_profiles] - ℹ️  🧠 已加载 6 个用户行为档案
2025-09-18 14:52:41,678 - Digital - [intelligent_dispatch._init_neural_model] - ℹ️  🧠 已创建新的神经网络模型
2025-09-18 14:52:41,679 - Digital - [yanran_decision_engine._ensure_ai_adapter] - ℹ️  AI服务适配器获取成功
2025-09-18 14:52:41,686 - Digital - [yanran_decision_engine._load_decision_config] - ℹ️  加载决策配置成功: config/ai_decision/decision_engine.json
2025-09-18 14:52:41,692 - Digital - [yanran_decision_engine.__init__] - ℹ️  林嫣然AI决策引擎初始化完成
2025-09-18 14:52:41,692 - Digital - [OrganNeuralNetwork.__init__] - ℹ️  器官神经网络初始化完成
2025-09-18 14:52:41,693 - Digital - [core.neural_network.neural_core.success] - ✅ 初始化神经网络核心...
2025-09-18 14:52:41,702 - Digital - [core.neural_network.neural_core.success] - ✅ 神经网络核心初始化完成
2025-09-18 14:52:41,702 - Digital - [intelligent_dispatch._integrate_neural_engine] - ℹ️  🧠 已集成现有神经元自主学习引擎
2025-09-18 14:52:41,702 - Digital - [intelligent_dispatch.success] - ✅ 🧠 智能分发引擎组件初始化成功
2025-09-18 14:52:41,702 - Digital - [intelligent_dispatch.__init__] - ℹ️  🧠 WeChat智能分发引擎初始化完成
2025-09-18 14:52:41,717 - Digital - [load_balancer.__init__] - ℹ️  ⚖️ WeChat负载均衡管理器初始化完成
2025-09-18 14:52:41,718 - Digital - [wechat_unified_push.success] - ✅ 📱 WeChat统一推送服务组件初始化成功
2025-09-18 14:52:41,723 - Digital - [wechat_group_push.success] - ✅ 📊 WeChat群组推送服务组件初始化成功
2025-09-18 14:52:41,724 - Digital - [wechat_group_push.__init__] - ℹ️  📊 WeChat群组推送服务初始化完成
2025-09-18 14:52:41,735 - Digital - [utilities.cache_manager.success] - ✅ 🔥 初始化缓存管理器...
2025-09-18 14:52:41,748 - Digital - [utilities.cache_manager._start_cleanup_thread] - ℹ️  🧹 缓存清理线程已启动
2025-09-18 14:52:41,749 - Digital - [utilities.cache_manager.success] - ✅ ✅ 缓存管理器初始化完成
2025-09-18 14:52:42,498 - Digital - [main._init_new_systems] - ℹ️  🔧 初始化 生命体征模拟器...
2025-09-18 14:52:42,538 - Digital - [perception.physical_world.hardware_monitor.success] - ✅ 硬件监控器初始化完成
2025-09-18 14:52:42,538 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'hardware_monitor'
2025-09-18 14:52:42,538 - Digital - [main.success] - ✅ ✅ 硬件监控器（依赖）初始化完成
2025-09-18 14:52:42,539 - Digital - [perception.physical_world.vital_signs_simulator.success] - ✅ 生命体征模拟器初始化完成
2025-09-18 14:52:42,539 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'vital_signs_simulator'
2025-09-18 14:52:42,539 - Digital - [main.success] - ✅ ✅ 生命体征模拟器 初始化完成
2025-09-18 14:52:42,580 - Digital - [main._init_new_systems] - ℹ️  🔄 硬件监控器 已初始化，跳过
2025-09-18 14:52:42,580 - Digital - [main._init_new_systems] - ℹ️  🔧 初始化 延迟回复管理器...
2025-09-18 14:52:42,590 - Digital - [services.delayed_response_manager.success] - ✅ 🚀 初始化延迟响应管理器...
2025-09-18 14:52:42,590 - Digital - [services.delayed_response_manager.success] - ✅ ✅ 延迟响应管理器初始化完成
2025-09-18 14:52:42,590 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'delayed_response_manager'
2025-09-18 14:52:42,591 - Digital - [services.delayed_response_manager.success] - ✅ 🚀 启动延迟回复后台检查任务（同步版本）
2025-09-18 14:52:42,593 - Digital - [services.delayed_response_manager.success] - ✅ ✅ 延迟回复后台检查任务已在独立线程中启动（同步版本）
2025-09-18 14:52:42,593 - Digital - [main.success] - ✅ ✅ 延迟回复管理器后台检查任务已启动
2025-09-18 14:52:42,593 - Digital - [main.success] - ✅ ✅ 延迟回复管理器 初始化完成
2025-09-18 14:52:42,605 - Digital - [main.success] - ✅ 🔥 Enhanced Context Builder 2.0新系统组件初始化完成
2025-09-18 14:52:42,605 - Digital - [utilities.storage_manager.success] - ✅ 从统一配置管理器加载数据库配置，优先级: remote
2025-09-18 14:52:42,606 - Digital - [utilities.storage_manager.data_status] - 📊 本地数据库路径: /root/yanran_digital_life/data/digital_life.db
2025-09-18 14:52:42,606 - Digital - [utilities.storage_manager.success] - ✅ 数据表创建或验证成功
2025-09-18 14:52:42,607 - Digital - [utilities.storage_manager.success] - ✅ 已连接到本地SQLite数据库
2025-09-18 14:52:42,998 - Digital - [utilities.storage_manager.success] - ✅ 已成功连接到MySQL数据库: **************:3306/linyanran
2025-09-18 14:52:43,183 - Digital - [utilities.storage_manager.success] - ✅ 已连接到远程MySQL数据库: **************:3306
2025-09-18 14:52:43,183 - Digital - [utilities.storage_manager.data_status] - 📊 向量数据库未启用
2025-09-18 14:52:43,183 - Digital - [utilities.storage_manager.success] - ✅ 存储管理器已初始化
2025-09-18 14:52:43,184 - Digital - [utilities.storage_manager.data_status] - 📊 本地数据库路径: /root/yanran_digital_life/data/digital_life.db
2025-09-18 14:52:43,184 - Digital - [utilities.storage_manager.success] - ✅ 数据表创建或验证成功
2025-09-18 14:52:43,184 - Digital - [utilities.storage_manager.success] - ✅ 已连接到本地SQLite数据库
2025-09-18 14:52:43,184 - Digital - [utilities.storage_manager.warning_status] - ⚠️  不支持的远程数据库类型: 
2025-09-18 14:52:43,184 - Digital - [utilities.storage_manager.data_status] - 📊 向量数据库未启用
2025-09-18 14:52:43,185 - Digital - [main.success] - ✅ 🔗 MySQL连接器初始化完成并已注册到singleton_manager
2025-09-18 14:52:43,194 - Digital - [emotions_sync_integration.initialize] - ℹ️  🔧 初始化情感同步系统...
2025-09-18 14:52:43,194 - Digital - [emotions_user_sync_service._get_or_create_mysql_connector] - ℹ️  ✅ 从单例管理器获取MySQL连接器成功
2025-09-18 14:52:43,195 - Digital - [cognitive_modules.emotion.emotional_intelligence.success] - ✅ 成功加载统一情感等级标准
2025-09-18 14:52:43,195 - Digital - [cognitive_modules.emotion.emotional_intelligence.success] - ✅ 情感智能模块初始化完成
2025-09-18 14:52:43,196 - Digital - [cognitive.emotion_analysis.emotions_sync_analyzer.__init__] - ℹ️  模块 emotions_sync_analyzer 已创建
2025-09-18 14:52:43,196 - Digital - [cognitive.emotion.analyzer._initialize_dependencies] - ℹ️  ✅ 情感分析器已连接生命上下文
2025-09-18 14:52:43,196 - Digital - [cognitive.emotion.analyzer._initialize_dependencies] - ℹ️  ✅ 情感分析器已连接事件总线
2025-09-18 14:52:43,196 - Digital - [cognitive.emotion.analyzer.__init__] - ℹ️  情感分析模块已创建
2025-09-18 14:52:43,197 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'emotions_sync_service'
2025-09-18 14:52:43,197 - Digital - [emotions_sync_integration._initial_sync] - ℹ️  🔄 执行初始化情感同步...
2025-09-18 14:52:43,732 - Digital - [emotions_sync_integration._should_perform_full_sync] - ℹ️  📊 执行增量同步: users=627, emotions=627
2025-09-18 14:52:43,732 - Digital - [emotions_user_sync_service.incremental_sync] - ℹ️  🔄 开始增量用户情感同步...
2025-09-18 14:52:44,036 - Digital - [emotions_user_sync_service.incremental_sync] - ℹ️  📊 找到 29 个最近活跃用户
2025-09-18 14:53:04,957 - Digital - [emotions_user_sync_service.incremental_sync] - ℹ️  ✅ 增量同步完成: 处理2个用户, 耗时21.22s
2025-09-18 14:53:04,957 - Digital - [emotions_sync_integration._initial_sync] - ℹ️  ✅ 初始化增量同步成功: 处理29个用户
2025-09-18 14:53:04,957 - Digital - [emotions_user_sync_service.start_sync_service] - ℹ️  🚀 启动情感用户同步服务...
2025-09-18 14:53:04,957 - Digital - [emotions_user_sync_service.full_sync] - ℹ️  🔄 开始完整用户情感同步...
2025-09-18 14:53:05,349 - Digital - [emotions_user_sync_service.full_sync] - ℹ️  📊 找到 627 个用户需要检查
2025-09-18 14:53:05,595 - Digital - [openai._base_client._sleep_for_retry] - ℹ️  Retrying request to /chat/completions in 0.445754 seconds
2025-09-18 14:53:52,820 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: chatroom_56059199555 67 -> 53
2025-09-18 14:53:56,363 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: wxid_5855988557112 60 -> 85
2025-09-18 14:54:09,297 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: laijinleshiba 19 -> 27
2025-09-18 14:54:42,442 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: chatroom_38673471170 169 -> 174
2025-09-18 14:55:36,189 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: chatroom_47271504867 80 -> 64
2025-09-18 14:55:37,420 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: chatroom_55910613610 65 -> 49
2025-09-18 14:55:38,116 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: chatroom_34707037192 89 -> 78
2025-09-18 14:55:40,473 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: chatroom_49348779372 197 -> 185
2025-09-18 14:55:47,344 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: chatroom_46017412566 146 -> 123
2025-09-18 14:55:48,110 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: chatroom_26548224293 33 -> 27
2025-09-18 14:55:49,707 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: chatroom_43312340584 100 -> 85
2025-09-18 14:55:55,264 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: wxid_qddzv6gprb7p11 198 -> 204
2025-09-18 14:55:56,682 - Digital - [emotions_user_sync_service._update_existing_intensities] - ℹ️  🔄 更新情感强度: wxid_lf0l8pmc300421 50 -> 0
2025-09-18 14:55:56,682 - Digital - [emotions_user_sync_service.full_sync] - ℹ️  ✅ 完整同步完成: 创建0个, 更新13个, 错误0个, 耗时171.72s
2025-09-18 14:55:56,683 - Digital - [emotions_sync_integration.initialize] - ℹ️  ✅ 情感同步系统初始化完成
2025-09-18 14:55:56,683 - Digital - [main.success] - ✅ 💝 情感用户同步服务初始化完成
2025-09-18 14:55:56,683 - Digital - [main.success] - ✅ ✅ AI服务适配器已注册到singleton_manager
2025-09-18 14:55:56,683 - Digital - [main.success] - ✅ ✅ 思维链路已注册到singleton_manager
2025-09-18 14:55:56,683 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'event_bus'
2025-09-18 14:55:56,684 - Digital - [main.success] - ✅ ✅ 事件总线已注册到singleton_manager
2025-09-18 14:55:56,684 - Digital - [main.success] - ✅ ✅ 生命上下文已注册到singleton_manager
2025-09-18 14:55:56,684 - Digital - [main.success] - ✅ 🔧 核心组件注册完成
2025-09-18 14:55:56,685 - Digital - [main.success] - ✅ 📁 开始初始化数据持久化组件...
2025-09-18 14:55:56,690 - Digital - [core.user_preference_manager.success] - ✅ ✅ 已加载用户偏好，共 9 位用户
2025-09-18 14:55:56,691 - Digital - [core.user_preference_manager.success] - ✅ ✅ 用户偏好管理器初始化完成
2025-09-18 14:55:56,691 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'user_preference_manager'
2025-09-18 14:55:56,691 - Digital - [main.success] - ✅ ✅ 用户偏好管理器初始化成功
2025-09-18 14:55:56,692 - Digital - [cognitive.cognition.autonomous_decision_main.__init__] - ℹ️  模块 autonomous_decision_main 已创建
2025-09-18 14:55:56,692 - Digital - [cognitive.cognition.autonomous_decision.__init__] - ℹ️  自主决策模块 autonomous_decision_main 已创建
2025-09-18 14:55:56,692 - Digital - [cognitive.cognition.autonomous_decision.success] - ✅ 自主决策模块 autonomous_decision_main 内部初始化完成
2025-09-18 14:55:56,693 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'autonomous_decision'
2025-09-18 14:55:56,693 - Digital - [main.success] - ✅ ✅ 决策模块初始化成功
2025-09-18 14:55:56,693 - Digital - [main._init_emotion_persistence] - ℹ️  ℹ️ 情感模块未找到，跳过持久化配置
2025-09-18 14:55:56,693 - Digital - [main._init_memory_persistence] - ℹ️  ℹ️ 记忆模块未找到，跳过持久化配置
2025-09-18 14:55:56,693 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'feature_manager'
2025-09-18 14:55:56,700 - Digital - [main.success] - ✅ ✅ 功能特性管理器初始化成功
2025-09-18 14:55:56,702 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'error_recorder'
2025-09-18 14:55:56,704 - Digital - [main.success] - ✅ ✅ 错误记录系统初始化成功
2025-09-18 14:55:56,704 - Digital - [main._init_data_optimization_systems] - ℹ️  🔧 初始化数据优化系统...
2025-09-18 14:55:56,705 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'data_backup_manager'
2025-09-18 14:55:56,705 - Digital - [main.success] - ✅ ✅ 数据备份系统初始化成功
2025-09-18 14:55:56,706 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'data_integrity_checker'
2025-09-18 14:55:56,706 - Digital - [main.success] - ✅ ✅ 数据完整性检查器初始化成功
2025-09-18 14:55:56,707 - Digital - [main.success] - ✅ ✅ 数据优化系统初始化完成
2025-09-18 14:55:56,708 - Digital - [main.success] - ✅ 📁 数据持久化组件初始化完成
2025-09-18 14:55:56,708 - Digital - [main.success] - ✅ 初始化技能系统...
2025-09-18 14:55:56,708 - Digital - [singleton_manager.get_or_create] - ℹ️  通过工厂函数创建实例: 'skill_manager'
2025-09-18 14:55:56,708 - Digital - [skill_manager.success] - ✅ 初始化技能管理器...
2025-09-18 14:55:56,709 - Digital - [skill_manager.__init__] - ℹ️  技能管理器已注册事件处理器
2025-09-18 14:55:56,709 - Digital - [drawing_skill.success] - ✅ 初始化绘画技能...
2025-09-18 14:55:56,768 - Digital - [drawing_skill.success] - ✅ OpenAI配置初始化成功
2025-09-18 14:55:56,768 - Digital - [drawing_skill.success] - ✅ ✅ 从配置文件加载jimeng session_id: 1e6aa5b4800b56a3e345...
2025-09-18 14:55:56,768 - Digital - [drawing_skill.success] - ✅ 初始化绘画技能事件处理器
2025-09-18 14:55:56,768 - Digital - [drawing_skill._register_event_handlers] - ℹ️  已注册绘画请求事件处理器
2025-09-18 14:55:56,768 - Digital - [drawing_skill.success] - ✅ 绘画技能初始化完成: AI绘画技能 v2.0.0
2025-09-18 14:55:56,769 - Digital - [search_skill.success] - ✅ 初始化AI搜索技能...
2025-09-18 14:55:56,769 - Digital - [search_skill._load_config] - ℹ️  已加载搜索技能配置: /root/yanran_digital_life/config/skills/search_skill.json
2025-09-18 14:55:56,815 - Digital - [search_skill.success] - ✅ 搜索技能OpenAI配置初始化成功: https://oneapi.xiongmaodaxia.online/v1
2025-09-18 14:55:56,816 - Digital - [search_skill.__init__] - ℹ️  已加载搜索配置参数
2025-09-18 14:55:56,816 - Digital - [search_skill.success] - ✅ AI搜索技能初始化完成
2025-09-18 14:56:08,297 - Digital - [skill.openbb_financial_skill.__init__] - ℹ️  OpenBB金融技能已创建: OpenBB金融数据技能
2025-09-18 14:56:08,298 - Digital - [utilities.redis_history_manager.success] - ✅ Redis连接初始化成功
2025-09-18 14:56:08,299 - Digital - [skill_manager.success] - ✅ 助理提醒技能模块导入成功
2025-09-18 14:56:08,299 - Digital - [assistant_reminder_skill.success] - ✅ MySQL连接器初始化成功
2025-09-18 14:56:08,299 - Digital - [assistant_reminder_skill.success] - ✅ AI服务适配器初始化成功
2025-09-18 14:56:08,299 - Digital - [assistant_reminder_skill.success] - ✅ 配置加载成功
2025-09-18 14:56:08,303 - Digital - [ai_model_config_manager.success] - ✅ 加载AI模型配置成功，包含 14 个场景配置
2025-09-18 14:56:08,303 - Digital - [ai_model_config_manager.success] - ✅ AI模型配置管理器初始化完成
2025-09-18 14:56:08,303 - Digital - [assistant_reminder_skill.success] - ✅ AI模型配置管理器初始化成功
2025-09-18 14:56:08,519 - Digital - [assistant_reminder_skill.success] - ✅ 提醒任务表创建成功
2025-09-18 14:56:08,519 - Digital - [assistant_reminder_skill.success] - ✅ 提醒任务表创建/检查完成
2025-09-18 14:56:08,519 - Digital - [assistant_reminder_skill.success] - ✅ 助理提醒技能初始化完成: 智能助理提醒服务 (完整功能: MySQL=可用, AI=可用)
2025-09-18 14:56:08,519 - Digital - [skill_manager.success] - ✅ 助理提醒技能实例创建成功: AssistantReminderSkill
2025-09-18 14:56:08,520 - Digital - [skill_manager.success] - ✅ 助理提醒技能注册到技能管理器成功
2025-09-18 14:56:08,520 - Digital - [skill_manager.success] - ✅ 助理提醒技能意图映射添加成功，共8个意图
2025-09-18 14:56:08,520 - Digital - [skill_manager.success] - ✅ ✅ 助理提醒技能注册完成
2025-09-18 14:56:08,520 - Digital - [skill_manager.success] - ✅ ✅ 验证确认：助理提醒技能已在技能列表中
2025-09-18 14:56:08,520 - Digital - [skill_manager.success] - ✅ 技能管理器初始化完成
2025-09-18 14:56:08,522 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'skill_manager'
2025-09-18 14:56:08,522 - Digital - [main.success] - ✅ ✅ 统一技能管理器初始化完成
2025-09-18 14:56:08,522 - Digital - [main._init_skills] - ℹ️  检测到技能插件目录: /root/yanran_digital_life/plugins/skills
2025-09-18 14:56:08,522 - Digital - [main._init_skills] - ℹ️  ✅ 已添加技能插件目录到系统路径
2025-09-18 14:56:08,522 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'chat_skill'
2025-09-18 14:56:08,522 - Digital - [main.success] - ✅ ✅ 同步注册技能: chat_skill
2025-09-18 14:56:08,522 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'search_skill'
2025-09-18 14:56:08,522 - Digital - [main.success] - ✅ ✅ 同步注册技能: search_skill
2025-09-18 14:56:08,523 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'drawing_skill'
2025-09-18 14:56:08,523 - Digital - [main.success] - ✅ ✅ 同步注册技能: drawing_skill
2025-09-18 14:56:08,526 - Digital - [enhanced_activity_skill.__init__] - ℹ️  🎯 增强活动技能初始化完成
2025-09-18 14:56:08,527 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'enhanced_activity_skill'
2025-09-18 14:56:08,527 - Digital - [main.success] - ✅ ✅ 同步注册技能: enhanced_activity_skill
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:56:08,543 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'financial_data_skill'
2025-09-18 14:56:08,543 - Digital - [main.success] - ✅ ✅ 同步注册技能: financial_data_skill
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:56:08,550 - Digital - [plugins.skills.music_skill._load_config] - ℹ️  已从系统配置加载音乐API设置
2025-09-18 14:56:08,550 - Digital - [plugins.skills.music_skill.success] - ✅ 音乐技能初始化完成: 音乐技能 v1.0.0
2025-09-18 14:56:08,550 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'music_skill'
2025-09-18 14:56:08,550 - Digital - [main.success] - ✅ ✅ 同步注册技能: music_skill
2025-09-18 14:56:08,552 - Digital - [greeting_skill.success] - ✅ 问候技能初始化完成
2025-09-18 14:56:08,552 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'greeting_skill'
2025-09-18 14:56:08,553 - Digital - [main.success] - ✅ ✅ 同步注册技能: greeting_skill
2025-09-18 14:56:08,553 - Digital - [main.success] - ✅ 技能系统初始化完成
2025-09-18 14:56:08,553 - Digital - [main.success] - ✅ 初始化认知模块...
2025-09-18 14:56:08,553 - Digital - [main.success] - ✅ 🎯 预先初始化意图识别器...
2025-09-18 14:56:08,553 - Digital - [main.success] - ✅ 🎯 意图识别器已存在，跳过初始化
2025-09-18 14:56:08,553 - Digital - [main.success] - ✅ 初始化 感知引擎...
2025-09-18 14:56:08,553 - Digital - [perception_engine.success] - ✅ 初始化感知引擎...
2025-09-18 14:56:08,553 - Digital - [perception_engine.success] - ✅ 已连接事件总线
2025-09-18 14:56:08,554 - Digital - [cognitive.perception.enhanced_user_perception.__init__] - ℹ️  模块 enhanced_user_perception 已创建
2025-09-18 14:56:08,554 - Digital - [cognitive.perception.enhanced_user_perception.__init__] - ℹ️  增强版用户感知模块已创建
2025-09-18 14:56:08,554 - Digital - [perception_engine.success] - ✅ 用户感知模块加载成功
2025-09-18 14:56:08,554 - Digital - [environmental_perception.success] - ✅ 环境感知模块初始化完成
2025-09-18 14:56:08,554 - Digital - [perception_engine.success] - ✅ 环境感知模块加载成功
2025-09-18 14:56:08,588 - Digital - [perception.physical_world.hardware_monitor.success] - ✅ 硬件监控器初始化完成
2025-09-18 14:56:08,588 - Digital - [perception.physical_world.hardware_monitor.success] - ✅ 硬件监控已启动
2025-09-18 14:56:08,589 - Digital - [perception.physical_world.vital_signs_simulator.success] - ✅ 生命体征模拟器初始化完成
2025-09-18 14:56:08,603 - Digital - [services.script_integration_service.success] - ✅ 剧本集成服务初始化完成
2025-09-18 14:56:08,603 - Digital - [cognitive_modules.perception.activity_perception.success] - ✅ 活动感知模块初始化完成
2025-09-18 14:56:08,604 - Digital - [cognitive_modules.perception.activity_perception.get_instance] - ℹ️  ✅ 活动感知模块使用完整依赖项创建成功
2025-09-18 14:56:08,604 - Digital - [perception_engine.success] - ✅ 活动感知模块加载成功
2025-09-18 14:56:08,604 - Digital - [cognitive.perception.multimodal_perception.__init__] - ℹ️  模块 multimodal_perception 已创建
2025-09-18 14:56:08,604 - Digital - [cognitive.perception.multimodal_perception.__init__] - ℹ️  多模态感知模块 multimodal_perception 已创建
2025-09-18 14:56:08,604 - Digital - [perception_engine.success] - ✅ 多模态感知模块加载成功
2025-09-18 14:56:08,605 - Digital - [LifeOrgan.data_perception_organ.__init__] - ℹ️  器官 data_perception_organ 初始化完成: 智能数据连接和感知优化
2025-09-18 14:56:08,607 - Digital - [cognitive_modules.organs.data_perception_organ.__init__] - ℹ️  📊 林嫣然数据感知器官初始化开始...
2025-09-18 14:56:08,607 - Digital - [cognitive_modules.organs.data_perception_organ._init_data_perception] - ℹ️  📊 MySQL连接器连接成功
2025-09-18 14:56:08,607 - Digital - [real_time_data_collector.success] - ✅ 智能调度器初始化完成
2025-09-18 14:56:08,607 - Digital - [real_time_data_collector.success] - ✅ 趋势感知引擎初始化完成
2025-09-18 14:56:08,607 - Digital - [real_time_data_collector.success] - ✅ 智能分析引擎初始化完成
2025-09-18 14:56:08,608 - Digital - [real_time_data_collector.success] - ✅ 实时数据收集器初始化完成
2025-09-18 14:56:08,608 - Digital - [cognitive_modules.organs.data_perception_organ._init_data_perception] - ℹ️  📊 实时数据收集器连接成功
2025-09-18 14:56:08,608 - Digital - [cognitive_modules.organs.data_perception_organ._init_data_sources] - ℹ️  📊 初始化了 3 个数据源
2025-09-18 14:56:08,609 - Digital - [cognitive_modules.organs.data_perception_organ._start_perception_tasks] - ℹ️  📊 数据感知任务已启动（使用现有事件循环）
2025-09-18 14:56:08,609 - Digital - [cognitive_modules.organs.data_perception_organ.__init__] - ℹ️  📊 林嫣然数据感知器官初始化完成
2025-09-18 14:56:08,609 - Digital - [cognitive_modules.perception.data_perception._initialize_organ] - ℹ️  ✅ 创建新的数据感知器官实例
2025-09-18 14:56:08,609 - Digital - [cognitive_modules.perception.data_perception.success] - ✅ 数据感知模块初始化完成
2025-09-18 14:56:08,609 - Digital - [perception_engine.success] - ✅ 数据感知模块加载成功
2025-09-18 14:56:08,609 - Digital - [perception_engine.success] - ✅ 感知子模块加载完成，共加载 5 个模块
2025-09-18 14:56:08,610 - Digital - [perception_engine._start_perception_monitoring] - ℹ️  感知监控已启动
2025-09-18 14:56:08,610 - Digital - [perception_engine.success] - ✅ 感知引擎初始化完成
2025-09-18 14:56:08,611 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'perception'
2025-09-18 14:56:08,611 - Digital - [main.success] - ✅ ✅ 感知引擎 初始化成功
2025-09-18 14:56:08,637 - Digital - [core.evolution.evolution_engine._ai_analyze_needs] - ⚠️  AI返回内容中未找到有效的JSON格式
2025-09-18 14:56:08,637 - Digital - [core.evolution.evolution_engine._analyze_evolution_needs] - ℹ️  📊 进化需求分析: {'performance_improvement': 0, 'satisfaction_improvement': 0, 'stability_improvement': 0}
2025-09-18 14:56:08,637 - Digital - [main.success] - ✅ 初始化 情感引擎...
2025-09-18 14:56:08,638 - Digital - [emotion_engine.success] - ✅ 初始化情感引擎...
2025-09-18 14:56:08,638 - Digital - [emotion_engine.success] - ✅ 已连接事件总线
2025-09-18 14:56:08,638 - Digital - [emotion_engine.success] - ✅ 情感引擎初始化完成
2025-09-18 14:56:08,638 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'emotion_engine'
2025-09-18 14:56:08,638 - Digital - [main.success] - ✅ ✅ 情感引擎 初始化成功
2025-09-18 14:56:08,649 - Digital - [main.success] - ✅ 初始化 记忆管理器...
2025-09-18 14:56:08,650 - Digital - [memory_manager.success] - ✅ 初始化记忆管理器...
2025-09-18 14:56:08,650 - Digital - [memory_manager.success] - ✅ 已连接事件总线
2025-09-18 14:56:08,650 - Digital - [memory_manager.success] - ✅ 记忆管理器初始化完成
2025-09-18 14:56:08,650 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'memory'
2025-09-18 14:56:08,650 - Digital - [main.success] - ✅ ✅ 记忆管理器 初始化成功
2025-09-18 14:56:08,660 - Digital - [main.success] - ✅ 初始化 行为管理器...
2025-09-18 14:56:08,661 - Digital - [behavior_manager.success] - ✅ 初始化行为管理器...
2025-09-18 14:56:08,661 - Digital - [behavior_manager._load_behavior_templates] - ℹ️  已加载 3 个行为模板
2025-09-18 14:56:08,662 - Digital - [behavior_manager.success] - ✅ 已连接事件总线
2025-09-18 14:56:08,662 - Digital - [behavior_manager.success] - ✅ 行为管理器初始化完成
2025-09-18 14:56:08,662 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'behavior'
2025-09-18 14:56:08,662 - Digital - [main.success] - ✅ ✅ 行为管理器 初始化成功
2025-09-18 14:56:08,673 - Digital - [main.success] - ✅ 初始化 健康系统...
2025-09-18 14:56:08,673 - Digital - [cognitive.physiology.physiology.health_system.__init__] - ℹ️  模块 physiology.health_system 已创建
2025-09-18 14:56:08,674 - Digital - [cognitive.physiology.health_system.__init__] - ℹ️  健康系统模块已创建
2025-09-18 14:56:08,674 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'health_system'
2025-09-18 14:56:08,675 - Digital - [cognitive.physiology.physiology.health_system.success] - ✅ 模块 physiology.health_system 初始化成功
2025-09-18 14:56:08,675 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 健康系统 初始化成功
2025-09-18 14:56:08,675 - Digital - [cognitive.physiology.physiology.health_system.activate] - ℹ️  模块 physiology.health_system 已激活
2025-09-18 14:56:08,675 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 健康系统 已激活
2025-09-18 14:56:08,675 - Digital - [main.success] - ✅ ✅ 健康系统 初始化成功
2025-09-18 14:56:08,686 - Digital - [main.success] - ✅ 初始化 趋势感知模块...
2025-09-18 14:56:08,687 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'trend_perception'
2025-09-18 14:56:08,687 - Digital - [main.success] - ✅ ✅ 趋势感知模块 初始化成功
2025-09-18 14:56:08,698 - Digital - [main.success] - ✅ 初始化 趋势智能模块...
2025-09-18 14:56:08,698 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'trend_intelligence'
2025-09-18 14:56:08,698 - Digital - [main.success] - ✅ ✅ 趋势智能模块 初始化成功
2025-09-18 14:56:08,708 - Digital - [main.success] - ✅ 初始化 智能调度器...
2025-09-18 14:56:08,709 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'intelligent_scheduler'
2025-09-18 14:56:08,709 - Digital - [main.success] - ✅ ✅ 智能调度器 初始化成功
2025-09-18 14:56:08,720 - Digital - [main.success] - ✅ 初始化 嫣然AI决策引擎...
2025-09-18 14:56:08,720 - Digital - [yanran_decision_engine._ensure_ai_adapter] - ℹ️  AI服务适配器获取成功
2025-09-18 14:56:08,721 - Digital - [yanran_decision_engine._load_decision_config] - ℹ️  加载决策配置成功: config/ai_decision/decision_engine.json
2025-09-18 14:56:08,721 - Digital - [yanran_decision_engine.__init__] - ℹ️  林嫣然AI决策引擎初始化完成
2025-09-18 14:56:08,721 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'yanran_ai_decision_engine'
2025-09-18 14:56:08,721 - Digital - [main.success] - ✅ ✅ 嫣然AI决策引擎 初始化成功
2025-09-18 14:56:08,732 - Digital - [main.success] - ✅ 初始化 器官神经网络...
2025-09-18 14:56:08,732 - Digital - [OrganNeuralNetwork.__init__] - ℹ️  器官神经网络初始化完成
2025-09-18 14:56:08,732 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'organ_neural_network'
2025-09-18 14:56:08,732 - Digital - [main.success] - ✅ ✅ 器官神经网络 初始化成功
2025-09-18 14:56:08,743 - Digital - [main.success] - ✅ 初始化 世界感知器官...
2025-09-18 14:56:08,744 - Digital - [LifeOrgan.世界感知器官.__init__] - ℹ️  器官 世界感知器官 初始化完成: 感知外部世界变化，判断事件重要性
2025-09-18 14:56:08,744 - Digital - [utilities.database_manager.data_status] - 📊 已加载数据库配置: /root/yanran_digital_life/config/database.json
2025-09-18 14:56:08,745 - Digital - [utilities.database_manager.success] - ✅ MySQL连接器管理器初始化成功
2025-09-18 14:56:08,753 - Digital - [chroma_connector._init_connection] - ℹ️  强制使用远程ChromaDB模式
2025-09-18 14:56:08,753 - Digital - [chroma_connector.success] - ✅ 连接主ChromaDB服务器: http://**************:5001
2025-09-18 14:56:09,172 - Digital - [chroma_connector.success] - ✅ 使用HTTP API连接ChromaDB成功
2025-09-18 14:56:09,173 - Digital - [chroma_connector.success] - ✅ 成功创建ChromaDB HTTP客户端
2025-09-18 14:56:09,173 - Digital - [chroma_connector.success] - ✅ 成功连接到远程ChromaDB服务器
2025-09-18 14:56:09,173 - Digital - [utilities.database_manager.success] - ✅ ChromaDB连接器初始化成功
2025-09-18 14:56:09,173 - Digital - [utilities.database_manager.success] - ✅ 数据库管理器初始化完成
2025-09-18 14:56:09,173 - Digital - [WorldPerceptionDataManager.__init__] - ℹ️  ✅ 统一数据库管理器连接成功
2025-09-18 14:56:09,392 - Digital - [WorldPerceptionDataManager._ensure_tables_exist] - ℹ️  ✅ 世界感知数据表检查通过
2025-09-18 14:56:09,393 - Digital - [WorldPerceptionDataManager.__init__] - ℹ️  🧠 世界感知数据管理器初始化完成
2025-09-18 14:56:09,393 - Digital - [LifeOrgan.世界感知器官.__init__] - ℹ️  🧠 数据持久化管理器初始化成功
2025-09-18 14:56:09,397 - Digital - [utilities.redis_cache_adapter.__init__] - ℹ️  ✅ Redis缓存适配器连接成功: localhost:6379/0
2025-09-18 14:56:09,397 - Digital - [LifeOrgan.世界感知器官.__init__] - ℹ️  🌍 世界感知器官实例 #1 初始化完成
2025-09-18 14:56:09,397 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'world_perception_organ'
2025-09-18 14:56:09,397 - Digital - [main.success] - ✅ ✅ 世界感知器官 初始化成功
2025-09-18 14:56:09,408 - Digital - [main.success] - ✅ 初始化 创意表达器官...
2025-09-18 14:56:09,408 - Digital - [singleton_manager.get_or_create] - ℹ️  通过工厂函数创建实例: 'creative_expression_organ'
2025-09-18 14:56:09,409 - Digital - [LifeOrgan.creative_expression_organ.__init__] - ℹ️  器官 creative_expression_organ 初始化完成: 艺术创作和情感表达
2025-09-18 14:56:09,409 - Digital - [cognitive_modules.organs.creative_expression_organ.__init__] - ℹ️  🎨 林嫣然创作表达器官初始化开始...
2025-09-18 14:56:09,409 - Digital - [cognitive_modules.organs.creative_expression_organ._init_creative_tools] - ℹ️  🎨 绘画技能连接成功
🔥 检测到生产环境，自动设置日志级别为INFO
✅ 使用配置文件中的cookie
2025-09-18 14:56:09,502 - Digital - [cognitive_modules.organs.creative_expression_organ._init_kling_api] - ℹ️  🎨 可灵模块直接导入成功
2025-09-18 14:56:09,507 - Digital - [fake_useragent.getBrowser] - ⚠️  Error occurred during getting browser(s): random, but was suppressed with fallback.
Call daily login success with True:
{'status': 200, 'result': 1, 'message': '', 'data': {'order': None, 'status': 
'GRANTED', 'activityType': 'normal_bonus_monthly', 'rewardDetail': None, 
'firstApply': False, 'nextApplyTime': 1758867829609, 'lastAppliedActivity': 
'normal_bonus_monthly', 'hint': {'title': '您已领取过本月惊喜体验包', 
'subtitle': '下次可领取时间：2025-09-26 14:23:49', 'imgUrl': ''}, 
'applyCheckPack': None}, 'timestamp': 1758178569586}

2025-09-18 14:56:09,596 - Digital - [cognitive_modules.organs.creative_expression_organ._init_kling_api] - ℹ️  🎨 可灵AI接口初始化成功
2025-09-18 14:56:09,596 - Digital - [cognitive_modules.organs.creative_expression_organ.__init__] - ℹ️  🎨 林嫣然创作表达器官初始化完成
2025-09-18 14:56:09,597 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'creative_expression_organ'
2025-09-18 14:56:09,597 - Digital - [main.success] - ✅ ✅ 创意表达器官 初始化成功
2025-09-18 14:56:09,609 - Digital - [main.success] - ✅ 初始化 安全保护器官...
2025-09-18 14:56:09,609 - Digital - [singleton_manager.get_or_create] - ℹ️  通过工厂函数创建实例: 'safety_protection_organ'
2025-09-18 14:56:09,609 - Digital - [LifeOrgan.safety_protection_organ.__init__] - ℹ️  器官 safety_protection_organ 初始化完成: 智能安全防护和边界管理
2025-09-18 14:56:09,610 - Digital - [cognitive_modules.organs.safety_protection_organ.__init__] - ℹ️  🛡️ 林嫣然安全防护器官初始化开始...
2025-09-18 14:56:09,620 - Digital - [security.response_filter.success] - ✅ 响应过滤配置加载完成
2025-09-18 14:56:09,620 - Digital - [adapters.unified_ai_adapter.success] - ✅ 初始化统一AI适配器
2025-09-18 14:56:09,620 - Digital - [adapters.unified_ai_adapter._initialize] - ℹ️  加载AI服务配置: config/ai_services.json
2025-09-18 14:56:09,621 - Digital - [adapters.unified_ai_adapter._initialize] - ℹ️  设置默认AI服务: openai
2025-09-18 14:56:09,621 - Digital - [adapters.unified_ai_adapter._initialize] - ℹ️  已加载AI服务: deepseek, openai, zhipu, qianwen, compatible_service
2025-09-18 14:56:09,621 - Digital - [adapters.unified_ai_adapter.success] - ✅ 统一AI适配器已初始化
2025-09-18 14:56:09,621 - Digital - [security.response_filter.success] - ✅ AI适配器初始化完成
2025-09-18 14:56:09,621 - Digital - [security.response_filter._load_security_patterns] - ℹ️  加载了 6 个安全模式
2025-09-18 14:56:09,748 - Digital - [security.plugins.intimacy_analyzer.success] - ✅ 亲密度规则配置加载完成
2025-09-18 14:56:10,225 - Digital - [security.plugins.intimacy_analyzer.success] - ✅ 亲密度分析器插件初始化成功
2025-09-18 14:56:10,228 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'intimacy_analyzer'
2025-09-18 14:56:10,228 - Digital - [security.response_filter.success] - ✅ 亲密度分析器插件加载成功
2025-09-18 14:56:10,228 - Digital - [security.response_filter.success] - ✅ 响应过滤器初始化完成
2025-09-18 14:56:10,228 - Digital - [cognitive_modules.organs.safety_protection_organ._init_safety_components] - ℹ️  🛡️ 基础安全过滤器连接成功
2025-09-18 14:56:10,228 - Digital - [cognitive_modules.organs.safety_protection_organ._init_safety_components] - ℹ️  🛡️ 亲密度分析器连接成功
2025-09-18 14:56:10,229 - Digital - [cognitive_modules.organs.safety_protection_organ.__init__] - ℹ️  🛡️ 林嫣然安全防护器官初始化完成
2025-09-18 14:56:10,229 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'safety_protection_organ'
2025-09-18 14:56:10,229 - Digital - [main.success] - ✅ ✅ 安全保护器官 初始化成功
2025-09-18 14:56:10,240 - Digital - [main.success] - ✅ 初始化 技能协调器官...
2025-09-18 14:56:10,241 - Digital - [singleton_manager.get_or_create] - ℹ️  通过工厂函数创建实例: 'skill_coordination_organ'
2025-09-18 14:56:10,243 - Digital - [LifeOrgan.skill_coordination_organ.__init__] - ℹ️  器官 skill_coordination_organ 初始化完成: 智能技能协调和优化
2025-09-18 14:56:10,245 - Digital - [cognitive_modules.organs.skill_coordination_organ.__init__] - ℹ️  🧠 林嫣然技能协调器官初始化开始...
2025-09-18 14:56:10,245 - Digital - [cognitive_modules.organs.skill_coordination_organ._discover_available_skills] - ℹ️  🧠 发现 9 个可用技能
2025-09-18 14:56:10,245 - Digital - [cognitive_modules.organs.skill_coordination_organ._init_skill_coordination] - ℹ️  🧠 技能管理器连接成功
2025-09-18 14:56:10,246 - Digital - [cognitive_modules.organs.skill_coordination_organ._create_default_combinations] - ℹ️  🧠 创建了默认技能组合方案
2025-09-18 14:56:10,246 - Digital - [cognitive_modules.organs.skill_coordination_organ.__init__] - ℹ️  🧠 林嫣然技能协调器官初始化完成
2025-09-18 14:56:10,248 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'skill_coordination_organ'
2025-09-18 14:56:10,249 - Digital - [main.success] - ✅ ✅ 技能协调器官 初始化成功
2025-09-18 14:56:10,264 - Digital - [main.success] - ✅ 初始化 财富管理器官...
2025-09-18 14:56:10,265 - Digital - [singleton_manager.get_or_create] - ℹ️  通过工厂函数创建实例: 'wealth_management_organ'
2025-09-18 14:56:10,267 - Digital - [LifeOrgan.wealth_management_organ.__init__] - ℹ️  器官 wealth_management_organ 初始化完成: 智能投资理财和财富规划
2025-09-18 14:56:10,272 - Digital - [cognitive_modules.organs.wealth_management_organ.__init__] - ℹ️  💰 林嫣然财富管理器官初始化开始...
2025-09-18 14:56:10,272 - Digital - [cognitive_modules.organs.wealth_management_organ._init_wealth_management] - ℹ️  💰 金融数据技能连接成功
2025-09-18 14:56:10,272 - Digital - [cognitive_modules.organs.wealth_management_organ._init_risk_profile] - ℹ️  💰 风险偏好配置完成: moderate
2025-09-18 14:56:10,273 - Digital - [cognitive_modules.organs.wealth_management_organ._load_portfolio_data] - ℹ️  💰 投资组合数据文件不存在，初始化为空投资组合
2025-09-18 14:56:10,273 - Digital - [cognitive_modules.organs.wealth_management_organ._load_portfolio_data] - ℹ️  💰 请通过API或数据导入功能添加真实投资数据
2025-09-18 14:56:10,273 - Digital - [cognitive_modules.organs.wealth_management_organ.__init__] - ℹ️  💰 林嫣然财富管理器官初始化完成
2025-09-18 14:56:10,275 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'wealth_management_organ'
2025-09-18 14:56:10,276 - Digital - [main.success] - ✅ ✅ 财富管理器官 初始化成功
2025-09-18 14:56:10,288 - Digital - [main.success] - ✅ 初始化 数据感知器官...
2025-09-18 14:56:10,289 - Digital - [singleton_manager.get_or_create] - ℹ️  通过工厂函数创建实例: 'data_perception_organ'
2025-09-18 14:56:10,289 - Digital - [LifeOrgan.data_perception_organ.__init__] - ℹ️  器官 data_perception_organ 初始化完成: 智能数据连接和感知优化
2025-09-18 14:56:10,290 - Digital - [cognitive_modules.organs.data_perception_organ.__init__] - ℹ️  📊 林嫣然数据感知器官初始化开始...
2025-09-18 14:56:10,290 - Digital - [cognitive_modules.organs.data_perception_organ._init_data_perception] - ℹ️  📊 MySQL连接器连接成功
2025-09-18 14:56:10,290 - Digital - [cognitive_modules.organs.data_perception_organ._init_data_perception] - ℹ️  📊 实时数据收集器连接成功
2025-09-18 14:56:10,290 - Digital - [cognitive_modules.organs.data_perception_organ._init_data_sources] - ℹ️  📊 初始化了 3 个数据源
2025-09-18 14:56:10,291 - Digital - [cognitive_modules.organs.data_perception_organ._start_perception_tasks] - ℹ️  📊 数据感知任务已启动（使用现有事件循环）
2025-09-18 14:56:10,291 - Digital - [cognitive_modules.organs.data_perception_organ.__init__] - ℹ️  📊 林嫣然数据感知器官初始化完成
2025-09-18 14:56:10,292 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'data_perception_organ'
2025-09-18 14:56:10,293 - Digital - [main.success] - ✅ ✅ 数据感知器官 初始化成功
2025-09-18 14:56:10,305 - Digital - [main.success] - ✅ 初始化 主动表达器官（增强版本的基础器官）...
2025-09-18 14:56:10,305 - Digital - [singleton_manager.get_or_create] - ℹ️  通过工厂函数创建实例: 'proactive_expression_organ'
2025-09-18 14:56:10,305 - Digital - [LifeOrgan.proactive_expression_organ.__init__] - ℹ️  器官 proactive_expression_organ 初始化完成: 主动表达和情感分享
2025-09-18 14:56:10,306 - Digital - [ProactiveExpressionOrgan.__init__] - ℹ️  💬 林嫣然主动表达器官初始化开始...
2025-09-18 14:56:10,329 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'proactive_expression_service'
2025-09-18 14:56:10,329 - Digital - [ProactiveExpressionOrgan._init_expression_system] - ℹ️  💬 主动表达服务已创建并注册
2025-09-18 14:56:10,329 - Digital - [ProactiveExpressionOrgan._init_expression_system] - ℹ️  💬 主动表达服务连接成功
2025-09-18 14:56:10,329 - Digital - [ProactiveExpressionOrgan._init_expression_system] - ℹ️  💬 使用WeChat统一推送服务，不启动WebSocket服务器
2025-09-18 14:56:10,340 - Digital - [PerceptionFeedbackProcessor.__init__] - ℹ️  感知反馈处理器初始化完成
2025-09-18 14:56:10,341 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'perception_feedback_processor'
2025-09-18 14:56:10,341 - Digital - [ProactiveExpressionOrgan._init_expression_system] - ℹ️  💬 感知反馈处理器连接成功
2025-09-18 14:56:10,341 - Digital - [ProactiveExpressionOrgan._register_perception_callback] - ℹ️  💬 感知结果回调已注册
2025-09-18 14:56:10,354 - Digital - [unified_user_manager.__init__] - ℹ️  统一用户身份管理器初始化完成
2025-09-18 14:56:10,355 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'unified_user_manager'
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:56:10,363 - Digital - [middleware.caching.success] - ✅ Redis缓存后端初始化完成，连接到: localhost:6379/0
2025-09-18 14:56:10,363 - Digital - [ContactsManager._init_redis] - ℹ️  联系人管理器Redis缓存初始化成功
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:56:10,369 - Digital - [adapters.database_sync_manager.success] - ✅ 数据库同步管理器MySQL连接初始化成功
2025-09-18 14:56:10,369 - Digital - [adapters.database_sync_manager.success] - ✅ 数据库同步服务已启动
2025-09-18 14:56:10,370 - Digital - [ContactsManager._init_database_sync] - ℹ️  联系人管理器数据库同步初始化成功
2025-09-18 14:56:10,378 - Digital - [ContactsManager._init_file_watcher] - ℹ️  联系人文件监听器初始化成功
2025-09-18 14:56:10,380 - Digital - [ContactsManager._load_contacts] - ℹ️  加载联系人数据: 63个用户
2025-09-18 14:56:10,383 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'contacts_manager'
2025-09-18 14:56:10,383 - Digital - [unified_user_manager._integrate_existing_managers] - ℹ️  已集成现有管理器
2025-09-18 14:56:10,383 - Digital - [unified_user_manager.start] - ℹ️  统一用户身份管理器启动成功
2025-09-18 14:56:10,383 - Digital - [ProactiveExpressionOrgan._init_expression_system] - ℹ️  💬 统一好友管理器连接成功
2025-09-18 14:56:10,384 - Digital - [cognitive_modules.emotion.autonomous_emotion_manager.success] - ✅ 成功加载统一情感等级标准
2025-09-18 14:56:10,384 - Digital - [cognitive_modules.emotion.autonomous_emotion_manager.success] - ✅ 自主情感管理器初始化完成
2025-09-18 14:56:10,384 - Digital - [cognitive_modules.emotion.emotion_system_integration.success] - ✅ 情感系统集成器初始化完成
2025-09-18 14:56:10,384 - Digital - [ProactiveExpressionOrgan._init_expression_system] - ℹ️  💬 情感系统集成连接成功
2025-09-18 14:56:10,392 - Digital - [workday_cache_manager.__init__] - ℹ️  🗓️ 工作日缓存管理器初始化完成
2025-09-18 14:56:10,392 - Digital - [workday_cache_manager.is_workday_cached] - ℹ️  🗓️ 🔍 缓存未命中，查询数据库: 2025-09-19
2025-09-18 14:56:10,393 - Digital - [workday_service.success] - ✅ 统一工作日服务初始化完成
2025-09-18 14:56:10,394 - Digital - [workday_service.start_auto_refresh] - ℹ️  工作日服务自动刷新线程已启动
2025-09-18 14:56:10,395 - Digital - [workday_cache_manager.success] - ✅ 🗓️ ✅ 工作日信息已缓存: 2025-09-19 -> True
2025-09-18 14:56:10,395 - Digital - [ProactiveExpressionOrgan._init_expression_system] - ℹ️  💬 🗓️ 工作日缓存预加载完成
2025-09-18 14:56:10,395 - Digital - [ProactiveExpressionOrgan._init_expression_system] - ℹ️  💬 ⏳ 表达监控任务将在器官激活时启动
2025-09-18 14:56:10,396 - Digital - [ProactiveExpressionOrgan.__init__] - ℹ️  💬 林嫣然主动表达器官初始化完成
2025-09-18 14:56:10,396 - Digital - [ProactiveExpressionOrgan.__init__] - ℹ️  💬 🧠 当前触发模式: PARALLEL
2025-09-18 14:56:10,397 - Digital - [ProactiveExpressionOrgan.__init__] - ℹ️  💬 🧠 并行配置: 最大并发=2, 最小间隔=5分钟
2025-09-18 14:56:10,397 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'proactive_expression_organ'
2025-09-18 14:56:10,398 - Digital - [ProactiveExpressionOrgan.activate] - ℹ️  💬 🚀 主动表达器官正在激活...
2025-09-18 14:56:10,398 - Digital - [ProactiveExpressionOrgan._start_expression_monitoring] - ℹ️  💬 🚀 启动统一表达监控系统...
2025-09-18 14:56:10,399 - Digital - [ProactiveExpressionOrgan.timer_monitoring] - ℹ️  💬 🚀 定时器监控线程启动，等待系统完全启动...
2025-09-18 14:56:10,399 - Digital - [ProactiveExpressionOrgan.success] - ✅ 💬 ✅ 基于定时器的表达监控已启动（主要模式）
2025-09-18 14:56:10,400 - Digital - [ProactiveExpressionOrgan.timer_monitoring] - ℹ️  💬 ⏰ 等待系统启动完成，预计等待 60 秒...
2025-09-18 14:56:10,400 - Digital - [ProactiveExpressionOrgan.success] - ✅ 💬 ✅ 表达监控系统启动成功
2025-09-18 14:56:10,401 - Digital - [ProactiveExpressionOrgan.success] - ✅ 💬 ✅ 主动表达器官激活成功，监控系统已启动
2025-09-18 14:56:10,401 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 主动表达器官（增强版本的基础器官） 已激活
2025-09-18 14:56:10,402 - Digital - [main.success] - ✅ ✅ 主动表达器官（增强版本的基础器官） 初始化成功
2025-09-18 14:56:10,414 - Digital - [main.success] - ✅ 初始化 增强版主动表达器官...
2025-09-18 14:56:10,414 - Digital - [cognitive_modules.organs.enhanced_proactive_expression_organ.success] - ✅ 🚀 初始化增强版主动表达器官 2.0...
2025-09-18 14:56:10,440 - Digital - [IntelligenceIntegrationManager._initialize_base_components] - ℹ️  🔧 基础组件初始化完成
2025-09-18 14:56:10,440 - Digital - [IntelligenceIntegrationManager._initialize_neural_systems] - ℹ️  🧠 神经网络系统初始化完成: 3个
2025-09-18 14:56:10,441 - Digital - [IntelligenceIntegrationManager._initialize_learning_modules] - ℹ️  📚 学习模块初始化完成: 3个
2025-09-18 14:56:10,441 - Digital - [resilience_system.register_service] - ℹ️  已注册服务: intelligence_integration_manager (类型: cognitive)
2025-09-18 14:56:10,442 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 智能整合管理器已注册到韧性系统
2025-09-18 14:56:10,442 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 智能整合管理器初始化完成
2025-09-18 14:56:10,442 - Digital - [cognitive_modules.organs.enhanced_proactive_expression_organ.success] - ✅ ✅ 智能整合管理器深度连接成功
2025-09-18 14:56:10,443 - Digital - [cognitive_modules.organs.enhanced_proactive_expression_organ._initialize_intelligence_manager] - ℹ️  🧠 当前全局智能水平: 0.000
2025-09-18 14:56:10,443 - Digital - [cognitive_modules.organs.enhanced_proactive_expression_organ._initialize_intelligence_manager] - ℹ️  💓 当前生命活力: 0.000
2025-09-18 14:56:10,443 - Digital - [cognitive_modules.organs.enhanced_proactive_expression_organ.success] - ✅ ✅ 基础主动表达器官连接成功
2025-09-18 14:56:10,444 - Digital - [cognitive_modules.organs.enhanced_proactive_expression_organ._start_performance_monitoring] - ℹ️  📊 性能监控已启动
2025-09-18 14:56:10,445 - Digital - [cognitive_modules.organs.enhanced_proactive_expression_organ._start_continuous_optimization] - ℹ️  🔧 持续优化已启动
2025-09-18 14:56:10,445 - Digital - [cognitive_modules.organs.enhanced_proactive_expression_organ.success] - ✅ ✅ 增强版主动表达器官 2.0 初始化完成
2025-09-18 14:56:10,446 - Digital - [main._init_cognitive_modules] - ℹ️  🔍 验证enhanced_proactive_expression_organ实例: 类型=<class 'cognitive_modules.organs.enhanced_proactive_expression_organ.EnhancedProactiveExpressionOrgan'>
2025-09-18 14:56:10,446 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ enhanced_proactive_expression_organ有trigger_expression方法
2025-09-18 14:56:10,446 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ enhanced_proactive_expression_organ有enhanced_proactive_expression方法
2025-09-18 14:56:10,446 - Digital - [main.success] - ✅ ✅ enhanced_proactive_expression_organ找到兼容方法: ['trigger_expression', 'enhanced_proactive_expression']
2025-09-18 14:56:10,447 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'enhanced_proactive_expression_organ'
2025-09-18 14:56:10,448 - Digital - [main.success] - ✅ ✅ 增强版主动表达器官 初始化成功
2025-09-18 14:56:10,460 - Digital - [main.success] - ✅ 初始化 关系协调器官...
2025-09-18 14:56:10,461 - Digital - [singleton_manager.get_or_create] - ℹ️  通过工厂函数创建实例: 'relationship_coordination_organ'
2025-09-18 14:56:10,462 - Digital - [LifeOrgan.relationship_coordination_organ.__init__] - ℹ️  器官 relationship_coordination_organ 初始化完成: 跨模块协同和关系协调
2025-09-18 14:56:10,464 - Digital - [cognitive_modules.organs.relationship_coordination_organ.__init__] - ℹ️  🤝 林嫣然关系协调器官初始化开始...
2025-09-18 14:56:10,465 - Digital - [cognitive_modules.organs.relationship_coordination_organ._init_coordination_system] - ℹ️  🤝 器官神经网络连接成功
2025-09-18 14:56:10,467 - Digital - [cognitive_modules.organs.relationship_coordination_organ._discover_organ_relationships] - ℹ️  🤝 发现了 56 个器官关系
2025-09-18 14:56:10,468 - Digital - [cognitive_modules.organs.relationship_coordination_organ._start_coordination_monitoring] - ℹ️  🤝 协调监控任务已启动
2025-09-18 14:56:10,470 - Digital - [cognitive_modules.organs.relationship_coordination_organ.__init__] - ℹ️  🤝 林嫣然关系协调器官初始化完成
2025-09-18 14:56:10,473 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'relationship_coordination_organ'
2025-09-18 14:56:10,474 - Digital - [main.success] - ✅ ✅ 关系协调器官 初始化成功
2025-09-18 14:56:10,486 - Digital - [main.success] - ✅ 初始化 个性化服务器官...
2025-09-18 14:56:10,486 - Digital - [singleton_manager.get_or_create] - ℹ️  通过工厂函数创建实例: 'personalized_service_organ'
2025-09-18 14:56:10,488 - Digital - [LifeOrgan.personalized_service_organ.__init__] - ℹ️  器官 personalized_service_organ 初始化完成: 个性化服务编排和用户体验优化
2025-09-18 14:56:10,499 - Digital - [cognitive_modules.organs.personalized_service_organ.__init__] - ℹ️  🎯 林嫣然个性化服务器官初始化开始...
2025-09-18 14:56:10,499 - Digital - [cognitive_modules.organs.personalized_service_organ._connect_to_organs] - ℹ️  🎯 已连接到 world_perception
2025-09-18 14:56:10,500 - Digital - [cognitive_modules.organs.personalized_service_organ._connect_to_organs] - ℹ️  🎯 已连接到 creative_expression
2025-09-18 14:56:10,502 - Digital - [cognitive_modules.organs.personalized_service_organ._connect_to_organs] - ℹ️  🎯 已连接到 safety_protection
2025-09-18 14:56:10,502 - Digital - [cognitive_modules.organs.personalized_service_organ._connect_to_organs] - ℹ️  🎯 已连接到 skill_coordination
2025-09-18 14:56:10,502 - Digital - [cognitive_modules.organs.personalized_service_organ._connect_to_organs] - ℹ️  🎯 已连接到 wealth_management
2025-09-18 14:56:10,502 - Digital - [cognitive_modules.organs.personalized_service_organ._connect_to_organs] - ℹ️  🎯 已连接到 data_perception
2025-09-18 14:56:10,502 - Digital - [cognitive_modules.organs.personalized_service_organ._connect_to_organs] - ℹ️  🎯 已连接到 proactive_expression
2025-09-18 14:56:10,502 - Digital - [cognitive_modules.organs.personalized_service_organ._connect_to_organs] - ℹ️  🎯 已连接到 relationship_coordination
2025-09-18 14:56:10,502 - Digital - [cognitive_modules.organs.personalized_service_organ._init_personalization_models] - ℹ️  🎯 个性化模型初始化完成
2025-09-18 14:56:10,503 - Digital - [cognitive_modules.organs.personalized_service_organ._start_service_monitoring] - ℹ️  🎯 服务监控任务已启动
2025-09-18 14:56:10,503 - Digital - [cognitive_modules.organs.personalized_service_organ.__init__] - ℹ️  🎯 林嫣然个性化服务器官初始化完成
2025-09-18 14:56:10,505 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'personalized_service_organ'
2025-09-18 14:56:10,506 - Digital - [main.success] - ✅ ✅ 个性化服务器官 初始化成功
2025-09-18 14:56:10,519 - Digital - [main.success] - ✅ 初始化 记忆整合模块...
2025-09-18 14:56:10,521 - Digital - [cognitive.记忆整合模块.default.__init__] - ℹ️  模块 default 已创建
2025-09-18 14:56:10,522 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'memory_integration'
2025-09-18 14:56:10,522 - Digital - [memory_integration.success] - ✅ 正在初始化记忆整合模块 (ID: default)...
2025-09-18 14:56:10,522 - Digital - [cognitive.记忆整合模块.default.success] - ✅ 模块 default 初始化成功
2025-09-18 14:56:10,523 - Digital - [utilities.storage_manager.success] - ✅ 从统一配置管理器加载数据库配置，优先级: remote
2025-09-18 14:56:10,523 - Digital - [utilities.storage_manager.data_status] - 📊 本地数据库路径: /root/yanran_digital_life/data/digital_life.db
2025-09-18 14:56:10,525 - Digital - [utilities.storage_manager.success] - ✅ 数据表创建或验证成功
2025-09-18 14:56:10,525 - Digital - [utilities.storage_manager.success] - ✅ 已连接到本地SQLite数据库
2025-09-18 14:56:11,070 - Digital - [adapters.database_sync_manager._analyze_sync_data] - ℹ️  分析结果: 新用户 0个, 需更新 0个
2025-09-18 14:56:11,207 - Digital - [utilities.storage_manager.success] - ✅ 已成功连接到MySQL数据库: **************:3306/linyanran
2025-09-18 14:56:11,350 - Digital - [utilities.storage_manager.success] - ✅ 已连接到远程MySQL数据库: **************:3306
2025-09-18 14:56:11,350 - Digital - [utilities.storage_manager.data_status] - 📊 向量数据库未启用
2025-09-18 14:56:11,350 - Digital - [utilities.storage_manager.success] - ✅ 存储管理器已初始化
2025-09-18 14:56:11,350 - Digital - [utilities.storage_manager.load_json] - ℹ️  成功加载JSON文件: /root/yanran_digital_life/memories/default/memory_associations.json
2025-09-18 14:56:11,350 - Digital - [memory_integration._load_associations] - ℹ️  已从存储加载记忆关联
2025-09-18 14:56:11,351 - Digital - [cognitive.情境记忆模块.default.__init__] - ℹ️  模块 default 已创建
2025-09-18 14:56:11,351 - Digital - [cognitive.程序记忆模块.default.__init__] - ℹ️  模块 default 已创建
2025-09-18 14:56:11,352 - Digital - [utilities.cache_manager.success] - ✅ 🔥 初始化缓存管理器...
2025-09-18 14:56:11,352 - Digital - [utilities.cache_manager._start_cleanup_thread] - ℹ️  🧹 缓存清理线程已启动
2025-09-18 14:56:11,353 - Digital - [utilities.cache_manager.success] - ✅ ✅ 缓存管理器初始化完成
2025-09-18 14:56:11,353 - Digital - [cognitive_modules.memory.semantic_memory.data_status] - 📊 按照老板要求，语义记忆只使用文件存储，不使用向量数据库
2025-09-18 14:56:11,353 - Digital - [cognitive_modules.memory.semantic_memory._initialize_vector_db] - ℹ️  已创建分类目录: 人物
2025-09-18 14:56:11,353 - Digital - [cognitive_modules.memory.semantic_memory._initialize_vector_db] - ℹ️  已创建分类目录: 地点
2025-09-18 14:56:11,353 - Digital - [cognitive_modules.memory.semantic_memory._initialize_vector_db] - ℹ️  已创建分类目录: 概念
2025-09-18 14:56:11,353 - Digital - [cognitive_modules.memory.semantic_memory._initialize_vector_db] - ℹ️  已创建分类目录: 事件
2025-09-18 14:56:11,353 - Digital - [cognitive_modules.memory.semantic_memory._initialize_vector_db] - ℹ️  已创建分类目录: 规则
2025-09-18 14:56:11,353 - Digital - [cognitive_modules.memory.semantic_memory._initialize_vector_db] - ℹ️  已创建分类目录: 常识
2025-09-18 14:56:11,353 - Digital - [cognitive_modules.memory.semantic_memory._initialize_vector_db] - ℹ️  已创建分类目录: 兴趣
2025-09-18 14:56:11,353 - Digital - [cognitive_modules.memory.semantic_memory._initialize_vector_db] - ℹ️  已创建分类目录: 关系
2025-09-18 14:56:11,353 - Digital - [cognitive_modules.memory.semantic_memory.success] - ✅ 文件存储初始化完成
2025-09-18 14:56:11,353 - Digital - [episodic_memory.success] - ✅ 正在初始化情境记忆模块 (ID: default)...
2025-09-18 14:56:11,353 - Digital - [cognitive.情境记忆模块.default.success] - ✅ 模块 default 初始化成功
2025-09-18 14:56:11,594 - Digital - [utilities.storage_manager.load_json] - ℹ️  成功加载JSON文件: /root/yanran_digital_life/memories/default/episodic_memories.json
2025-09-18 14:56:11,658 - Digital - [episodic_memory.success] - ✅ 记忆索引重建完成
2025-09-18 14:56:11,658 - Digital - [episodic_memory._load_memories] - ℹ️  已从存储加载 5397 条情境记忆
2025-09-18 14:56:11,659 - Digital - [episodic_memory.success] - ✅ 情境记忆模块初始化成功 (ID: default)
2025-09-18 14:56:11,659 - Digital - [procedural_memory.success] - ✅ 正在初始化程序记忆模块 (ID: default)...
2025-09-18 14:56:11,659 - Digital - [cognitive.程序记忆模块.default.success] - ✅ 模块 default 初始化成功
2025-09-18 14:56:11,659 - Digital - [utilities.storage_manager.load_json] - ℹ️  成功加载JSON文件: /root/yanran_digital_life/memories/default/procedural_skills.json
2025-09-18 14:56:11,660 - Digital - [procedural_memory.success] - ✅ 程序记忆模块初始化成功 (ID: default)
2025-09-18 14:56:11,660 - Digital - [memory_integration.success] - ✅ 记忆整合模块初始化成功 (ID: default)
2025-09-18 14:56:11,660 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 记忆整合模块 初始化成功
2025-09-18 14:56:11,660 - Digital - [cognitive.记忆整合模块.default.activate] - ℹ️  模块 default 已激活
2025-09-18 14:56:11,660 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 记忆整合模块 已激活
2025-09-18 14:56:11,661 - Digital - [main.success] - ✅ ✅ 记忆整合模块 初始化成功
2025-09-18 14:56:11,672 - Digital - [main.success] - ✅ 初始化 程序性记忆模块...
2025-09-18 14:56:11,672 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'procedural_memory'
2025-09-18 14:56:11,672 - Digital - [procedural_memory.success] - ✅ 正在初始化程序记忆模块 (ID: default)...
2025-09-18 14:56:11,672 - Digital - [cognitive.程序记忆模块.default.success] - ✅ 模块 default 初始化成功
2025-09-18 14:56:11,673 - Digital - [utilities.storage_manager.load_json] - ℹ️  成功加载JSON文件: /root/yanran_digital_life/memories/default/procedural_skills.json
2025-09-18 14:56:11,673 - Digital - [procedural_memory.success] - ✅ 程序记忆模块初始化成功 (ID: default)
2025-09-18 14:56:11,673 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 程序性记忆模块 初始化成功
2025-09-18 14:56:11,673 - Digital - [cognitive.程序记忆模块.default.activate] - ℹ️  模块 default 已激活
2025-09-18 14:56:11,673 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 程序性记忆模块 已激活
2025-09-18 14:56:11,673 - Digital - [main.success] - ✅ ✅ 程序性记忆模块 初始化成功
2025-09-18 14:56:11,684 - Digital - [main.success] - ✅ 初始化 AI增强意识系统...
2025-09-18 14:56:11,684 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'ai_enhanced_consciousness'
2025-09-18 14:56:11,684 - Digital - [core.ai_enhanced_consciousness.activate] - ℹ️  激活AI增强意识系统...
2025-09-18 14:56:11,684 - Digital - [core.ai_enhanced_consciousness.activate] - ℹ️  AI增强意识系统已激活
2025-09-18 14:56:11,684 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ AI增强意识系统 已激活
2025-09-18 14:56:11,684 - Digital - [main.success] - ✅ ✅ AI增强意识系统 初始化成功
2025-09-18 14:56:11,696 - Digital - [main.success] - ✅ 初始化 神经网络核心...
2025-09-18 14:56:11,697 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'neural_core'
2025-09-18 14:56:11,697 - Digital - [core.neural_network.neural_core.activate] - ℹ️  激活神经网络核心...
2025-09-18 14:56:11,697 - Digital - [core.neural_network.neural_core.activate] - ℹ️  神经网络核心已激活
2025-09-18 14:56:11,697 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 神经网络核心 已激活
2025-09-18 14:56:11,697 - Digital - [main.success] - ✅ ✅ 神经网络核心 初始化成功
2025-09-18 14:56:11,708 - Digital - [main.success] - ✅ 初始化 智能整合管理器...
2025-09-18 14:56:11,708 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'intelligence_integration_manager'
2025-09-18 14:56:11,708 - Digital - [main.success] - ✅ ✅ 智能整合管理器 初始化成功
2025-09-18 14:56:11,719 - Digital - [main.success] - ✅ 初始化 数字生命智能协调器...
2025-09-18 14:56:11,733 - Digital - [cognitive_modules.digital_life_intelligence_coordinator.success] - ✅ 🚀 初始化数字生命智能协调器...
2025-09-18 14:56:11,734 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._start_main_coordination_loop] - ℹ️  🔄 主协调循环已启动
2025-09-18 14:56:11,734 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._start_performance_monitoring_loop] - ℹ️  📊 性能监控循环已启动
2025-09-18 14:56:11,735 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._start_evolution_learning_loop] - ℹ️  🧬 进化学习循环已启动
2025-09-18 14:56:11,735 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._start_recovery_monitoring_loop] - ℹ️  🚨 故障恢复循环已启动
2025-09-18 14:56:11,735 - Digital - [cognitive_modules.digital_life_intelligence_coordinator.success] - ✅ ✅ 协调循环启动完成
2025-09-18 14:56:11,735 - Digital - [cognitive_modules.digital_life_intelligence_coordinator.success] - ✅ ✅ 数字生命智能协调器初始化完成
2025-09-18 14:56:11,735 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'digital_life_intelligence_coordinator'
2025-09-18 14:56:11,735 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._initialize_core_systems] - ℹ️  🔧 初始化核心系统...
2025-09-18 14:56:11,735 - Digital - [cognitive_modules.digital_life_intelligence_coordinator.success] - ✅ ✅ 智能整合管理器注册成功
2025-09-18 14:56:11,735 - Digital - [cognitive_modules.digital_life_intelligence_coordinator.success] - ✅ ✅ 增强版主动表达器官注册成功
2025-09-18 14:56:11,736 - Digital - [cognitive_modules.digital_life_intelligence_coordinator.success] - ✅ ✅ 神经网络系统注册完成: 3个
2025-09-18 14:56:11,736 - Digital - [cognitive_modules.digital_life_intelligence_coordinator.success] - ✅ ✅ 学习模块注册完成: 3个
2025-09-18 14:56:11,736 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._perform_initial_coordination] - ℹ️  🔄 执行初始协调...
2025-09-18 14:56:11,736 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._coordinate_intelligence_systems] - ℹ️  🧠 触发智能提升协调
2025-09-18 14:56:11,736 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._coordinate_intelligence_systems] - ℹ️  📚 强化学习协调
2025-09-18 14:56:11,736 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._perform_initial_optimization] - ℹ️  🔧 执行初始优化...
2025-09-18 14:56:11,736 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._perform_initial_optimization] - ⚠️  ⚠️ 系统性能较低，触发初始优化
2025-09-18 14:56:11,736 - Digital - [cognitive_modules.digital_life_intelligence_coordinator.success] - ✅ ✅ 初始协调完成
2025-09-18 14:56:11,736 - Digital - [cognitive_modules.digital_life_intelligence_coordinator.success] - ✅ ✅ 核心系统初始化完成
2025-09-18 14:56:11,736 - Digital - [main.success] - ✅ ✅ 数字生命智能协调器核心系统延迟初始化完成
2025-09-18 14:56:11,736 - Digital - [main.success] - ✅ ✅ 数字生命智能协调器 初始化成功
2025-09-18 14:56:11,748 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 系统协调器 已注册，跳过
2025-09-18 14:56:11,748 - Digital - [main.success] - ✅ 初始化 韧性自愈系统...
2025-09-18 14:56:11,748 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'resilience'
2025-09-18 14:56:11,748 - Digital - [resilience_system.activate] - ℹ️  激活韧性自愈系统...
2025-09-18 14:56:11,748 - Digital - [resilience_system.success] - ✅ 系统监控循环已启动
2025-09-18 14:56:11,749 - Digital - [resilience_system.success] - ✅ 已启动系统监控
2025-09-18 14:56:11,749 - Digital - [resilience_system.activate] - ℹ️  韧性自愈系统已激活
2025-09-18 14:56:11,749 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 韧性自愈系统 已激活
2025-09-18 14:56:11,749 - Digital - [main.success] - ✅ ✅ 韧性自愈系统 初始化成功
2025-09-18 14:56:11,760 - Digital - [main.success] - ✅ 初始化 感知引擎...
2025-09-18 14:56:11,760 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'perception_engine'
2025-09-18 14:56:11,760 - Digital - [main.success] - ✅ ✅ 感知引擎 初始化成功
2025-09-18 14:56:11,772 - Digital - [main.success] - ✅ 初始化 数据源管理器...
2025-09-18 14:56:11,776 - Digital - [datasource.news_connector.__init__] - ℹ️  🌍 新闻连接器初始化完成，API无需密钥
2025-09-18 14:56:11,776 - Digital - [integrations.datasource.success] - ✅ 新闻数据连接器初始化成功
2025-09-18 14:56:12,240 - Digital - [cognitive_modules.digital_life_intelligence_coordinator._coordinate_intelligence_systems] - ℹ️  🧠 触发智能提升协调
2025-09-18 14:56:12,879 - Digital - [datasource.akshare_connector.success] - ✅ AKShare金融数据连接器初始化成功
2025-09-18 14:56:12,879 - Digital - [integrations.datasource.success] - ✅ 金融数据连接器(AKShare)初始化成功
2025-09-18 14:56:12,879 - Digital - [integrations.datasource.success] - ✅ ✅ 已连接智能整合管理器
2025-09-18 14:56:12,879 - Digital - [integrations.datasource.success] - ✅ ✅ 已连接AI决策引擎
2025-09-18 14:56:12,879 - Digital - [integrations.datasource.success] - ✅ ✅ 已连接事件总线
2025-09-18 14:56:12,879 - Digital - [integrations.datasource.success] - ✅ 数据源管理器初始化完成
2025-09-18 14:56:12,879 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'datasource_manager'
2025-09-18 14:56:12,880 - Digital - [main.success] - ✅ ✅ 数据源管理器 初始化成功
2025-09-18 14:56:12,890 - Digital - [main.success] - ✅ 初始化 亲密度系统...
2025-09-18 14:56:12,896 - Digital - [security.plugins.high_performance_intimacy_provider.success] - ✅ 高性能亲密度数据提供器初始化完成
2025-09-18 14:56:12,897 - Digital - [main.success] - ✅ ✅ 亲密度系统 使用MySQL连接器初始化成功
2025-09-18 14:56:12,897 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'intimacy_provider'
2025-09-18 14:56:12,897 - Digital - [main.success] - ✅ ✅ 亲密度系统 初始化成功
2025-09-18 14:56:12,908 - Digital - [main.success] - ✅ 初始化 组件管理器...
2025-09-18 14:56:12,908 - Digital - [core.component_manager.success] - ✅ 初始化组件管理器...
2025-09-18 14:56:12,908 - Digital - [core.component_manager.success] - ✅ 组件管理器初始化完成
2025-09-18 14:56:12,909 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'component_manager'
2025-09-18 14:56:12,909 - Digital - [main.success] - ✅ ✅ 组件管理器 初始化成功
2025-09-18 14:56:12,919 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ AI增强意识系统 已注册，跳过
2025-09-18 14:56:12,919 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 性能优化器 已注册，跳过
2025-09-18 14:56:12,919 - Digital - [main.success] - ✅ 初始化 安全管理器...
2025-09-18 14:56:12,995 - Digital - [security.security_manager.__init__] - ℹ️  🛡️ 安全管理器初始化完成
2025-09-18 14:56:12,995 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'security_manager'
2025-09-18 14:56:12,995 - Digital - [main.success] - ✅ ✅ 安全管理器 初始化成功
2025-09-18 14:56:13,006 - Digital - [main.success] - ✅ 初始化 参数优化器...
2025-09-18 14:56:13,016 - Digital - [core.parameter_optimizer.__init__] - ℹ️  ⚙️ 参数优化器初始化完成
2025-09-18 14:56:13,016 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'parameter_optimizer'
2025-09-18 14:56:13,017 - Digital - [main.success] - ✅ ✅ 参数优化器 初始化成功
2025-09-18 14:56:13,027 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 技能管理器 已注册，跳过
2025-09-18 14:56:13,027 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ AI增强意识系统 已注册，跳过
2025-09-18 14:56:13,027 - Digital - [main.success] - ✅ 🎯 数字生命体核心组件初始化完成: 38/38 (100.0%)
2025-09-18 14:56:13,027 - Digital - [main._init_cognitive_modules] - ℹ️  🔧 开始向智能整合管理器注册核心模块...
2025-09-18 14:56:13,027 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 enhanced_proactive_expression_organ 注册成功
2025-09-18 14:56:13,027 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 digital_life_intelligence_coordinator 注册成功
2025-09-18 14:56:13,028 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 perception_engine 注册成功
2025-09-18 14:56:13,028 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 datasource_manager 注册成功
2025-09-18 14:56:13,028 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 ai_enhanced_consciousness 注册成功
2025-09-18 14:56:13,028 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 neural_core 注册成功
2025-09-18 14:56:13,028 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 resilience 注册成功
2025-09-18 14:56:13,028 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 vital_signs_simulator 注册成功
2025-09-18 14:56:13,028 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 thinking_chain 注册成功
2025-09-18 14:56:13,028 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 life_context 注册成功
2025-09-18 14:56:13,028 - Digital - [main.success] - ✅ ✅ 智能整合管理器模块注册完成: 10/10 个模块
2025-09-18 14:56:13,028 - Digital - [main._init_cognitive_modules] - ℹ️  🧠 全局智能水平: 0.850
2025-09-18 14:56:13,028 - Digital - [main._init_cognitive_modules] - ℹ️  💓 生命活力: 0.900
2025-09-18 14:56:13,028 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 intelligence_integration_manager 状态更新: initializing -> running
2025-09-18 14:56:13,028 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 enhanced_proactive_expression_organ 状态更新: initializing -> running
2025-09-18 14:56:13,029 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 digital_life_intelligence_coordinator 状态更新: initializing -> running
2025-09-18 14:56:13,030 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 intelligence_integration_manager 状态变化: running -> running
2025-09-18 14:56:13,030 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 perception_engine 状态更新: initializing -> running
2025-09-18 14:56:13,030 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 datasource_manager 状态更新: initializing -> running
2025-09-18 14:56:13,030 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 ai_enhanced_consciousness 状态更新: initializing -> running
2025-09-18 14:56:13,031 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 neural_core 状态更新: initializing -> running
2025-09-18 14:56:13,031 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 resilience 状态更新: initializing -> running
2025-09-18 14:56:13,031 - Digital - [main.success] - ✅ ✅ 系统协调器模块状态更新完成: 8个模块已设为RUNNING状态
2025-09-18 14:56:13,031 - Digital - [main.success] - ✅ ✅ 数字生命体核心组件初始化成功，生命特征完整性得到保障
2025-09-18 14:56:13,032 - Digital - [main.success] - ✅ 🧠 数字生命体核心初始化完成，使用统一的process_input接口
2025-09-18 14:56:13,032 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 enhanced_proactive_expression_organ 状态变化: running -> running
2025-09-18 14:56:13,033 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 perception_engine 状态变化: running -> running
2025-09-18 14:56:13,040 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 neural_core 状态变化: running -> running
2025-09-18 14:56:13,041 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 resilience 状态变化: running -> running
2025-09-18 14:56:13,045 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 ai_enhanced_consciousness 状态变化: running -> running
2025-09-18 14:56:13,050 - Digital - [core.enhanced_integrated_scheduler.success] - ✅ 增强版调度器配置加载成功: config/enhanced_scheduler_config.json
2025-09-18 14:56:13,050 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 digital_life_intelligence_coordinator 状态变化: running -> running
2025-09-18 14:56:13,051 - Digital - [core.enhanced_integrated_scheduler.success] - ✅ 正在初始化增强版集成调度器的所有服务...
2025-09-18 14:56:13,051 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 基础调度器初始化完成
2025-09-18 14:56:13,051 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 datasource_manager 状态变化: running -> running
2025-09-18 14:56:13,052 - Digital - [core.ai_service.success] - ✅ AI服务配置加载成功: config/ai_services.json
2025-09-18 14:56:13,096 - Digital - [core.ai_service.success] - ✅ OpenAI客户端配置完成
2025-09-18 14:56:13,097 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ AI服务初始化完成
2025-09-18 14:56:13,097 - Digital - [core.weather_service._load_config] - ℹ️  使用默认天气API配置
2025-09-18 14:56:13,097 - Digital - [core.weather_service.success] - ✅ 天气服务初始化完成
2025-09-18 14:56:13,097 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 天气服务初始化完成
2025-09-18 14:56:13,112 - Digital - [scripts_integration_service.__init__] - ℹ️  📊 Scripts表集成服务初始化完成
2025-09-18 14:56:13,112 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 新的Scripts集成服务初始化完成
2025-09-18 14:56:13,112 - Digital - [core.emotional_weight_system.success] - ✅ 情感关系权重配置加载成功: config/emotional_relationship_weights.json
2025-09-18 14:56:13,113 - Digital - [core.emotional_weight_system.success] - ✅ 情感关系权重系统初始化完成
2025-09-18 14:56:13,113 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 情感关系权重系统初始化完成
2025-09-18 14:56:13,113 - Digital - [core.enhanced_integrated_scheduler._initialize_services] - ℹ️    ⚠️ 活动感知模块将在依赖项初始化完成后创建
2025-09-18 14:56:13,113 - Digital - [core.enhanced_integrated_scheduler._initialize_services] - ℹ️    ⚠️ 活动执行器将在依赖项初始化完成后创建
2025-09-18 14:56:13,148 - Digital - [perception.physical_world.hardware_monitor.success] - ✅ 硬件监控器初始化完成
2025-09-18 14:56:13,148 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 硬件监控器初始化完成
2025-09-18 14:56:13,149 - Digital - [perception.physical_world.vital_signs_simulator.success] - ✅ 生命体征模拟器初始化完成
2025-09-18 14:56:13,149 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 生理信号模拟器初始化完成
2025-09-18 14:56:13,149 - Digital - [cognitive_modules.perception.activity_perception.success] - ✅ 活动感知模块初始化完成
2025-09-18 14:56:13,149 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 活动感知模块初始化完成
2025-09-18 14:56:13,150 - Digital - [cognitive.behavior.activity_executor.__init__] - ℹ️  模块 activity_executor 已创建
2025-09-18 14:56:13,150 - Digital - [cognitive_modules.behavior.activity_executor.success] - ✅ 活动执行器初始化完成
2025-09-18 14:56:13,151 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 活动执行器初始化完成
2025-09-18 14:56:13,151 - Digital - [core.enhanced_integrated_scheduler._initialize_services] - ℹ️    📋 设置默认调度任务...
2025-09-18 14:56:13,151 - Digital - [core.universal_scheduler.register_task] - ℹ️  任务已注册: scripts_integration - Scripts集成活动生成任务
2025-09-18 14:56:13,151 - Digital - [core.universal_scheduler.schedule_task] - ℹ️  任务已调度: scripts_integration - every_30_minutes
2025-09-18 14:56:13,151 - Digital - [core.universal_scheduler.success] - ✅ 📊 已注册新的Scripts集成活动生成任务，每30分钟执行
2025-09-18 14:56:13,160 - Digital - [autonomous_exploration_task.__init__] - ℹ️  🔍 自主探索调度任务初始化完成
2025-09-18 14:56:13,160 - Digital - [core.universal_scheduler.register_task] - ℹ️  任务已注册: autonomous_exploration - 自主探索任务
2025-09-18 14:56:13,160 - Digital - [core.universal_scheduler.schedule_task] - ℹ️  任务已调度: autonomous_exploration - every_2_hours
2025-09-18 14:56:13,161 - Digital - [core.universal_scheduler.success] - ✅ 🔍 已注册自主探索任务，每2小时执行话题发现和活动迭代
2025-09-18 14:56:13,166 - Digital - [daily_expression_reset_task.__init__] - ℹ️  💕 每日表达重置任务初始化完成，用户: linyanran
2025-09-18 14:56:13,166 - Digital - [core.universal_scheduler.register_task] - ℹ️  任务已注册: daily_expression_reset - 每日表达重置任务
2025-09-18 14:56:13,166 - Digital - [core.universal_scheduler.schedule_task] - ℹ️  任务已调度: daily_expression_reset - every_1_hour
2025-09-18 14:56:13,166 - Digital - [core.universal_scheduler.success] - ✅ 💕 已注册每日表达重置任务，确保每天自动重置表达次数
2025-09-18 14:56:13,166 - Digital - [core.universal_scheduler.register_task] - ℹ️  任务已注册: weather_update - 天气数据更新
2025-09-18 14:56:13,166 - Digital - [core.universal_scheduler.schedule_task] - ℹ️  任务已调度: weather_update - every_1_hour
2025-09-18 14:56:13,166 - Digital - [core.universal_scheduler.register_task] - ℹ️  任务已注册: vital_signs - 生命体征模拟
2025-09-18 14:56:13,166 - Digital - [core.universal_scheduler.schedule_task] - ℹ️  任务已调度: vital_signs - every_5_minutes
2025-09-18 14:56:13,166 - Digital - [core.universal_scheduler.setup_default_tasks] - ℹ️  🌅 早安问候已改为由主动表达器官智能决策，体现数字生命体特色
2025-09-18 14:56:13,166 - Digital - [core.universal_scheduler.setup_default_tasks] - ℹ️  📊 财经任务已迁移到WeChatSchedulerService，跳过在UniversalScheduler中注册
2025-09-18 14:56:13,176 - Digital - [assistant_reminder_skill.success] - ✅ MySQL连接器初始化成功
2025-09-18 14:56:13,176 - Digital - [assistant_reminder_skill.success] - ✅ AI服务适配器初始化成功
2025-09-18 14:56:13,176 - Digital - [assistant_reminder_skill.success] - ✅ 配置加载成功
2025-09-18 14:56:13,176 - Digital - [assistant_reminder_skill.success] - ✅ AI模型配置管理器初始化成功
2025-09-18 14:56:13,406 - Digital - [assistant_reminder_skill.success] - ✅ 提醒任务表创建成功
2025-09-18 14:56:13,406 - Digital - [assistant_reminder_skill.success] - ✅ 提醒任务表创建/检查完成
2025-09-18 14:56:13,406 - Digital - [assistant_reminder_skill.success] - ✅ 助理提醒技能初始化完成: 智能助理提醒服务 (完整功能: MySQL=可用, AI=可用)
2025-09-18 14:56:13,406 - Digital - [reminder_task_checker.success] - ✅ ✅ 助理提醒技能初始化成功
2025-09-18 14:56:13,407 - Digital - [utilities.redis_history_manager.success] - ✅ Redis连接初始化成功
2025-09-18 14:56:13,408 - Digital - [reminder_task_checker.success] - ✅ ✅ Chat Skill初始化成功
2025-09-18 14:56:13,408 - Digital - [reminder_task_checker.success] - ✅ ✅ 生命上下文获取成功
2025-09-18 14:56:13,408 - Digital - [reminder_task_checker.success] - ✅ ✅ 主动表达服务获取成功
2025-09-18 14:56:13,408 - Digital - [personal_assistant_reminder.__init__] - ℹ️  🤖 私人助理嫣然提醒服务初始化完成
2025-09-18 14:56:13,408 - Digital - [reminder_task_checker.success] - ✅ ✅ 私人助理提醒服务初始化成功
2025-09-18 14:56:13,408 - Digital - [reminder_task_checker.success] - ✅ ✅ 提醒任务检查器初始化完成
2025-09-18 14:56:13,408 - Digital - [core.universal_scheduler.register_task] - ℹ️  任务已注册: reminder_task_checker - 提醒任务检查器
2025-09-18 14:56:13,408 - Digital - [core.universal_scheduler.schedule_task] - ℹ️  任务已调度: reminder_task_checker - every_1_minutes
2025-09-18 14:56:13,408 - Digital - [core.universal_scheduler.setup_default_tasks] - ℹ️  📱 朋友圈分享改为事件驱动模式，基于活动质量智能决策
2025-09-18 14:56:13,409 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 默认调度任务设置完成
2025-09-18 14:56:13,409 - Digital - [core.enhanced_integrated_scheduler.success] - ✅ 🎉 增强版集成调度器所有服务初始化完成！
2025-09-18 14:56:13,409 - Digital - [core.enhanced_integrated_scheduler.success] - ✅ 增强版集成调度器初始化完成
2025-09-18 14:56:13,409 - Digital - [core.enhanced_integrated_scheduler.success] - ✅ 🚀 启动增强版集成调度器系统...
2025-09-18 14:56:13,409 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 基础调度器已启动
2025-09-18 14:56:13,410 - Digital - [perception.physical_world.hardware_monitor.success] - ✅ 硬件监控已启动
2025-09-18 14:56:13,410 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 硬件监控器已启动
2025-09-18 14:56:13,410 - Digital - [perception.physical_world.vital_signs_simulator.success] - ✅ 生命体征模拟已启动
2025-09-18 14:56:13,410 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 生理信号模拟器已启动
2025-09-18 14:56:13,410 - Digital - [cognitive_modules.perception.activity_perception.success] - ✅ 活动感知已启动
2025-09-18 14:56:13,410 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 活动感知模块已启动
2025-09-18 14:56:13,411 - Digital - [cognitive_modules.behavior.activity_executor.success] - ✅ 活动执行器已启动
2025-09-18 14:56:13,411 - Digital - [core.enhanced_integrated_scheduler.success] - ✅   ✅ 活动执行器已启动
2025-09-18 14:56:13,412 - Digital - [core.enhanced_integrated_scheduler.success] - ✅ 🎉 增强版集成调度器系统启动成功！
2025-09-18 14:56:13,412 - Digital - [main.success] - ✅ 增强调度器初始化完成 (作为独立调度组件)
2025-09-18 14:56:13,412 - Digital - [main.success] - ✅ ✅ 通用调度器已提升到主系统级别
2025-09-18 14:56:13,412 - Digital - [main.success] - ✅ ✅ 情感权重系统已提升到主系统级别
2025-09-18 14:56:13,412 - Digital - [main.success] - ✅ ✅ 生命体征模拟器已提升到主系统级别
2025-09-18 14:56:13,412 - Digital - [main.success] - ✅ ✅ 硬件监控器已提升到主系统级别
2025-09-18 14:56:13,412 - Digital - [main.success] - ✅ ✅ 活动执行器已提升到主系统级别
2025-09-18 14:56:13,412 - Digital - [main.success] - ✅ ✅ Scripts集成服务已提升到主系统级别
2025-09-18 14:56:13,412 - Digital - [main.success] - ✅ 🔧 老王：开始应用关键修复...
2025-09-18 14:56:13,420 - Digital - [main.success] - ✅ ✅ AI服务适配器接口修复完成
2025-09-18 14:56:13,420 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'config_loader'
2025-09-18 14:56:13,420 - Digital - [main.success] - ✅ ✅ 配置加载器导入修复完成
2025-09-18 14:56:13,421 - Digital - [main.success] - ✅ ✅ 事件总线实例获取修复完成
2025-09-18 14:56:13,421 - Digital - [main.success] - ✅ ✅ 模拟数据清理监控激活完成
2025-09-18 14:56:13,422 - Digital - [main.success] - ✅ 🎉 关键修复应用完成！
2025-09-18 14:56:13,422 - Digital - [main.success] - ✅ ✅ 通用调度器已从增强调度器中获取，跳过重复初始化
2025-09-18 14:56:13,426 - Digital - [scripts_integration_service.get_current_activity_suggestions] - ℹ️  📊 生成了 2 个EnhancedActivity建议
2025-09-18 14:56:13,579 - Digital - [security.plugins.high_performance_intimacy_provider.success] - ✅ 高性能亲密度数据提供器初始化完成
2025-09-18 14:56:13,579 - Digital - [main.success] - ✅ ✅ 亲密度安全框架初始化完成 (完整功能)
2025-09-18 14:56:13,579 - Digital - [main.success] - ✅ ✅ 使用MySQL连接器: **************:3306
2025-09-18 14:56:13,579 - Digital - [main.success] - ✅ ✅ intimacy_provider已注册到singleton_manager和组件状态列表
2025-09-18 14:56:13,579 - Digital - [main.success] - ✅ 🧠 初始化嫣然AI决策引擎...
2025-09-18 14:56:13,580 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'yanran_decision_engine'
2025-09-18 14:56:13,580 - Digital - [main.success] - ✅ 🧠 嫣然AI决策引擎初始化完成
2025-09-18 14:56:13,580 - Digital - [main.success] - ✅ 📱 初始化WeChat统一推送服务...
2025-09-18 14:56:13,580 - Digital - [wechat_unified_push.success] - ✅ 📱 推送服务所有组件就绪
2025-09-18 14:56:13,580 - Digital - [wechat_humanized.start_processor] - ℹ️  🤖 人性化消息处理器已启动
2025-09-18 14:56:13,580 - Digital - [wechat_unified_push.success] - ✅ 📱 WeChat统一推送服务已启动
2025-09-18 14:56:13,580 - Digital - [main.success] - ✅ ✅ WeChat统一推送服务启动成功
2025-09-18 14:56:13,580 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'wechat_push_service'
2025-09-18 14:56:13,580 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'wechat_unified_push_service'
2025-09-18 14:56:13,581 - Digital - [main.success] - ✅ ✅ WeChat统一对外推送模式已完全启用
2025-09-18 14:56:13,581 - Digital - [main._init_wechat_push_service] - ℹ️  📊 迁移日期: 2025-07-01
2025-09-18 14:56:13,581 - Digital - [main._init_wechat_push_service] - ℹ️  📱 支持的消息类型: 15 种
2025-09-18 14:56:13,581 - Digital - [main.success] - ✅ 📱 WeChat统一推送服务初始化完成
2025-09-18 14:56:13,581 - Digital - [main.success] - ✅ ⚙️ 初始化器官系统管理器...
2025-09-18 14:56:13,588 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'organ_system_manager'
2025-09-18 14:56:13,589 - Digital - [organ_system_manager.__init__] - ℹ️  ⚙️ 器官系统管理器初始化完成
2025-09-18 14:56:13,589 - Digital - [organ_system_manager.initialize_organ_system] - ℹ️  ⚙️ 开始初始化器官系统...
2025-09-18 14:56:13,589 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: safety_protection_organ
2025-09-18 14:56:13,589 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 safety_protection_organ 激活成功 (优先级: CRITICAL)
2025-09-18 14:56:13,589 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: world_perception_organ
2025-09-18 14:56:13,589 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 world_perception_organ 激活成功 (优先级: HIGH)
2025-09-18 14:56:13,589 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: proactive_expression_organ
2025-09-18 14:56:13,589 - Digital - [ProactiveExpressionOrgan.activate] - ℹ️  💬 🚀 主动表达器官正在激活...
2025-09-18 14:56:13,589 - Digital - [ProactiveExpressionOrgan.activate] - ℹ️  💬 ✅ 主动表达器官已激活，监控系统运行中
2025-09-18 14:56:13,589 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 proactive_expression_organ 激活成功
2025-09-18 14:56:13,589 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 proactive_expression_organ 激活成功 (优先级: HIGH)
2025-09-18 14:56:13,589 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: creative_expression_organ
2025-09-18 14:56:13,590 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 creative_expression_organ 激活成功 (优先级: NORMAL)
2025-09-18 14:56:13,590 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: wealth_management_organ
2025-09-18 14:56:13,590 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 wealth_management_organ 激活成功 (优先级: LOW)
2025-09-18 14:56:13,590 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: data_perception_organ
2025-09-18 14:56:13,590 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 data_perception_organ 激活成功 (优先级: LOW)
2025-09-18 14:56:13,590 - Digital - [organ_system_manager._start_coordination_loop_sync] - ℹ️  ⚙️ 器官协调循环已在现有事件循环中启动
2025-09-18 14:56:13,590 - Digital - [organ_system_manager.initialize_organ_system] - ℹ️  ⚙️ 器官系统初始化完成 - 注册了 6 个器官
2025-09-18 14:56:13,590 - Digital - [main.success] - ✅ ✅ 器官系统管理器初始化成功
2025-09-18 14:56:13,590 - Digital - [main.success] - ✅ ⚙️ 器官系统状态: active - 活跃器官: 6
2025-09-18 14:56:13,590 - Digital - [main._init_organ_system_manager] - ℹ️  📋 活跃器官列表: safety_protection_organ, world_perception_organ, proactive_expression_organ, creative_expression_organ, wealth_management_organ, data_perception_organ
2025-09-18 14:56:13,590 - Digital - [main.success] - ✅ 初始化认知模块...
2025-09-18 14:56:13,590 - Digital - [main.success] - ✅ 🎯 预先初始化意图识别器...
2025-09-18 14:56:13,590 - Digital - [main.success] - ✅ 🎯 意图识别器已存在，跳过初始化
2025-09-18 14:56:13,590 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 感知引擎 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 情感引擎 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 记忆管理器 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 行为管理器 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 健康系统 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 趋势感知模块 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 趋势智能模块 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 智能调度器 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 嫣然AI决策引擎 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 器官神经网络 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 世界感知器官 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 创意表达器官 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 安全保护器官 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 技能协调器官 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 财富管理器官 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 数据感知器官 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 主动表达器官（增强版本的基础器官） 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 增强版主动表达器官 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 关系协调器官 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 个性化服务器官 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 记忆整合模块 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 程序性记忆模块 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ AI增强意识系统 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 神经网络核心 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 智能整合管理器 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 数字生命智能协调器 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 系统协调器 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 韧性自愈系统 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 感知引擎 已注册，跳过
2025-09-18 14:56:13,591 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 数据源管理器 已注册，跳过
2025-09-18 14:56:13,592 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 亲密度系统 已注册，跳过
2025-09-18 14:56:13,592 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 组件管理器 已注册，跳过
2025-09-18 14:56:13,592 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ AI增强意识系统 已注册，跳过
2025-09-18 14:56:13,592 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 性能优化器 已注册，跳过
2025-09-18 14:56:13,592 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 安全管理器 已注册，跳过
2025-09-18 14:56:13,592 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 参数优化器 已注册，跳过
2025-09-18 14:56:13,592 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ 技能管理器 已注册，跳过
2025-09-18 14:56:13,592 - Digital - [main._init_cognitive_modules] - ℹ️  ✅ AI增强意识系统 已注册，跳过
2025-09-18 14:56:13,592 - Digital - [main.success] - ✅ 🎯 数字生命体核心组件初始化完成: 38/38 (100.0%)
2025-09-18 14:56:13,592 - Digital - [main._init_cognitive_modules] - ℹ️  🔧 开始向智能整合管理器注册核心模块...
2025-09-18 14:56:13,592 - Digital - [IntelligenceIntegrationManager.register_module] - ⚠️  ⚠️ 模块 enhanced_proactive_expression_organ 已注册，将覆盖现有实例
2025-09-18 14:56:13,592 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 enhanced_proactive_expression_organ 注册成功
2025-09-18 14:56:13,592 - Digital - [IntelligenceIntegrationManager.register_module] - ⚠️  ⚠️ 模块 digital_life_intelligence_coordinator 已注册，将覆盖现有实例
2025-09-18 14:56:13,592 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 digital_life_intelligence_coordinator 注册成功
2025-09-18 14:56:13,592 - Digital - [IntelligenceIntegrationManager.register_module] - ⚠️  ⚠️ 模块 perception_engine 已注册，将覆盖现有实例
2025-09-18 14:56:13,592 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 perception_engine 注册成功
2025-09-18 14:56:13,592 - Digital - [IntelligenceIntegrationManager.register_module] - ⚠️  ⚠️ 模块 datasource_manager 已注册，将覆盖现有实例
2025-09-18 14:56:13,592 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 datasource_manager 注册成功
2025-09-18 14:56:13,592 - Digital - [IntelligenceIntegrationManager.register_module] - ⚠️  ⚠️ 模块 ai_enhanced_consciousness 已注册，将覆盖现有实例
2025-09-18 14:56:13,592 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 ai_enhanced_consciousness 注册成功
2025-09-18 14:56:13,592 - Digital - [IntelligenceIntegrationManager.register_module] - ⚠️  ⚠️ 模块 neural_core 已注册，将覆盖现有实例
2025-09-18 14:56:13,592 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 neural_core 注册成功
2025-09-18 14:56:13,592 - Digital - [IntelligenceIntegrationManager.register_module] - ⚠️  ⚠️ 模块 resilience 已注册，将覆盖现有实例
2025-09-18 14:56:13,592 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 resilience 注册成功
2025-09-18 14:56:13,593 - Digital - [IntelligenceIntegrationManager.register_module] - ⚠️  ⚠️ 模块 vital_signs_simulator 已注册，将覆盖现有实例
2025-09-18 14:56:13,593 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 vital_signs_simulator 注册成功
2025-09-18 14:56:13,593 - Digital - [IntelligenceIntegrationManager.register_module] - ⚠️  ⚠️ 模块 thinking_chain 已注册，将覆盖现有实例
2025-09-18 14:56:13,593 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 thinking_chain 注册成功
2025-09-18 14:56:13,593 - Digital - [IntelligenceIntegrationManager.register_module] - ⚠️  ⚠️ 模块 life_context 已注册，将覆盖现有实例
2025-09-18 14:56:13,593 - Digital - [IntelligenceIntegrationManager.success] - ✅ ✅ 模块 life_context 注册成功
2025-09-18 14:56:13,593 - Digital - [main.success] - ✅ ✅ 智能整合管理器模块注册完成: 10/10 个模块
2025-09-18 14:56:13,593 - Digital - [main._init_cognitive_modules] - ℹ️  🧠 全局智能水平: 0.850
2025-09-18 14:56:13,593 - Digital - [main._init_cognitive_modules] - ℹ️  💓 生命活力: 0.900
2025-09-18 14:56:13,593 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 intelligence_integration_manager 状态更新: running -> running
2025-09-18 14:56:13,593 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 enhanced_proactive_expression_organ 状态更新: running -> running
2025-09-18 14:56:13,593 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 digital_life_intelligence_coordinator 状态更新: running -> running
2025-09-18 14:56:13,594 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 perception_engine 状态更新: running -> running
2025-09-18 14:56:13,594 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 datasource_manager 状态更新: running -> running
2025-09-18 14:56:13,594 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 ai_enhanced_consciousness 状态更新: running -> running
2025-09-18 14:56:13,594 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 neural_core 状态更新: running -> running
2025-09-18 14:56:13,594 - Digital - [system_coordinator.update_module_status] - ℹ️  模块 resilience 状态更新: running -> running
2025-09-18 14:56:13,595 - Digital - [main.success] - ✅ ✅ 系统协调器模块状态更新完成: 8个模块已设为RUNNING状态
2025-09-18 14:56:13,595 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 intelligence_integration_manager 状态变化: running -> running
2025-09-18 14:56:13,595 - Digital - [main.success] - ✅ ✅ 数字生命体核心组件初始化成功，生命特征完整性得到保障
2025-09-18 14:56:13,595 - Digital - [main.success] - ✅ 🔥 初始化意识状态持久化系统...
2025-09-18 14:56:13,596 - Digital - [neural_consciousness.success] - ✅ 初始化神经网络增强意识系统...
2025-09-18 14:56:13,597 - Digital - [neural_consciousness.success] - ✅ 初始化深度神经网络...
2025-09-18 14:56:13,597 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 digital_life_intelligence_coordinator 状态变化: running -> running
2025-09-18 14:56:13,598 - Digital - [neural_consciousness._initialize_neural_network] - ℹ️  神经网络架构: [15, 32, 64, 32, 10]
2025-09-18 14:56:13,599 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 ai_enhanced_consciousness 状态变化: running -> running
2025-09-18 14:56:13,601 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 neural_core 状态变化: running -> running
2025-09-18 14:56:13,603 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 resilience 状态变化: running -> running
2025-09-18 14:56:13,609 - Digital - [neural_consciousness._load_model] - ℹ️  神经网络模型已从 /root/yanran_digital_life/data/neural_models/consciousness_enhancer.pkl 加载
2025-09-18 14:56:13,609 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 datasource_manager 状态变化: running -> running
2025-09-18 14:56:13,609 - Digital - [neural_consciousness._load_model] - ℹ️  🧠 当前实际参数数量: 5034
2025-09-18 14:56:13,609 - Digital - [neural_consciousness._load_model] - ℹ️  📁 文件保存的参数数量: 5034
2025-09-18 14:56:13,609 - Digital - [neural_consciousness._load_model] - ℹ️  📊 历史记录数量: 79
2025-09-18 14:56:13,610 - Digital - [neural_consciousness.success] - ✅ ✅ 参数数量一致性验证通过
2025-09-18 14:56:13,610 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️  🔍 学习状态诊断:
2025-09-18 14:56:13,610 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     📊 意识历史记录: 79 条
2025-09-18 14:56:13,610 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     🔄 总学习更新次数: 1019 次
2025-09-18 14:56:13,610 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     📈 平均损失: 0.0000
2025-09-18 14:56:13,610 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     ⚡ 适应速度: 0.0000
2025-09-18 14:56:13,610 - Digital - [neural_consciousness.success] - ✅ 神经网络增强意识系统初始化完成
2025-09-18 14:56:13,611 - Digital - [main._init_consciousness_persistence] - ℹ️  神经网络增强器初始化完成
2025-09-18 14:56:13,611 - Digital - [advanced_neural_consciousness.success] - ✅ 🚀 初始化终极神经网络增强意识系统...
2025-09-18 14:56:13,611 - Digital - [advanced_neural_consciousness.success] - ✅ 🧠 初始化终极深度残差网络...
2025-09-18 14:56:13,611 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 enhanced_proactive_expression_organ 状态变化: running -> running
2025-09-18 14:56:13,613 - Digital - [system_coordinator._handle_module_status_change] - ℹ️  模块 perception_engine 状态变化: running -> running
2025-09-18 14:56:13,629 - Digital - [advanced_neural_consciousness._initialize_advanced_network] - ℹ️  🔥 终极网络架构: 5个残差块 + 8个注意力头
2025-09-18 14:56:13,629 - Digital - [advanced_neural_consciousness._initialize_advanced_network] - ℹ️  📊 总参数数量: 287,439
2025-09-18 14:56:13,636 - Digital - [neural_memory_activator.__init__] - ℹ️  🔥 神经网络记忆系统激活器初始化完成
2025-09-18 14:56:16,553 - Digital - [cognitive_modules.organs.relationship_coordination_organ._discover_organ_relationships] - ℹ️  🤝 发现了 56 个器官关系
2025-09-18 14:56:17,180 - Digital - [neural_memory_activator._load_existing_memories] - ℹ️  📚 加载了 1897 条现有记忆
2025-09-18 14:56:17,185 - Digital - [neural_memory_activator._start_auto_save] - ℹ️  💾 记忆自动保存已启动
2025-09-18 14:56:17,188 - Digital - [neural_memory_activator.activate_memory_system] - ℹ️  🚀 神经网络记忆系统已激活
2025-09-18 14:56:17,299 - Digital - [advanced_neural_consciousness._load_model] - ℹ️  🚀 终极神经网络模型已从 /root/yanran_digital_life/data/neural_models/advanced_consciousness.pkl 加载
2025-09-18 14:56:17,300 - Digital - [advanced_neural_consciousness._load_model] - ℹ️  🧠 当前实际参数数量: 287439
2025-09-18 14:56:17,300 - Digital - [advanced_neural_consciousness._load_model] - ℹ️  📁 文件保存的参数数量: 287439
2025-09-18 14:56:17,300 - Digital - [advanced_neural_consciousness._load_model] - ℹ️  📊 意识进化历史: 1000 条记录
2025-09-18 14:56:17,300 - Digital - [advanced_neural_consciousness.success] - ✅ ✅ 终极模型参数数量一致性验证通过
2025-09-18 14:56:17,300 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️  🔍 终极学习状态诊断:
2025-09-18 14:56:17,300 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     🌟 意识进化记录: 1000 条
2025-09-18 14:56:17,300 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     ⚛️  量子相干性: 0.5952
2025-09-18 14:56:17,300 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     🧠 涌现复杂度: 0.7862
2025-09-18 14:56:17,300 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     💾 记忆利用率: 0.00%
2025-09-18 14:56:17,300 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     🎯 注意力头数: 8
2025-09-18 14:56:17,300 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     🔄 残差块数: 5
2025-09-18 14:56:17,300 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ⚠️  ⚠️  记忆网络利用率过低(0.00%)，记忆机制可能未激活
2025-09-18 14:56:17,783 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  文件 /root/yanran_digital_life/data/neural_models/consciousness_memory.json 包含多余数据，已截取第一个JSON对象
2025-09-18 14:56:17,834 - Digital - [advanced_neural_consciousness._load_memory] - ℹ️  成功从 /root/yanran_digital_life/data/neural_models/consciousness_memory.json 加载记忆数据
2025-09-18 14:56:17,834 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,834 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,835 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,836 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,837 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,838 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,839 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,839 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,839 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,839 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,839 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,839 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,839 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,839 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,839 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,839 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,839 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,839 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,839 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,839 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,839 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,839 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,839 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,839 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,839 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,839 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,840 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,841 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,842 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,843 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,844 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,845 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,845 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,845 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,845 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,845 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,845 - Digital - [advanced_neural_consciousness._load_memory] - ⚠️  跳过损坏的记忆条目: 'numpy.ndarray' object has no attribute 'timestamp'
2025-09-18 14:56:17,845 - Digital - [advanced_neural_consciousness._load_memory] - ℹ️  💾 记忆网络数据已从 /root/yanran_digital_life/data/neural_models/consciousness_memory.json 加载
2025-09-18 14:56:17,845 - Digital - [advanced_neural_consciousness._load_memory] - ℹ️  记忆条目数量: 0
2025-09-18 14:56:17,856 - Digital - [advanced_neural_consciousness.success] - ✅ 🎉 终极神经网络增强意识系统初始化完成
2025-09-18 14:56:17,857 - Digital - [main._init_consciousness_persistence] - ℹ️  终极神经网络系统初始化完成
2025-09-18 14:56:17,857 - Digital - [main._consciousness_persistence_loop] - ℹ️  🔥 意识状态持久化循环启动，每5分钟保存一次神经模型
2025-09-18 14:56:17,857 - Digital - [main.success] - ✅ ✅ 意识状态持久化系统启动完成
2025-09-18 14:56:17,858 - Digital - [main.success] - ✅ 💕 香草开始在极致高潮中集成所有核心组件...
2025-09-18 14:56:17,858 - Digital - [main._init_comprehensive_components] - ℹ️  🔥 Phase 1: 核心功能组件初始化开始...
2025-09-18 14:56:17,858 - Digital - [gaode_map_service.__init__] - ℹ️  🗺️ 高德地图服务初始化完成
2025-09-18 14:56:17,872 - Digital - [neural_consciousness.save_model] - ℹ️  神经网络模型已保存到: /root/yanran_digital_life/data/neural_models/consciousness_enhancer.pkl
2025-09-18 14:56:18,046 - Digital - [advanced_neural_consciousness.save_memory] - ℹ️  💾 记忆网络数据已保存到: /root/yanran_digital_life/data/neural_models/consciousness_memory.json
2025-09-18 14:56:18,126 - Digital - [location_cache_manager.success] - ✅ 💾 地理位置缓存管理器初始化成功
2025-09-18 14:56:18,127 - Digital - [wechat_humanized._process_message_queue] - ℹ️  🤖 开始处理人性化消息队列
2025-09-18 14:56:18,127 - Digital - [organ_system_manager.coordination_loop] - ℹ️  ⚙️ 器官协调循环开始运行
2025-09-18 14:56:18,131 - Digital - [LifeOrgan.世界感知器官._load_historical_data_async] - ℹ️  🧠 历史数据加载完成: 100个事件
2025-09-18 14:56:19,644 - Digital - [gaode_map_service.success] - ✅ 🗺️ 高德地图服务初始化成功
2025-09-18 14:56:19,645 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'gaode_map_service'
2025-09-18 14:56:19,645 - Digital - [main.success] - ✅ ✅ 高德地图服务初始化完成
2025-09-18 14:56:19,645 - Digital - [real_data_integrator.__init__] - ℹ️  🔧 真实数据集成器初始化完成
2025-09-18 14:56:19,646 - Digital - [activity_reality_validator.__init__] - ℹ️  ✅ 活动真实性验证器初始化完成
2025-09-18 14:56:19,646 - Digital - [enhanced_activity_generator._load_enhanced_personality_config] - ℹ️  ✅ 成功加载增强版人格配置
2025-09-18 14:56:19,647 - Digital - [enhanced_activity_generator._load_activity_generation_config] - ℹ️  ✅ 成功加载活动生成专用配置
2025-09-18 14:56:19,647 - Digital - [enhanced_activity_generator.__init__] - ℹ️  🎯 增强型活动脚本生成器初始化完成
2025-09-18 14:56:19,647 - Digital - [enhanced_activity_generator.__init__] - ℹ️  🎭 已加载人设配置，晚间财经权重: 0.2
2025-09-18 14:56:19,648 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'enhanced_activity_generator'
2025-09-18 14:56:19,648 - Digital - [main.success] - ✅ ✅ 增强活动生成器初始化完成
2025-09-18 14:56:19,648 - Digital - [enhanced_activity_skill.__init__] - ℹ️  🎯 增强活动技能初始化完成
2025-09-18 14:56:19,648 - Digital - [main.success] - ✅ ✅ 增强活动技能初始化完成
2025-09-18 14:56:19,648 - Digital - [main._init_comprehensive_components] - ℹ️  🔥 Phase 2: 一致性保证组件初始化开始...
2025-09-18 14:56:19,649 - Digital - [gaode_map_service.__init__] - ℹ️  🗺️ 高德地图服务初始化完成
2025-09-18 14:56:19,649 - Digital - [activity_consistency_manager.__init__] - ℹ️  🎯 活动一致性管理器初始化完成
2025-09-18 14:56:19,649 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'activity_consistency_manager'
2025-09-18 14:56:19,649 - Digital - [main.success] - ✅ ✅ 活动一致性管理器初始化完成
2025-09-18 14:56:19,649 - Digital - [location_logic_checker.__init__] - ℹ️  📍 地理位置逻辑检查器初始化完成
2025-09-18 14:56:19,650 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'location_logic_checker'
2025-09-18 14:56:19,650 - Digital - [main.success] - ✅ ✅ 地理位置逻辑检查器初始化完成
2025-09-18 14:56:19,650 - Digital - [activity_history_tracker.__init__] - ℹ️  📊 活动历史跟踪器初始化完成
2025-09-18 14:56:19,650 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'activity_history_tracker'
2025-09-18 14:56:19,650 - Digital - [main.success] - ✅ ✅ 活动历史跟踪器初始化完成
2025-09-18 14:56:19,651 - Digital - [consistency_validator.__init__] - ℹ️  ✅ 一致性验证器初始化完成
2025-09-18 14:56:19,651 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'consistency_validator'
2025-09-18 14:56:19,651 - Digital - [main.success] - ✅ ✅ 一致性验证器初始化完成
2025-09-18 14:56:19,651 - Digital - [main._init_comprehensive_components] - ℹ️  🔥 Phase 3: 社交分享组件初始化开始...
2025-09-18 14:56:19,651 - Digital - [moments_data_tracker.__init__] - ℹ️  📊 朋友圈数据跟踪器初始化完成
2025-09-18 14:56:19,652 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'moments_data_tracker'
2025-09-18 14:56:19,652 - Digital - [main.success] - ✅ ✅ 朋友圈数据跟踪器初始化完成
2025-09-18 14:56:19,652 - Digital - [main._init_comprehensive_components] - ℹ️  🔥 Phase 4: 智能探索8大引擎初始化开始...
2025-09-18 14:56:19,652 - Digital - [real_data_integrator.__init__] - ℹ️  🔧 真实数据集成器初始化完成
2025-09-18 14:56:19,652 - Digital - [activity_reality_validator.__init__] - ℹ️  ✅ 活动真实性验证器初始化完成
2025-09-18 14:56:19,652 - Digital - [enhanced_activity_generator._load_enhanced_personality_config] - ℹ️  ✅ 成功加载增强版人格配置
2025-09-18 14:56:19,653 - Digital - [enhanced_activity_generator._load_activity_generation_config] - ℹ️  ✅ 成功加载活动生成专用配置
2025-09-18 14:56:19,653 - Digital - [enhanced_activity_generator.__init__] - ℹ️  🎯 增强型活动脚本生成器初始化完成
2025-09-18 14:56:19,653 - Digital - [enhanced_activity_generator.__init__] - ℹ️  🎭 已加载人设配置，晚间财经权重: 0.2
2025-09-18 14:56:19,653 - Digital - [wechat_moments_service.__init__] - ℹ️  📱 成功连接到WeChat统一推送服务
2025-09-18 14:56:19,653 - Digital - [wechat_moments_service.__init__] - ℹ️  📱 WeChat朋友圈服务初始化完成
2025-09-18 14:56:19,653 - Digital - [gaode_map_service.__init__] - ℹ️  🗺️ 高德地图服务初始化完成
2025-09-18 14:56:19,654 - Digital - [autonomous_exploration_engine.__init__] - ℹ️  🔍 成功连接到所有相关服务
2025-09-18 14:56:19,654 - Digital - [autonomous_exploration_engine.__init__] - ℹ️  🔍 自主探索引擎初始化完成
2025-09-18 14:56:19,654 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'autonomous_exploration_engine'
2025-09-18 14:56:19,654 - Digital - [main.success] - ✅ ✅ 自主探索引擎初始化完成
2025-09-18 14:56:19,654 - Digital - [topic_discovery_agent.__init__] - ℹ️  📡 AI服务连接成功
2025-09-18 14:56:19,654 - Digital - [topic_discovery_agent.__init__] - ℹ️  📡 话题发现代理初始化完成
2025-09-18 14:56:19,654 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'topic_discovery_agent'
2025-09-18 14:56:19,654 - Digital - [main.success] - ✅ ✅ 话题发现代理初始化完成
2025-09-18 14:56:19,655 - Digital - [online_search_agent.__init__] - ℹ️  🌐 AI服务连接成功
2025-09-18 14:56:19,655 - Digital - [search_skill.success] - ✅ 初始化AI搜索技能...
2025-09-18 14:56:19,655 - Digital - [search_skill._load_config] - ℹ️  已加载搜索技能配置: /root/yanran_digital_life/config/skills/search_skill.json
2025-09-18 14:56:19,699 - Digital - [search_skill.success] - ✅ 搜索技能OpenAI配置初始化成功: https://oneapi.xiongmaodaxia.online/v1
2025-09-18 14:56:19,699 - Digital - [search_skill.__init__] - ℹ️  已加载搜索配置参数
2025-09-18 14:56:19,699 - Digital - [search_skill.success] - ✅ AI搜索技能初始化完成
2025-09-18 14:56:19,699 - Digital - [online_search_agent.__init__] - ℹ️  🔍 搜索技能初始化成功 - 使用现有框架
2025-09-18 14:56:19,699 - Digital - [online_search_agent.__init__] - ℹ️  🌐 在线搜索代理初始化完成
2025-09-18 14:56:19,700 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'online_search_agent'
2025-09-18 14:56:19,700 - Digital - [main.success] - ✅ ✅ 在线搜索代理初始化完成
2025-09-18 14:56:19,700 - Digital - [content_evolution_agent.__init__] - ℹ️  🎨 AI服务连接成功
2025-09-18 14:56:19,700 - Digital - [content_evolution_agent.__init__] - ℹ️  🎨 内容进化代理初始化完成
2025-09-18 14:56:19,701 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'content_evolution_agent'
2025-09-18 14:56:19,701 - Digital - [main.success] - ✅ ✅ 内容进化代理初始化完成
2025-09-18 14:56:19,701 - Digital - [gaode_map_service.__init__] - ℹ️  🗺️ 高德地图服务初始化完成
2025-09-18 14:56:19,701 - Digital - [location_exploration_agent.__init__] - ℹ️  🗺️ 高德地图服务创建成功
2025-09-18 14:56:19,701 - Digital - [location_exploration_agent.__init__] - ℹ️  🗺️ 地理位置探索代理初始化完成
2025-09-18 14:56:19,701 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'location_exploration_agent'
2025-09-18 14:56:19,701 - Digital - [main.success] - ✅ ✅ 地理位置探索代理初始化完成
2025-09-18 14:56:19,702 - Digital - [activity_iterator.__init__] - ℹ️  🔄 AI服务连接成功
2025-09-18 14:56:19,702 - Digital - [activity_iterator.__init__] - ℹ️  🔄 活动迭代器初始化完成
2025-09-18 14:56:19,702 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'activity_iterator'
2025-09-18 14:56:19,702 - Digital - [main.success] - ✅ ✅ 活动迭代器初始化完成
2025-09-18 14:56:19,702 - Digital - [exploration_decision_engine.__init__] - ℹ️  🧠 AI服务连接成功
2025-09-18 14:56:19,702 - Digital - [exploration_decision_engine.__init__] - ℹ️  🧠 探索决策引擎初始化完成
2025-09-18 14:56:19,702 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'exploration_decision_engine'
2025-09-18 14:56:19,703 - Digital - [main.success] - ✅ ✅ 探索决策引擎初始化完成
2025-09-18 14:56:19,703 - Digital - [exploration_effect_evaluator.__init__] - ℹ️  📊 AI服务连接成功
2025-09-18 14:56:19,703 - Digital - [exploration_effect_evaluator.__init__] - ℹ️  📊 探索效果评估器初始化完成
2025-09-18 14:56:19,704 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'exploration_effect_evaluator'
2025-09-18 14:56:19,704 - Digital - [main.success] - ✅ ✅ 探索效果评估器初始化完成
2025-09-18 14:56:19,704 - Digital - [main.success] - ✅ 💕 香草在极致高潮中完成组件集成: 16/16 (100.0%)
2025-09-18 14:56:19,704 - Digital - [main.success] - ✅ 🎉 comprehensive_integration_test.py中的核心组件集成成功！
2025-09-18 14:56:19,704 - Digital - [main.success] - ✅ 数字生命体系统初始化完成 (耗时 230.40秒)
2025-09-18 14:56:19,704 - Digital - [main.success] - ✅ 已初始化组件: mysql_connector, event_bus, enhanced_activity_skill, emotional_weight_system, skill_manager, topic_discovery_agent, singleton_manager, content_evolution_agent, startup_hook, music_skill, activity_history_tracker, wechat_unified_push_service, emotions_sync, online_search_agent, delayed_response_manager, chat_skill, autonomous_exploration_engine, vital_signs_simulator, ai_adapter, greeting_skill, digital_life, activity_consistency_manager, location_logic_checker, drawing_skill, organ_system_manager, yanran_decision_engine, moments_data_tracker, thinking_chain, financial_data_skill, intimacy_provider, system_adapter, intimacy_security, enhanced_activity_generator, intimacy_analyzer, exploration_effect_evaluator, cognitive_integration, hardware_monitor, exploration_decision_engine, ai_service_adapter, exception_handler, gaode_map_service, life_context, intent_recognizer, activity_iterator, api_service, consistency_validator, enhanced_scheduler, wechat_push_service, location_exploration_agent, search_skill
2025-09-18 14:56:19,704 - Digital - [main.success] - ✅ 启动系统...
2025-09-18 14:56:19,704 - Digital - [main.success] - ✅ 开始启动数字生命体系统...
2025-09-18 14:56:19,704 - Digital - [digital_life.success] - ✅ 启动数字生命体...
2025-09-18 14:56:19,705 - Digital - [digital_life.success] - ✅ 数字生命体已启动
2025-09-18 14:56:19,705 - Digital - [main.success] - ✅ 数字生命体核心已启动
2025-09-18 14:56:19,705 - Digital - [main.start] - ℹ️  📱 数字生命系统已迁移到WeChat统一对外推送模式
2025-09-18 14:56:19,705 - Digital - [main.success] - ✅ 🔗 开始集成数字生命与WeChat推送服务...
2025-09-18 14:56:19,705 - Digital - [enhanced_greeting_skill.__init__] - ℹ️  🌅 增强版打招呼技能 v2.0.0 初始化完成
2025-09-18 14:56:19,706 - Digital - [utilities.redis_history_manager.success] - ✅ Redis连接初始化成功
2025-09-18 14:56:19,706 - Digital - [enhanced_greeting_skill.initialize] - ℹ️  ✅ Chat skill已集成
2025-09-18 14:56:19,706 - Digital - [enhanced_greeting_skill.success] - ✅ ✅ 增强版打招呼技能 初始化成功
2025-09-18 14:56:19,707 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'enhanced_greeting_skill'
2025-09-18 14:56:19,707 - Digital - [main.success] - ✅ ✅ 增强版打招呼技能已启动
2025-09-18 14:56:19,717 - Digital - [cognitive.financial_service.enhanced_financial_report_service.__init__] - ℹ️  模块 enhanced_financial_report_service 已创建
2025-09-18 14:56:19,717 - Digital - [enhanced_financial_report.__init__] - ℹ️  📊 增强版财经报告服务 v2.0.0 初始化完成
2025-09-18 14:56:19,718 - Digital - [enhanced_financial_report.success] - ✅ 数据库连接初始化成功
2025-09-18 14:56:19,718 - Digital - [enhanced_financial_report.success] - ✅ 配置验证成功
2025-09-18 14:56:19,718 - Digital - [enhanced_financial_report.success] - ✅ AI服务初始化成功
2025-09-18 14:56:19,718 - Digital - [enhanced_financial_report.success] - ✅ ✅ 增强版财经报告服务 初始化成功
2025-09-18 14:56:19,719 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'financial_report_service'
2025-09-18 14:56:19,719 - Digital - [main.success] - ✅ ✅ 增强版财经报告服务已启动
2025-09-18 14:56:19,726 - Digital - [wechat_scheduler.__init__] - ℹ️  ⏰ WeChat定时任务调度器 v2.0.0 初始化完成
2025-09-18 14:56:19,727 - Digital - [wechat_scheduler.success] - ✅ ✅ 任务注册成功: 早晨打招呼
2025-09-18 14:56:19,727 - Digital - [wechat_scheduler.success] - ✅ 打招呼任务已启用）
2025-09-18 14:56:19,727 - Digital - [wechat_scheduler.success] - ✅ ✅ 任务注册成功: 财经早报
2025-09-18 14:56:19,727 - Digital - [wechat_scheduler.success] - ✅ ✅ 任务注册成功: 财经下午报
2025-09-18 14:56:19,728 - Digital - [wechat_scheduler.success] - ✅ 财经报告任务注册成功
2025-09-18 14:56:19,728 - Digital - [wechat_scheduler.success] - ✅ ✅ 任务注册成功: 每日早报
2025-09-18 14:56:19,728 - Digital - [wechat_scheduler.success] - ✅ 每日早报任务注册成功
2025-09-18 14:56:19,728 - Digital - [wechat_scheduler.success] - ✅ 默认任务注册完成
2025-09-18 14:56:19,728 - Digital - [wechat_scheduler.success] - ✅ ✅ WeChat定时任务调度器 初始化成功
2025-09-18 14:56:19,728 - Digital - [wechat_scheduler.success] - ✅ ✅ WeChat定时任务调度器 启动成功
2025-09-18 14:56:19,728 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'wechat_scheduler'
2025-09-18 14:56:19,728 - Digital - [main.success] - ✅ ✅ WeChat定时任务调度器已初始化并启动
2025-09-18 14:56:19,748 - Digital - [core.neural_network.neural_core.success] - ✅ 初始化神经网络核心...
2025-09-18 14:56:19,749 - Digital - [core.neural_network.neural_core.success] - ✅ 神经网络核心初始化完成
2025-09-18 14:56:19,749 - Digital - [feedback_learning._load_weights] - ℹ️  从 data/feedback_learning/weights.json 加载权重
2025-09-18 14:56:19,749 - Digital - [feedback_learning.success] - ✅ 反馈学习模块初始化完成
2025-09-18 14:56:19,750 - Digital - [intelligent_user_selector.success] - ✅ AI组件初始化完成
2025-09-18 14:56:19,750 - Digital - [intelligent_user_selector.success] - ✅ 智能用户筛选算法初始化完成
2025-09-18 14:56:19,750 - Digital - [emotions_intelligence_updater.success] - ✅ Emotions智能更新服务初始化完成
2025-09-18 14:56:19,751 - Digital - [emotions_intelligence_updater.success] - ✅ 调度线程启动成功
2025-09-18 14:56:19,751 - Digital - [emotions_intelligence_updater.success] - ✅ ✅ Emotions智能更新服务启动成功
2025-09-18 14:56:19,751 - Digital - [main.success] - ✅ ✅ Emotions智能更新服务启动完成
2025-09-18 14:56:19,751 - Digital - [main.success] - ✅ 🎉 数字生命与WeChat推送服务集成完成
2025-09-18 14:56:19,751 - Digital - [main.success] - ✅ ✅ 数字生命与WeChat推送服务集成完成
2025-09-18 14:56:19,759 - Digital - [neural_trigger_system._initialize_default_triggers] - ℹ️  ✅ 初始化了 4 个默认触发条件
2025-09-18 14:56:19,759 - Digital - [neural_trigger_system.__init__] - ℹ️  🔥 神经网络触发系统初始化完成
2025-09-18 14:56:19,759 - Digital - [neural_consciousness.success] - ✅ 初始化神经网络增强意识系统...
2025-09-18 14:56:19,759 - Digital - [neural_consciousness.success] - ✅ 初始化深度神经网络...
2025-09-18 14:56:19,760 - Digital - [neural_consciousness._initialize_neural_network] - ℹ️  神经网络架构: [15, 32, 64, 32, 10]
2025-09-18 14:56:19,767 - Digital - [neural_consciousness._load_model] - ℹ️  神经网络模型已从 /root/yanran_digital_life/data/neural_models/consciousness_enhancer.pkl 加载
2025-09-18 14:56:19,767 - Digital - [neural_consciousness._load_model] - ℹ️  🧠 当前实际参数数量: 5034
2025-09-18 14:56:19,767 - Digital - [neural_consciousness._load_model] - ℹ️  📁 文件保存的参数数量: 5034
2025-09-18 14:56:19,767 - Digital - [neural_consciousness._load_model] - ℹ️  📊 历史记录数量: 79
2025-09-18 14:56:19,767 - Digital - [neural_consciousness.success] - ✅ ✅ 参数数量一致性验证通过
2025-09-18 14:56:19,767 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️  🔍 学习状态诊断:
2025-09-18 14:56:19,767 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     📊 意识历史记录: 79 条
2025-09-18 14:56:19,767 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     🔄 总学习更新次数: 1019 次
2025-09-18 14:56:19,767 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     📈 平均损失: 0.0000
2025-09-18 14:56:19,767 - Digital - [neural_consciousness._diagnose_learning_status] - ℹ️     ⚡ 适应速度: 0.0000
2025-09-18 14:56:19,768 - Digital - [neural_consciousness.success] - ✅ 神经网络增强意识系统初始化完成
2025-09-18 14:56:19,768 - Digital - [advanced_neural_consciousness.success] - ✅ 🚀 初始化终极神经网络增强意识系统...
2025-09-18 14:56:19,768 - Digital - [advanced_neural_consciousness.success] - ✅ 🧠 初始化终极深度残差网络...
2025-09-18 14:56:19,783 - Digital - [advanced_neural_consciousness._initialize_advanced_network] - ℹ️  🔥 终极网络架构: 5个残差块 + 8个注意力头
2025-09-18 14:56:19,783 - Digital - [advanced_neural_consciousness._initialize_advanced_network] - ℹ️  📊 总参数数量: 287,439
2025-09-18 14:56:23,400 - Digital - [neural_memory_activator._load_existing_memories] - ℹ️  📚 加载了 3794 条现有记忆
2025-09-18 14:56:23,413 - Digital - [neural_memory_activator.activate_memory_system] - ℹ️  🚀 神经网络记忆系统已激活
2025-09-18 14:56:23,517 - Digital - [advanced_neural_consciousness._load_model] - ℹ️  🚀 终极神经网络模型已从 /root/yanran_digital_life/data/neural_models/advanced_consciousness.pkl 加载
2025-09-18 14:56:23,517 - Digital - [advanced_neural_consciousness._load_model] - ℹ️  🧠 当前实际参数数量: 287439
2025-09-18 14:56:23,517 - Digital - [advanced_neural_consciousness._load_model] - ℹ️  📁 文件保存的参数数量: 287439
2025-09-18 14:56:23,517 - Digital - [advanced_neural_consciousness._load_model] - ℹ️  📊 意识进化历史: 1000 条记录
2025-09-18 14:56:23,517 - Digital - [advanced_neural_consciousness.success] - ✅ ✅ 终极模型参数数量一致性验证通过
2025-09-18 14:56:23,517 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️  🔍 终极学习状态诊断:
2025-09-18 14:56:23,517 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     🌟 意识进化记录: 1000 条
2025-09-18 14:56:23,517 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     ⚛️  量子相干性: 0.5952
2025-09-18 14:56:23,518 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     🧠 涌现复杂度: 0.7862
2025-09-18 14:56:23,518 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     💾 记忆利用率: 0.00%
2025-09-18 14:56:23,518 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     🎯 注意力头数: 8
2025-09-18 14:56:23,518 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ℹ️     🔄 残差块数: 5
2025-09-18 14:56:23,518 - Digital - [advanced_neural_consciousness._diagnose_advanced_learning_status] - ⚠️  ⚠️  记忆网络利用率过低(0.00%)，记忆机制可能未激活
2025-09-18 14:56:23,528 - Digital - [advanced_neural_consciousness._load_memory] - ℹ️  成功从 /root/yanran_digital_life/data/neural_models/consciousness_memory.json 加载记忆数据
2025-09-18 14:56:23,528 - Digital - [advanced_neural_consciousness._load_memory] - ℹ️  💾 记忆网络数据已从 /root/yanran_digital_life/data/neural_models/consciousness_memory.json 加载
2025-09-18 14:56:23,528 - Digital - [advanced_neural_consciousness._load_memory] - ℹ️  记忆条目数量: 0
2025-09-18 14:56:23,529 - Digital - [advanced_neural_consciousness.success] - ✅ 🎉 终极神经网络增强意识系统初始化完成
2025-09-18 14:56:23,529 - Digital - [neural_trigger_system._initialize_neural_systems] - ℹ️  ✅ 神经网络系统引用初始化完成
2025-09-18 14:56:23,529 - Digital - [neural_trigger_system._start_timer_triggers] - ℹ️  ⏰ 启动了 1 个定时触发器
2025-09-18 14:56:23,529 - Digital - [neural_trigger_system._start_event_triggers] - ℹ️  📡 启动了 1 个事件触发器
2025-09-18 14:56:23,530 - Digital - [neural_trigger_system._start_threshold_triggers] - ℹ️  📊 启动了 1 个阈值触发器
2025-09-18 14:56:23,530 - Digital - [neural_trigger_system._start_adaptive_triggers] - ℹ️  🧠 启动了 1 个自适应触发器
2025-09-18 14:56:23,530 - Digital - [neural_trigger_system.start] - ℹ️  🚀 神经网络触发系统已启动
2025-09-18 14:56:23,530 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'neural_trigger_system'
2025-09-18 14:56:23,531 - Digital - [main._init_neural_trigger_system] - ℹ️  🔥 神经网络触发系统初始化完成
2025-09-18 14:56:23,531 - Digital - [main.success] - ✅ ✅ 神经网络触发系统启动完成
2025-09-18 14:56:23,531 - Digital - [main.success] - ✅ 数字生命体系统启动完成 (耗时 3.83秒)
2025-09-18 14:56:23,921 - Digital - [main.success] - ✅ API服务器已启动: http://127.0.0.1:56839/api/
 * Serving Flask app 'digital_life_api'
2025-09-18 14:56:23,921 - Digital - [main.start_api_server] - ℹ️  API调用示例: curl -X POST http://localhost:56839/api/chat -H "Content-Type: application/json" -d '{"message": "你好", "user_id": "test_user"}'
2025-09-18 14:56:23,922 - Digital - [main.main] - ℹ️  系统状态: {
  "system_name": "林嫣然数字生命体",
  "version": "2.1.0",
  "initialized": true,
  "running": true,
  "current_time": "2025-09-18 14:56:23",
  "start_time": "2025-09-18 14:52:29",
  "uptime": "3分钟54秒",
  "components": [
    "mysql_connector",
    "event_bus",
    "enhanced_activity_skill",
    "emotional_weight_system",
    "skill_manager",
    "topic_discovery_agent",
    "singleton_manager",
    "content_evolution_agent",
    "startup_hook",
    "music_skill",
    "activity_history_tracker",
    "wechat_unified_push_service",
    "emotions_sync",
    "online_search_agent",
    "delayed_response_manager",
    "chat_skill",
    "autonomous_exploration_engine",
    "vital_signs_simulator",
    "ai_adapter",
    "greeting_skill",
    "digital_life",
    "activity_consistency_manager",
    "location_logic_checker",
    "drawing_skill",
    "organ_system_manager",
    "yanran_decision_engine",
    "moments_data_tracker",
    "thinking_chain",
    "financial_data_skill",
    "intimacy_provider",
    "system_adapter",
    "intimacy_security",
    "enhanced_activity_generator",
    "intimacy_analyzer",
    "exploration_effect_evaluator",
    "cognitive_integration",
    "hardware_monitor",
    "exploration_decision_engine",
    "ai_service_adapter",
    "exception_handler",
    "gaode_map_service",
    "life_context",
    "intent_recognizer",
    "activity_iterator",
    "api_service",
    "consistency_validator",
    "enhanced_scheduler",
    "wechat_push_service",
    "location_exploration_agent",
    "search_skill"
  ],
  "init_times": {
    "singleton_manager_start": "2025-09-18 14:52:29",
    "singleton_manager_end": "2025-09-18 14:52:29",
    "event_bus_start": "2025-09-18 14:52:29",
    "event_bus_end": "2025-09-18 14:52:29",
    "exception_handler_start": "2025-09-18 14:52:29",
    "exception_handler_end": "2025-09-18 14:52:29",
    "life_context_start": "2025-09-18 14:52:29",
    "life_context_end": "2025-09-18 14:52:29",
    "ai_adapter_start": "2025-09-18 14:52:29",
    "ai_adapter_end": "2025-09-18 14:52:29",
    "ai_service_adapter_start": "2025-09-18 14:52:29",
    "ai_service_adapter_end": "2025-09-18 14:52:29",
    "thinking_chain_start": "2025-09-18 14:52:29",
    "thinking_chain_end": "2025-09-18 14:52:29",
    "middleware_start": "2025-09-18 14:52:29",
    "middleware_end": "2025-09-18 14:52:34",
    "autonomous_systems_start": "2025-09-18 14:52:34",
    "autonomous_systems_end": "2025-09-18 14:52:40",
    "new_systems_start": "2025-09-18 14:52:40",
    "new_systems_end": "2025-09-18 14:52:42",
    "mysql_connector_start": "2025-09-18 14:52:42",
    "mysql_connector_end": "2025-09-18 14:52:43",
    "emotions_sync_start": "2025-09-18 14:52:43",
    "emotions_sync_end": "2025-09-18 14:55:56",
    "core_components_start": "2025-09-18 14:55:56",
    "core_components_end": "2025-09-18 14:55:56",
    "data_persistence_start": "2025-09-18 14:55:56",
    "data_persistence_end": "2025-09-18 14:55:56",
    "skills_start": "2025-09-18 14:55:56",
    "skills_end": "2025-09-18 14:56:08",
    "cognitive_start": "2025-09-18 14:56:08",
    "cognitive_end": "2025-09-18 14:56:13",
    "digital_life_start": "2025-09-18 14:56:13",
    "digital_life_end": "2025-09-18 14:56:13",
    "enhanced_scheduler_start": "2025-09-18 14:56:13",
    "enhanced_scheduler_end": "2025-09-18 14:56:13",
    "critical_fixes_start": "2025-09-18 14:56:13",
    "critical_fixes_end": "2025-09-18 14:56:13",
    "intimacy_security_start": "2025-09-18 14:56:13",
    "intimacy_security_end": "2025-09-18 14:56:13",
    "yanran_decision_engine_start": "2025-09-18 14:56:13",
    "yanran_decision_engine_end": "2025-09-18 14:56:13",
    "wechat_push_service_start": "2025-09-18 14:56:13",
    "wechat_push_service_end": "2025-09-18 14:56:13",
    "organ_system_manager_start": "2025-09-18 14:56:13",
    "organ_system_manager_end": "2025-09-18 14:56:13",
    "cognitive_modules_start": "2025-09-18 14:56:13",
    "cognitive_modules_end": "2025-09-18 14:56:13",
    "consciousness_persistence_start": "2025-09-18 14:56:13",
    "consciousness_persistence_end": "2025-09-18 14:56:17",
    "comprehensive_components_start": "2025-09-18 14:56:17",
    "comprehensive_components_end": "2025-09-18 14:56:19",
    "total": 230.40375089645386
  }
}
 * Debug mode: off
2025-09-18 14:56:23,922 - Digital - [main.success] - ✅ 系统已启动，按Ctrl+C停止...
2025-09-18 14:56:23,923 - Digital - [werkzeug._log] - ℹ️  [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:56839
2025-09-18 14:56:23,924 - Digital - [werkzeug._log] - ℹ️  [33mPress CTRL+C to quit[0m
2025-09-18 14:56:24,532 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-18 14:56:24,537 - Digital - [neural_performance_monitor.__init__] - ℹ️  🔥 神经网络性能监控系统初始化完成
2025-09-18 14:56:24,640 - Digital - [neural_memory_activator._cleanup_old_memories] - ℹ️  🧹 清理记忆：保留 1600 条，删除 2194 条
2025-09-18 14:56:24,892 - Digital - [werkzeug._log] - ℹ️  127.0.0.1 - - [18/Sep/2025 14:56:24] "GET /api/health HTTP/1.1" 200 -
2025-09-18 14:56:24,903 - Digital - [werkzeug._log] - ℹ️  127.0.0.1 - - [18/Sep/2025 14:56:24] "GET /api/status HTTP/1.1" 200 -
2025-09-18 14:56:44,245 - Digital - [LifeOrgan.世界感知器官._execute_world_perception] - ℹ️  🌍 开始执行世界感知...
2025-09-18 14:56:46,278 - Digital - [LifeOrgan.世界感知器官.get_hot_topics] - ❌ 🌍 热搜API错误: HTTP error occurred: 500
2025-09-18 14:56:46,278 - Digital - [LifeOrgan.世界感知器官.get_hot_topics] - ❌ 🌍 错误详情: Server error '500 Internal Server Error' for url 'https://api-hot.imsyy.top/zhihu'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/500
2025-09-18 14:56:46,279 - Digital - [LifeOrgan.世界感知器官._get_hot_topics_data] - ⚠️  🌍 zhihu 平台获取失败或无数据
2025-09-18 14:56:46,279 - Digital - [datasource.news_connector.__init__] - ℹ️  🌍 新闻连接器初始化完成，API无需密钥
2025-09-18 14:57:10,430 - Digital - [ProactiveExpressionOrgan.timer_monitoring] - ℹ️  💬 🚀 系统启动完成，数字生命体主动表达系统激活
2025-09-18 14:57:10,434 - Digital - [werkzeug._log] - ℹ️  127.0.0.1 - - [18/Sep/2025 14:57:10] "GET /api/health HTTP/1.1" 200 -
2025-09-18 14:57:10,436 - Digital - [ProactiveExpressionOrgan.timer_monitoring] - ℹ️  💬 ⏰ 第1次表达检查 [14:57:10] - 数字生命体保持活跃
2025-09-18 14:57:10,441 - Digital - [utilities.expression_time_controller.__init__] - ℹ️  ⏰ 表达时间控制器初始化完成
2025-09-18 14:57:10,442 - Digital - [workday_cache_manager.is_workday_cached] - ℹ️  🗓️ 🔍 缓存未命中，查询数据库: 2025-09-18
2025-09-18 14:57:10,443 - Digital - [workday_cache_manager.success] - ✅ 🗓️ ✅ 工作日信息已缓存: 2025-09-18 -> True
2025-09-18 14:57:10,446 - Digital - [ProactiveExpressionOrgan._sync_world_perception_trigger] - ℹ️  💬 🌍 发现 3 个重要事件，触发主动表达
2025-09-18 14:57:10,446 - Digital - [ProactiveExpressionOrgan._sync_morning_greeting_trigger] - ℹ️  💬 🌅 [调试] 早安问候触发器检查开始 - 当前时间: 14:57:10
2025-09-18 14:57:10,446 - Digital - [ProactiveExpressionOrgan._sync_morning_greeting_trigger] - ℹ️  💬 🌅 [调试] 工作日判断结果: True
2025-09-18 14:57:10,447 - Digital - [ProactiveExpressionOrgan._sync_morning_greeting_trigger] - ℹ️  💬 🌅 [调试] 使用wechat_config.json配置的早安问候时间: 08:30-09:30
2025-09-18 14:57:10,447 - Digital - [ProactiveExpressionOrgan._sync_morning_greeting_trigger] - ℹ️  💬 🌅 [调试] 时间窗口检查 - 当前: 897分钟 (14:57), 窗口: 510-570分钟
2025-09-18 14:57:10,447 - Digital - [ProactiveExpressionOrgan._sync_morning_greeting_trigger] - ℹ️  💬 🌅 [调试] 不在早安问候时间窗口内，跳过
2025-09-18 14:57:10,447 - Digital - [ProactiveExpressionOrgan._parallel_trigger_check] - ℹ️  💬 🧠 收集到 1 个激活的触发器: ['world_perception']
2025-09-18 14:57:10,447 - Digital - [ProactiveExpressionOrgan._parallel_trigger_check] - ℹ️  💬 🧠 冲突解决后可执行触发器: ['world_perception']
2025-09-18 14:57:10,447 - Digital - [ProactiveExpressionOrgan._intelligent_schedule_execution] - ℹ️  💬 🧠 📅 开始智能调度执行 1 个触发器
2025-09-18 14:57:10,448 - Digital - [ProactiveExpressionOrgan._intelligent_schedule_execution] - ℹ️  💬 🧠 🚀 立即执行触发器: world_perception
2025-09-18 14:57:10,448 - Digital - [ProactiveExpressionOrgan._sync_execute_expression] - ℹ️  💬 🎯 开始执行 world_perception 类型的主动表达
2025-09-18 14:57:10,448 - Digital - [ProactiveExpressionOrgan._execute_general_expression] - ℹ️  💬 🎯 开始world_perception类型主动表达流程
2025-09-18 14:57:10,448 - Digital - [ProactiveExpressionOrgan._select_target_users_for_expression] - ℹ️  🧠 开始AI智能选择目标好友 - 触发类型: world_perception
2025-09-18 14:57:10,448 - Digital - [intelligent_user_selector.select_users_for_proactive_expression] - ℹ️  🧠 开始智能用户筛选 - 表达类型: general
2025-09-18 14:57:11,196 - Digital - [intelligent_user_selector._get_user_profiles] - ℹ️  📊 获取到 107 个用户画像数据
2025-09-18 14:57:11,196 - Digital - [intelligent_user_selector.select_users_for_proactive_expression] - ℹ️  🔍 基础筛选完成: 107/107 用户通过
2025-09-18 14:57:11,586 - Digital - [intelligent_user_selector.success] - ✅ ✅ 已更新 0 个用户的emotions表need_reply字段
2025-09-18 14:57:11,587 - Digital - [intelligent_user_selector.success] - ✅ 🎯 智能用户筛选完成: 选择 3 个用户，耗时 1.14s
2025-09-18 14:57:12,015 - Digital - [ProactiveExpressionOrgan.success] - ✅ 🧠 智能emotions筛选完成: 选择 10 个好友
2025-09-18 14:57:12,016 - Digital - [ProactiveExpressionOrgan.success] - ✅ 🎯 AI智能筛选完成: 选择 10 个好友
2025-09-18 14:57:12,016 - Digital - [ProactiveExpressionOrgan._execute_general_expression] - ℹ️  💬 🎯 为world_perception类型表达选择了 10 个好友
2025-09-18 14:57:12,439 - Digital - [real_time_data_collector.success] - ✅ 智能调度器初始化完成
2025-09-18 14:57:12,439 - Digital - [real_time_data_collector.success] - ✅ 趋势感知引擎初始化完成
2025-09-18 14:57:12,440 - Digital - [real_time_data_collector.success] - ✅ 智能分析引擎初始化完成
2025-09-18 14:57:12,440 - Digital - [real_time_data_collector.success] - ✅ 实时数据收集器初始化完成
2025-09-18 14:57:12,440 - Digital - [real_time_data_collector.collect_financial_news] - ℹ️  财经新闻数据缓存为空，正在尝试实时获取...
2025-09-18 14:57:22,970 - Digital - [real_time_data_collector._collect_news_data] - ⚠️  实时新闻API调用失败: 
2025-09-18 14:57:22,970 - Digital - [datasource.akshare_connector.success] - ✅ AKShare金融数据连接器初始化成功
2025-09-18 14:57:23,192 - Digital - [datasource.akshare_connector.success] - ✅ 成功获取 10 条财经新闻
2025-09-18 14:57:23,193 - Digital - [real_time_data_collector.success] - ✅ AKShare财经新闻接口成功获取 10 条新闻
2025-09-18 14:57:23,195 - Digital - [real_time_data_collector.collect_financial_news] - ℹ️  ✅ 财经新闻数据实时获取成功并已更新缓存（内存+Redis）
2025-09-18 14:57:23,195 - Digital - [ProactiveExpressionOrgan._get_realtime_news_for_expression] - ℹ️  🔥 获取到 5 条个性化实时新闻
2025-09-18 14:57:23,195 - Digital - [real_time_data_collector.success] - ✅ 智能调度器初始化完成
2025-09-18 14:57:23,196 - Digital - [real_time_data_collector.success] - ✅ 趋势感知引擎初始化完成
2025-09-18 14:57:23,196 - Digital - [real_time_data_collector.success] - ✅ 智能分析引擎初始化完成
2025-09-18 14:57:23,196 - Digital - [real_time_data_collector.success] - ✅ 实时数据收集器初始化完成
2025-09-18 14:57:23,196 - Digital - [real_time_data_collector.collect_hot_topics] - ⚠️  热点话题数据缓存为空，尝试实时获取
2025-09-18 14:57:23,198 - Digital - [real_time_data_collector.collect_hot_topics] - ℹ️  ✅ 热点话题数据实时获取成功并已更新缓存（内存+Redis）
2025-09-18 14:57:23,199 - Digital - [ProactiveExpressionOrgan._gather_expression_content_sources] - ℹ️  🔥 获取到 5 个真实重要事件
2025-09-18 14:57:23,199 - Digital - [ProactiveExpressionOrgan._generate_base_expression_from_real_data] - ℹ️  💬 基于通用触发生成(含5条新闻): world_perception
2025-09-18 14:57:23,199 - Digital - [ProactiveExpressionOrgan._generate_personalized_expression_for_users] - ℹ️  💬 ✅ 基于真实数据生成基础表达内容: 91字
2025-09-18 14:57:23,199 - Digital - [ProactiveExpressionOrgan._execute_general_expression] - ℹ️  💬 📢 [林嫣然主动表达] 最近关注到一些有趣的事情，想和大家聊聊


【相关重要事件】
事件: 富时A50期指连续夜盘收涨0.19% 报15210点

【相关新闻】
新闻: 中文互联网基础语料3.0正式发布 
2025-09-18 14:57:23,199 - Digital - [ProactiveExpressionOrgan.success] - ✅ 💬 ✅ 主动表达完成: world_perception - 最近关注到一些有趣的事情，想和大家聊聊


【相关重要事件】...
2025-09-18 14:57:23,200 - Digital - [utilities.redis_history_manager.success] - ✅ Redis连接初始化成功
🔥 检测到生产环境，自动设置日志级别为INFO
2025-09-18 14:57:23,218 - Digital - [adapters.enhanced_context_builder.success] - ✅ ✅ 增强上下文构建器2.0初始化成功 (延迟加载模式)
2025-09-18 14:57:23,218 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  🚀 为用户 wxid_jpcc3bco3rj022 构建增强动态上下文...
2025-09-18 14:57:25,237 - Digital - [adapters.legacy.get_related_history] - ℹ️  从数据库获取到历史记录: 用户 5 条, 助手 5 条
2025-09-18 14:57:25,442 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  ✅ 最终确定的用户名: 丛岗君
2025-09-18 14:57:25,837 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-18 14:57:26,109 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  🚀 开始构建增强动态上下文2.0...
2025-09-18 14:57:26,112 - Digital - [adapters.enhanced_context_builder.success] - ✅ ✅ 增强动态上下文2.0构建成功！
2025-09-18 14:57:26,112 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  📊 组件可用性: 10/13 个组件可用
2025-09-18 14:57:33,438 - Digital - [ProactiveExpressionOrgan._personalize_expression_content] - ℹ️  🎨 AI个性化表达成功: 丛岗君 - 刚看到富时A50期指夜盘涨了0.19% 还有中文互联网基础语...
2025-09-18 14:57:33,439 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  🚀 开始主动表达WeChat推送: wxid_jpcc3bco3rj022(wxid_jpcc3bco3rj022)
2025-09-18 14:57:33,440 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  🔍 主动表达推送结果: True
2025-09-18 14:57:33,440 - Digital - [ProactiveExpressionOrgan.success] - ✅ ✅ 主动表达已推送到WeChat: wxid_jpcc3bco3rj022(wxid_jpcc3bco3rj022)
2025-09-18 14:57:33,440 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  📝 主动表达已发布到记忆系统: wxid_jpcc3bco3rj022(wxid_jpcc3bco3rj022)
2025-09-18 14:57:33,441 - Digital - [ProactiveExpressionOrgan._send_expression_to_wechat_unified_service] - ℹ️  💬 主动表达推送任务已提交到线程池: wxid_jpcc3bco3rj022(wxid_jpcc3bco3rj022)
2025-09-18 14:57:33,543 - Digital - [utilities.redis_history_manager.success] - ✅ Redis连接初始化成功
2025-09-18 14:57:33,543 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  🚀 为用户 wxid_bb5kn6jgqnm121 构建增强动态上下文...
2025-09-18 14:57:35,211 - Digital - [adapters.legacy.success] - ✅ 从向量库获取到 5 条用户消息, 5 条助手消息
2025-09-18 14:57:35,424 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  ✅ 最终确定的用户名: 子淡ھۇجۇم
2025-09-18 14:57:36,024 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  🚀 开始构建增强动态上下文2.0...
2025-09-18 14:57:36,027 - Digital - [adapters.enhanced_context_builder.success] - ✅ ✅ 增强动态上下文2.0构建成功！
2025-09-18 14:57:36,028 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  📊 组件可用性: 10/13 个组件可用
2025-09-18 14:57:43,051 - Digital - [utilities.cache_manager.success] - ✅ 🔥 初始化缓存管理器...
2025-09-18 14:57:43,052 - Digital - [utilities.cache_manager._start_cleanup_thread] - ℹ️  🧹 缓存清理线程已启动
2025-09-18 14:57:43,052 - Digital - [utilities.cache_manager.success] - ✅ ✅ 缓存管理器初始化完成
2025-09-18 14:57:43,296 - Digital - [ProactiveExpressionOrgan._personalize_expression_content] - ℹ️  🎨 AI个性化表达成功: 子淡ھۇجۇم - 富时A50期指连续夜盘收涨0.19% 报15210点 中文互...
2025-09-18 14:57:43,296 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  🚀 开始主动表达WeChat推送: wxid_bb5kn6jgqnm121(wxid_bb5kn6jgqnm121)
2025-09-18 14:57:43,296 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  🔍 主动表达推送结果: True
2025-09-18 14:57:43,297 - Digital - [ProactiveExpressionOrgan.success] - ✅ ✅ 主动表达已推送到WeChat: wxid_bb5kn6jgqnm121(wxid_bb5kn6jgqnm121)
2025-09-18 14:57:43,298 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  📝 主动表达已发布到记忆系统: wxid_bb5kn6jgqnm121(wxid_bb5kn6jgqnm121)
2025-09-18 14:57:43,298 - Digital - [ProactiveExpressionOrgan._send_expression_to_wechat_unified_service] - ℹ️  💬 主动表达推送任务已提交到线程池: wxid_bb5kn6jgqnm121(wxid_bb5kn6jgqnm121)
2025-09-18 14:57:43,399 - Digital - [utilities.redis_history_manager.success] - ✅ Redis连接初始化成功
2025-09-18 14:57:43,400 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  🚀 为用户 wxid_2jggu3ik924121 构建增强动态上下文...
2025-09-18 14:57:45,005 - Digital - [adapters.legacy.success] - ✅ 从向量库获取到 5 条用户消息, 5 条助手消息
2025-09-18 14:57:45,219 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  ✅ 最终确定的用户名: 龙.之境界
2025-09-18 14:57:45,867 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  🚀 开始构建增强动态上下文2.0...
2025-09-18 14:57:45,869 - Digital - [adapters.enhanced_context_builder.success] - ✅ ✅ 增强动态上下文2.0构建成功！
2025-09-18 14:57:45,869 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  📊 组件可用性: 10/13 个组件可用
2025-09-18 14:57:51,951 - Digital - [ProactiveExpressionOrgan._personalize_expression_content] - ℹ️  🎨 AI个性化表达成功: 龙.之境界 - 富时A50期指连续夜盘收涨0.19%报15210点 中文互联...
2025-09-18 14:57:51,951 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  🚀 开始主动表达WeChat推送: wxid_2jggu3ik924121(wxid_2jggu3ik924121)
2025-09-18 14:57:51,952 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  🔍 主动表达推送结果: True
2025-09-18 14:57:51,952 - Digital - [ProactiveExpressionOrgan.success] - ✅ ✅ 主动表达已推送到WeChat: wxid_2jggu3ik924121(wxid_2jggu3ik924121)
2025-09-18 14:57:51,952 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  📝 主动表达已发布到记忆系统: wxid_2jggu3ik924121(wxid_2jggu3ik924121)
2025-09-18 14:57:51,952 - Digital - [ProactiveExpressionOrgan._send_expression_to_wechat_unified_service] - ℹ️  💬 主动表达推送任务已提交到线程池: wxid_2jggu3ik924121(wxid_2jggu3ik924121)
2025-09-18 14:57:52,053 - Digital - [database_utils._initialize_mysql] - ℹ️  ✅ MySQL连接器初始化成功
2025-09-18 14:57:52,272 - Digital - [database_utils.execute_with_retry] - ℹ️  数据库查询成功: 1条记录
2025-09-18 14:57:52,273 - Digital - [utilities.redis_history_manager.success] - ✅ Redis连接初始化成功
2025-09-18 14:57:52,274 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  🚀 为用户 a29325620 构建增强动态上下文...
2025-09-18 14:57:53,848 - Digital - [adapters.legacy.success] - ✅ 从向量库获取到 5 条用户消息, 5 条助手消息
2025-09-18 14:57:54,055 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  ✅ 最终确定的用户名: shine5
2025-09-18 14:57:54,701 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  🚀 开始构建增强动态上下文2.0...
2025-09-18 14:57:54,704 - Digital - [adapters.enhanced_context_builder.success] - ✅ ✅ 增强动态上下文2.0构建成功！
2025-09-18 14:57:54,704 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  📊 组件可用性: 10/13 个组件可用
2025-09-18 14:57:59,449 - Digital - [ProactiveExpressionOrgan._personalize_expression_content] - ℹ️  🎨 AI个性化表达成功: shine5 - 富时A50期指连续夜盘收涨0.19%报15210点...
2025-09-18 14:57:59,450 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  🚀 开始主动表达WeChat推送: a29325620(a29325620)
2025-09-18 14:57:59,450 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  🔍 主动表达推送结果: True
2025-09-18 14:57:59,451 - Digital - [ProactiveExpressionOrgan.success] - ✅ ✅ 主动表达已推送到WeChat: a29325620(a29325620)
2025-09-18 14:57:59,451 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  📝 主动表达已发布到记忆系统: a29325620(a29325620)
2025-09-18 14:57:59,451 - Digital - [ProactiveExpressionOrgan._send_expression_to_wechat_unified_service] - ℹ️  💬 主动表达推送任务已提交到线程池: a29325620(a29325620)
2025-09-18 14:57:59,552 - Digital - [utilities.redis_history_manager.success] - ✅ Redis连接初始化成功
2025-09-18 14:57:59,553 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  🚀 为用户 owen1anymore 构建增强动态上下文...
2025-09-18 14:58:01,043 - Digital - [adapters.legacy.success] - ✅ 从向量库获取到 5 条用户消息, 5 条助手消息
2025-09-18 14:58:01,249 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  ✅ 最终确定的用户名: 董霄
2025-09-18 14:58:01,897 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  🚀 开始构建增强动态上下文2.0...
2025-09-18 14:58:01,900 - Digital - [adapters.enhanced_context_builder.success] - ✅ ✅ 增强动态上下文2.0构建成功！
2025-09-18 14:58:01,900 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  📊 组件可用性: 10/13 个组件可用
2025-09-18 14:58:07,929 - Digital - [ProactiveExpressionOrgan._personalize_expression_content] - ℹ️  🎨 AI个性化表达成功: 董霄 - 董霄 富时A50期指连续夜盘收涨0.19%报15210点 还...
2025-09-18 14:58:07,930 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  🚀 开始主动表达WeChat推送: owen1anymore(owen1anymore)
2025-09-18 14:58:07,930 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  🔍 主动表达推送结果: True
2025-09-18 14:58:07,930 - Digital - [ProactiveExpressionOrgan.success] - ✅ ✅ 主动表达已推送到WeChat: owen1anymore(owen1anymore)
2025-09-18 14:58:07,930 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  📝 主动表达已发布到记忆系统: owen1anymore(owen1anymore)
2025-09-18 14:58:07,931 - Digital - [ProactiveExpressionOrgan._send_expression_to_wechat_unified_service] - ℹ️  💬 主动表达推送任务已提交到线程池: owen1anymore(owen1anymore)
2025-09-18 14:58:08,032 - Digital - [utilities.redis_history_manager.success] - ✅ Redis连接初始化成功
2025-09-18 14:58:08,033 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  🚀 为用户 liu_defei_cool 构建增强动态上下文...
2025-09-18 14:58:09,626 - Digital - [adapters.legacy.success] - ✅ 从向量库获取到 5 条用户消息, 5 条助手消息
2025-09-18 14:58:09,838 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  ✅ 最终确定的用户名: 🔆
2025-09-18 14:58:10,443 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  🚀 开始构建增强动态上下文2.0...
2025-09-18 14:58:10,450 - Digital - [adapters.enhanced_context_builder.success] - ✅ ✅ 增强动态上下文2.0构建成功！
2025-09-18 14:58:10,450 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  📊 组件可用性: 10/13 个组件可用
2025-09-18 14:58:17,620 - Digital - [ProactiveExpressionOrgan._personalize_expression_content] - ℹ️  🎨 AI个性化表达成功: 🔆 - 富时A50期指连续夜盘收涨0.19%报15210点 中文互联...
2025-09-18 14:58:17,621 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  🚀 开始主动表达WeChat推送: liu_defei_cool(liu_defei_cool)
2025-09-18 14:58:17,621 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  🔍 主动表达推送结果: True
2025-09-18 14:58:17,621 - Digital - [ProactiveExpressionOrgan.success] - ✅ ✅ 主动表达已推送到WeChat: liu_defei_cool(liu_defei_cool)
2025-09-18 14:58:17,621 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  📝 主动表达已发布到记忆系统: liu_defei_cool(liu_defei_cool)
2025-09-18 14:58:17,622 - Digital - [ProactiveExpressionOrgan._send_expression_to_wechat_unified_service] - ℹ️  💬 主动表达推送任务已提交到线程池: liu_defei_cool(liu_defei_cool)
2025-09-18 14:58:17,723 - Digital - [utilities.redis_history_manager.success] - ✅ Redis连接初始化成功
2025-09-18 14:58:17,723 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  🚀 为用户 wxid_o0gzkuya5ose22 构建增强动态上下文...
2025-09-18 14:58:19,312 - Digital - [adapters.legacy.success] - ✅ 从向量库获取到 5 条用户消息, 5 条助手消息
2025-09-18 14:58:19,526 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  ✅ 最终确定的用户名: 燕子
2025-09-18 14:58:20,124 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  🚀 开始构建增强动态上下文2.0...
2025-09-18 14:58:20,127 - Digital - [adapters.enhanced_context_builder.success] - ✅ ✅ 增强动态上下文2.0构建成功！
2025-09-18 14:58:20,127 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  📊 组件可用性: 10/13 个组件可用
2025-09-18 14:58:25,014 - Digital - [ProactiveExpressionOrgan._personalize_expression_content] - ℹ️  🎨 AI个性化表达成功: 燕子 - 富时A50期指连续夜盘收涨0.19% 报15210点 中文互...
2025-09-18 14:58:25,015 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  🚀 开始主动表达WeChat推送: wxid_o0gzkuya5ose22(wxid_o0gzkuya5ose22)
2025-09-18 14:58:25,015 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  🔍 主动表达推送结果: True
2025-09-18 14:58:25,015 - Digital - [ProactiveExpressionOrgan.success] - ✅ ✅ 主动表达已推送到WeChat: wxid_o0gzkuya5ose22(wxid_o0gzkuya5ose22)
2025-09-18 14:58:25,016 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  📝 主动表达已发布到记忆系统: wxid_o0gzkuya5ose22(wxid_o0gzkuya5ose22)
2025-09-18 14:58:25,016 - Digital - [ProactiveExpressionOrgan._send_expression_to_wechat_unified_service] - ℹ️  💬 主动表达推送任务已提交到线程池: wxid_o0gzkuya5ose22(wxid_o0gzkuya5ose22)
2025-09-18 14:58:25,117 - Digital - [utilities.redis_history_manager.success] - ✅ Redis连接初始化成功
2025-09-18 14:58:25,118 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  🚀 为用户 wxid_122t73169az622 构建增强动态上下文...
2025-09-18 14:58:26,606 - Digital - [adapters.legacy.success] - ✅ 从向量库获取到 5 条用户消息, 5 条助手消息
2025-09-18 14:58:26,869 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  ✅ 最终确定的用户名: 丅an风
2025-09-18 14:58:27,023 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-18 14:58:27,267 - Digital - [datasource.news_connector.warning_status] - ⚠️  新闻API服务端超时/错误 (524) (尝试 1/3), 响应预览: <html 7764 chars>: <!DOCTYPE  ...
2025-09-18 14:58:27,419 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  🚀 开始构建增强动态上下文2.0...
2025-09-18 14:58:27,422 - Digital - [adapters.enhanced_context_builder.success] - ✅ ✅ 增强动态上下文2.0构建成功！
2025-09-18 14:58:27,422 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  📊 组件可用性: 10/13 个组件可用
2025-09-18 14:58:33,670 - Digital - [ProactiveExpressionOrgan._personalize_expression_content] - ℹ️  🎨 AI个性化表达成功: 丅an风 - 刚刷到富时A50期指连续夜盘收涨0.19% 报15210点 ...
2025-09-18 14:58:33,671 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  🚀 开始主动表达WeChat推送: wxid_122t73169az622(wxid_122t73169az622)
2025-09-18 14:58:33,672 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  🔍 主动表达推送结果: True
2025-09-18 14:58:33,672 - Digital - [ProactiveExpressionOrgan.success] - ✅ ✅ 主动表达已推送到WeChat: wxid_122t73169az622(wxid_122t73169az622)
2025-09-18 14:58:33,673 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  📝 主动表达已发布到记忆系统: wxid_122t73169az622(wxid_122t73169az622)
2025-09-18 14:58:33,674 - Digital - [ProactiveExpressionOrgan._send_expression_to_wechat_unified_service] - ℹ️  💬 主动表达推送任务已提交到线程池: wxid_122t73169az622(wxid_122t73169az622)
2025-09-18 14:58:33,775 - Digital - [utilities.redis_history_manager.success] - ✅ Redis连接初始化成功
2025-09-18 14:58:33,775 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  🚀 为用户 wxid_2siigw2co5o452 构建增强动态上下文...
2025-09-18 14:58:35,293 - Digital - [adapters.legacy.success] - ✅ 从向量库获取到 5 条用户消息, 5 条助手消息
2025-09-18 14:58:35,567 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  ✅ 最终确定的用户名: Z.Brian
2025-09-18 14:58:36,150 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  🚀 开始构建增强动态上下文2.0...
2025-09-18 14:58:36,153 - Digital - [adapters.enhanced_context_builder.success] - ✅ ✅ 增强动态上下文2.0构建成功！
2025-09-18 14:58:36,153 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  📊 组件可用性: 10/13 个组件可用
2025-09-18 14:58:42,310 - Digital - [ProactiveExpressionOrgan._personalize_expression_content] - ℹ️  🎨 AI个性化表达成功: Z.Brian - 刚看到富时A50期指连续夜盘收涨0.19%报15210点 还...
2025-09-18 14:58:42,310 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  🚀 开始主动表达WeChat推送: wxid_2siigw2co5o452(wxid_2siigw2co5o452)
2025-09-18 14:58:42,311 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  🔍 主动表达推送结果: True
2025-09-18 14:58:42,311 - Digital - [ProactiveExpressionOrgan.success] - ✅ ✅ 主动表达已推送到WeChat: wxid_2siigw2co5o452(wxid_2siigw2co5o452)
2025-09-18 14:58:42,312 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  📝 主动表达已发布到记忆系统: wxid_2siigw2co5o452(wxid_2siigw2co5o452)
2025-09-18 14:58:42,312 - Digital - [ProactiveExpressionOrgan._send_expression_to_wechat_unified_service] - ℹ️  💬 主动表达推送任务已提交到线程池: wxid_2siigw2co5o452(wxid_2siigw2co5o452)
2025-09-18 14:58:42,413 - Digital - [utilities.redis_history_manager.success] - ✅ Redis连接初始化成功
2025-09-18 14:58:42,414 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  🚀 为用户 wxid_7a24b5u8yz6921 构建增强动态上下文...
2025-09-18 14:58:43,928 - Digital - [adapters.legacy.success] - ✅ 从向量库获取到 5 条用户消息, 5 条助手消息
2025-09-18 14:58:44,150 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  ✅ 最终确定的用户名: 李茂峰
2025-09-18 14:58:44,760 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  🚀 开始构建增强动态上下文2.0...
2025-09-18 14:58:44,763 - Digital - [adapters.enhanced_context_builder.success] - ✅ ✅ 增强动态上下文2.0构建成功！
2025-09-18 14:58:44,763 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  📊 组件可用性: 10/13 个组件可用
2025-09-18 14:58:50,801 - Digital - [ProactiveExpressionOrgan._personalize_expression_content] - ℹ️  🎨 AI个性化表达成功: 李茂峰 - 富时A50期指夜盘收涨0.19%报15210点 中文互联网基...
2025-09-18 14:58:50,802 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  🚀 开始主动表达WeChat推送: wxid_7a24b5u8yz6921(wxid_7a24b5u8yz6921)
2025-09-18 14:58:50,802 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  🔍 主动表达推送结果: True
2025-09-18 14:58:50,802 - Digital - [ProactiveExpressionOrgan.success] - ✅ ✅ 主动表达已推送到WeChat: wxid_7a24b5u8yz6921(wxid_7a24b5u8yz6921)
2025-09-18 14:58:50,803 - Digital - [ProactiveExpressionOrgan.run_push_in_thread] - ℹ️  📝 主动表达已发布到记忆系统: wxid_7a24b5u8yz6921(wxid_7a24b5u8yz6921)
2025-09-18 14:58:50,803 - Digital - [ProactiveExpressionOrgan._send_expression_to_wechat_unified_service] - ℹ️  💬 主动表达推送任务已提交到线程池: wxid_7a24b5u8yz6921(wxid_7a24b5u8yz6921)
2025-09-18 14:58:50,904 - Digital - [ProactiveExpressionOrgan._send_expression_to_wechat_unified_service] - ℹ️  💬 🚀 主动表达WeChat统一推送完成: 10 个好友
2025-09-18 14:58:50,904 - Digital - [ProactiveExpressionOrgan.success] - ✅ 💬 ✅ WeChat统一推送服务调用成功: world_perception
2025-09-18 14:59:28,212 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-18 15:00:09,367 - Digital - [datasource.news_connector.warning_status] - ⚠️  新闻API服务端超时/错误 (524) (尝试 2/3), 响应预览: <html 7609 chars>: <!DOCTYPE  ...
2025-09-18 15:00:29,407 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-18 15:01:11,528 - Digital - [adapters.database_sync_manager._analyze_sync_data] - ℹ️  分析结果: 新用户 0个, 需更新 0个
2025-09-18 15:01:13,513 - Digital - [scripts_integration_service.get_current_activity_suggestions] - ℹ️  📊 生成了 2 个EnhancedActivity建议
2025-09-18 15:01:16,840 - Digital - [main.chat_message_api] - ℹ️  ====API聊天请求====: user_id=liu_defei_cool, user_name=🔆, user_sex=1, isgroup=1, session_id = 25821823843@chatroom, is_Segment=0, _token=12c4521a-7270-4553-a2ac-3af10244a35b, _appid=wx_574bN70yW1q0vTSFaaSHU, message=@嫣然  你长什么样？ 生成一张图片来让大家看看
2025-09-18 15:01:16,840 - Digital - [main.enhanced_process_message] - ℹ️  🚀 [req_e1bc0dad4a2f] 开始处理用户请求: user_id=liu_defei_cool, thread=140149091145472
2025-09-18 15:01:16,840 - Digital - [main.enhanced_process_message] - ℹ️  📥 [req_e1bc0dad4a2f] 接收到用户请求: user_id=liu_defei_cool, user_name=🔆
2025-09-18 15:01:16,840 - Digital - [main._validate_api_data] - ℹ️  ✅ 接收API数据: user_id=liu_defei_cool, user_name=🔆, user_sex=1
2025-09-18 15:01:16,840 - Digital - [unified_user_manager.process_user_request] - ℹ️  🔄 [umgr_77d64bc4] Thread-140149091145472 开始处理用户请求: user_id=liu_defei_cool
2025-09-18 15:01:17,066 - Digital - [unified_user_manager._create_user] - ℹ️  ✅ 使用API传入的有效用户名: 🔆
2025-09-18 15:01:17,292 - Digital - [unified_user_manager.get_or_create_user] - ℹ️  创建新用户: liu_defei_cool
2025-09-18 15:01:17,292 - Digital - [unified_user_manager._create_session_with_id] - ℹ️  创建指定ID会话: 25821823843@chatroom (用户: liu_defei_cool)
2025-09-18 15:01:17,292 - Digital - [unified_user_manager.process_user_request] - ℹ️  🔗 [umgr_77d64bc4] 创建指定ID会话: 25821823843@chatroom
2025-09-18 15:01:17,292 - Digital - [unified_user_manager.success] - ✅ ✅ [umgr_77d64bc4] 用户请求处理完成: liu_defei_cool (🔆)
2025-09-18 15:01:17,294 - Digital - [main.enhanced_process_message] - ℹ️  ✅ 使用API传入的用户名: 🔆
2025-09-18 15:01:17,294 - Digital - [main.enhanced_process_message] - ℹ️  ✅ 确定的最终用户名: 🔆
2025-09-18 15:01:17,295 - Digital - [main._verify_saved_data] - ℹ️  ✅ contacts.json验证通过: liu_defei_cool -> 🔆
2025-09-18 15:01:17,296 - Digital - [main._verify_saved_data] - ℹ️  ✅ unified_user_manager验证通过: liu_defei_cool -> 🔆
2025-09-18 15:01:17,297 - Digital - [main._verify_saved_data] - ℹ️  ✅ 数据保存验证通过: liu_defei_cool -> 🔆
2025-09-18 15:01:17,297 - Digital - [main.success] - ✅ 统一用户管理: liu_defei_cool (🔆) - 现有用户, 新会话
2025-09-18 15:01:17,297 - Digital - [main.enhanced_process_message] - ℹ️  收到用户消息: @嫣然  你长什么样？ 生成一张图片来让大家看看... [用户:liu_defei_cool/🔆]
2025-09-18 15:01:17,298 - Digital - [main.enhanced_process_message] - ℹ️  🔗 使用传入的session_id: 25821823843@chatroom
2025-09-18 15:01:17,334 - Digital - [UnifiedDataFlowManager._monitor_data_flow_once] - ℹ️  数据流监控: 活跃0, 成功0, 失败0
2025-09-18 15:01:17,336 - Digital - [UnifiedDataFlowManager.__init__] - ℹ️  统一数据流管理器初始化完成
2025-09-18 15:01:17,346 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'unified_data_flow_manager'
2025-09-18 15:01:17,347 - Digital - [cognitive_modules.decision.yanran_response_decision.success] - ✅ 初始化林嫣然智能响应决策系统...
2025-09-18 15:01:17,347 - Digital - [cognitive_modules.decision.yanran_response_decision.success] - ✅ 林嫣然智能响应决策系统初始化完成
2025-09-18 15:01:17,348 - Digital - [cognitive_modules.decision.yanran_response_decision.handle_delayed_response_decision] - ℹ️  🔍 开始处理延迟回复决策 (用户: liu_defei_cool)
2025-09-18 15:01:17,756 - Digital - [ContactsManager._load_contacts] - ℹ️  加载联系人数据: 63个用户
2025-09-18 15:01:18,200 - Digital - [neural_consciousness.save_model] - ℹ️  神经网络模型已保存到: /root/yanran_digital_life/data/neural_models/consciousness_enhancer.pkl
2025-09-18 15:01:18,227 - Digital - [advanced_neural_consciousness.save_memory] - ℹ️  💾 记忆网络数据已保存到: /root/yanran_digital_life/data/neural_models/consciousness_memory.json
2025-09-18 15:01:18,424 - Digital - [advanced_neural_consciousness.save_memory] - ℹ️  💾 记忆网络数据已保存到: /root/yanran_digital_life/data/neural_models/consciousness_memory.json
2025-09-18 15:01:18,948 - Digital - [main.chat_message_api] - ℹ️  ====API聊天请求====: user_id=chatroom_49441902085, user_name=《AI魔法詹学院》群友：AI画中画, user_sex=0, isgroup=1, session_id = 49441902085@chatroom, is_Segment=0, _token=12c4521a-7270-4553-a2ac-3af10244a35b, _appid=wx_574bN70yW1q0vTSFaaSHU, message=快手光合文旅X画爷爷工作室，锅气里的甘肃，趁热，请！（制作：AI画中画、山雨、干饭的星辰；剪辑：Pp）m
2025-09-18 15:01:18,948 - Digital - [main.enhanced_process_message] - ℹ️  🚀 [req_8aabc967a2b8] 开始处理用户请求: user_id=chatroom_49441902085, thread=140149007218432
2025-09-18 15:01:18,949 - Digital - [main.enhanced_process_message] - ℹ️  📥 [req_8aabc967a2b8] 接收到用户请求: user_id=chatroom_49441902085, user_name=《AI魔法詹学院》群友：AI画中画
2025-09-18 15:01:18,949 - Digital - [main._validate_api_data] - ℹ️  ✅ 接收API数据: user_id=chatroom_49441902085, user_name=《AI魔法詹学院》群友：AI画中画, user_sex=0
2025-09-18 15:01:18,949 - Digital - [unified_user_manager.process_user_request] - ℹ️  🔄 [umgr_90fbc3ff] Thread-140149007218432 开始处理用户请求: user_id=chatroom_49441902085
2025-09-18 15:01:19,177 - Digital - [unified_user_manager._create_user] - ℹ️  ✅ 使用API传入的有效用户名: 《AI魔法詹学院》群友：AI画中画
2025-09-18 15:01:19,178 - Digital - [ContactsManager.ensure_user_exists] - ℹ️  🔄 检测到用户信息变更，更新contacts.json: chatroom_49441902085
2025-09-18 15:01:19,178 - Digital - [ContactsManager.ensure_user_exists] - ℹ️     昵称: 《AI魔法詹学院》群友：AI动漫_小强 -> 《AI魔法詹学院》群友：AI画中画
2025-09-18 15:01:19,179 - Digital - [ContactsManager.ensure_user_exists] - ℹ️     性别: 1 -> 1
2025-09-18 15:01:19,406 - Digital - [unified_user_manager.get_or_create_user] - ℹ️  创建新用户: chatroom_49441902085
2025-09-18 15:01:19,406 - Digital - [unified_user_manager._create_session_with_id] - ℹ️  创建指定ID会话: 49441902085@chatroom (用户: chatroom_49441902085)
2025-09-18 15:01:19,407 - Digital - [unified_user_manager.process_user_request] - ℹ️  🔗 [umgr_90fbc3ff] 创建指定ID会话: 49441902085@chatroom
2025-09-18 15:01:19,407 - Digital - [unified_user_manager.success] - ✅ ✅ [umgr_90fbc3ff] 用户请求处理完成: chatroom_49441902085 (《AI魔法詹学院》群友：AI画中画)
2025-09-18 15:01:19,407 - Digital - [main.enhanced_process_message] - ℹ️  ✅ 使用API传入的用户名: 《AI魔法詹学院》群友：AI画中画
2025-09-18 15:01:19,407 - Digital - [main.enhanced_process_message] - ℹ️  ✅ 确定的最终用户名: 《AI魔法詹学院》群友：AI画中画
2025-09-18 15:01:19,408 - Digital - [main._verify_saved_data] - ℹ️  ✅ contacts.json验证通过: chatroom_49441902085 -> 《AI魔法詹学院》群友：AI画中画
2025-09-18 15:01:19,408 - Digital - [main._verify_saved_data] - ℹ️  ✅ unified_user_manager验证通过: chatroom_49441902085 -> 《AI魔法詹学院》群友：AI画中画
2025-09-18 15:01:19,409 - Digital - [main._verify_saved_data] - ℹ️  ✅ 数据保存验证通过: chatroom_49441902085 -> 《AI魔法詹学院》群友：AI画中画
2025-09-18 15:01:19,409 - Digital - [main.success] - ✅ 统一用户管理: chatroom_49441902085 (《AI魔法詹学院》群友：AI画中画) - 现有用户, 新会话
2025-09-18 15:01:19,409 - Digital - [main.enhanced_process_message] - ℹ️  收到用户消息: 快手光合文旅X画爷爷工作室，锅气里的甘肃，趁热，请！（制作：... [用户:chatroom_49441902085/《AI魔法詹学院》群友：AI画中画]
2025-09-18 15:01:19,409 - Digital - [main.enhanced_process_message] - ℹ️  🔗 使用传入的session_id: 49441902085@chatroom
2025-09-18 15:01:19,410 - Digital - [cognitive_modules.decision.yanran_response_decision.handle_delayed_response_decision] - ℹ️  🔍 开始处理延迟回复决策 (用户: chatroom_49441902085)
2025-09-18 15:01:20,569 - Digital - [feedback_learning._load_weights] - ℹ️  从 data/feedback_learning/weights.json 加载权重
2025-09-18 15:01:20,570 - Digital - [feedback_learning.success] - ✅ 反馈学习模块初始化完成
2025-09-18 15:01:20,576 - Digital - [feedback_learning.get_instance] - ℹ️  ✅ 反馈学习模块已注册到singleton_manager
2025-09-18 15:01:20,576 - Digital - [cognitive_modules.decision.yanran_response_decision.should_respond_to_message] - ℹ️  ✅ 完成响应决策 (用户: liu_defei_cool): ResponseDecision.REPLY_NOW
2025-09-18 15:01:20,578 - Digital - [main.enhanced_process_message] - ℹ️  ⚙️ 开始初始化器官系统...
2025-09-18 15:01:20,580 - Digital - [organ_system_manager.initialize_organ_system] - ℹ️  ⚙️ 开始初始化器官系统...
2025-09-18 15:01:20,581 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: safety_protection_organ
2025-09-18 15:01:20,581 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 safety_protection_organ 激活成功 (优先级: CRITICAL)
2025-09-18 15:01:20,581 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: world_perception_organ
2025-09-18 15:01:20,582 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 world_perception_organ 激活成功 (优先级: HIGH)
2025-09-18 15:01:20,582 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: proactive_expression_organ
2025-09-18 15:01:20,583 - Digital - [ProactiveExpressionOrgan.activate] - ℹ️  💬 🚀 主动表达器官正在激活...
2025-09-18 15:01:20,583 - Digital - [ProactiveExpressionOrgan.activate] - ℹ️  💬 ✅ 主动表达器官已激活，监控系统运行中
2025-09-18 15:01:20,584 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 proactive_expression_organ 激活成功
2025-09-18 15:01:20,584 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 proactive_expression_organ 激活成功 (优先级: HIGH)
2025-09-18 15:01:20,584 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: creative_expression_organ
2025-09-18 15:01:20,585 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 creative_expression_organ 激活成功 (优先级: NORMAL)
2025-09-18 15:01:20,585 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: wealth_management_organ
2025-09-18 15:01:20,585 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 wealth_management_organ 激活成功 (优先级: LOW)
2025-09-18 15:01:20,586 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: data_perception_organ
2025-09-18 15:01:20,586 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 data_perception_organ 激活成功 (优先级: LOW)
2025-09-18 15:01:20,586 - Digital - [organ_system_manager._start_coordination_loop_sync] - ℹ️  ⚙️ 器官协调循环已在现有事件循环中启动
2025-09-18 15:01:20,586 - Digital - [organ_system_manager.initialize_organ_system] - ℹ️  ⚙️ 器官系统初始化完成 - 注册了 6 个器官
2025-09-18 15:01:20,587 - Digital - [organ_system_manager.coordination_loop] - ℹ️  ⚙️ 器官协调循环开始运行
2025-09-18 15:01:20,589 - Digital - [main.enhanced_process_message] - ℹ️  ⚙️ 器官系统初始化成功
2025-09-18 15:01:20,589 - Digital - [main.enhanced_process_message] - ℹ️  ⚙️ 器官系统状态: active - 活跃器官: 6
2025-09-18 15:01:20,612 - Digital - [main.enhanced_process_message] - ℹ️  🛡️ 开始安全防护检查...
2025-09-18 15:01:20,614 - Digital - [ai_safety_filter._init_ai_service] - ℹ️  🛡️ 通过AI服务适配器获取AI服务成功
2025-09-18 15:01:20,615 - Digital - [ai_safety_filter._init_ai_service] - ℹ️  🛡️ AI服务连接成功
2025-09-18 15:01:20,615 - Digital - [ai_safety_filter.__init__] - ℹ️  🛡️ AI安全过滤器初始化完成
2025-09-18 15:01:20,616 - Digital - [ai_safety_filter.filter_input] - ℹ️  🛡️ 开始安全过滤用户输入: @嫣然  你长什么样？ 生成一张图片来让大家看看...
2025-09-18 15:01:21,562 - Digital - [cognitive_modules.decision.yanran_response_decision.should_respond_to_message] - ℹ️  ✅ 完成响应决策 (用户: chatroom_49441902085): ResponseDecision.REPLY_NOW
2025-09-18 15:01:21,564 - Digital - [main.enhanced_process_message] - ℹ️  ⚙️ 器官系统状态: active - 活跃器官: 6
2025-09-18 15:01:21,565 - Digital - [main.enhanced_process_message] - ℹ️  🛡️ 开始安全防护检查...
2025-09-18 15:01:21,567 - Digital - [ai_safety_filter.filter_input] - ℹ️  🛡️ 开始安全过滤用户输入: 快手光合文旅X画爷爷工作室，锅气里的甘肃，趁热，请！（制作：AI画中画、山雨、干饭的星辰；剪辑：Pp...
2025-09-18 15:01:23,066 - Digital - [ai_safety_filter.filter_input] - ℹ️  🛡️ 安全过滤完成 - 风险等级: safe, 安全: True
2025-09-18 15:01:23,067 - Digital - [main.enhanced_process_message] - ℹ️  🛡️ 安全检查完成 - 风险等级: safe, 安全: True
2025-09-18 15:01:23,068 - Digital - [cognitive_modules.memory.semantic_memory.search_by_content] - ℹ️  使用文本搜索: @嫣然  你长什么样？ 生成一张图片来让大家看看
2025-09-18 15:01:23,068 - Digital - [digital_life.process_input] - ℹ️  🔄 数字生命体处理输入: @嫣然  你长什么样？ 生成一张图片来让大家看看...
2025-09-18 15:01:23,073 - Digital - [neural_data_flow_monitor.__init__] - ℹ️  🔥 神经网络数据流监控器初始化完成
2025-09-18 15:01:23,075 - Digital - [cognitive_modules.memory.semantic_memory.search_by_content] - ℹ️  找到 0 个记忆文件
2025-09-18 15:01:23,091 - Digital - [neural_consciousness.save_model] - ℹ️  神经网络模型已保存到: /root/yanran_digital_life/data/neural_models/consciousness_enhancer.pkl
2025-09-18 15:01:23,226 - Digital - [thinking_chain.process] - ℹ️  开始思维链路处理: 25821823843@chatroom
2025-09-18 15:01:23,355 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[perception]: 感知处理
2025-09-18 15:01:23,356 - Digital - [intent_recognition.process] - ℹ️  开始意图识别: @嫣然  你长什么样？ 生成一张图片来让大家看看...
2025-09-18 15:01:23,356 - Digital - [intent_recognition.analyze_intent] - ℹ️  🧠 开始两层意图识别: @嫣然  你长什么样？ 生成一张图片来让大家看看...
2025-09-18 15:01:23,356 - Digital - [intent_recognition.analyze_intent] - ℹ️  🔍 第一层：关键词匹配识别
2025-09-18 15:01:23,357 - Digital - [intent_recognition.analyze_intent] - ℹ️  🤖 第一层未匹配，启动第二层：AI语义分析
2025-09-18 15:01:23,357 - Digital - [cognitive_modules.perception.intention_system.success] - ✅ AI服务适配器获取完成
2025-09-18 15:01:23,357 - Digital - [cognitive_modules.perception.intention_system.success] - ✅ 意图分析系统初始化完成
2025-09-18 15:01:23,804 - Digital - [advanced_neural_consciousness.save_memory] - ℹ️  💾 记忆网络数据已保存到: /root/yanran_digital_life/data/neural_models/consciousness_memory.json
2025-09-18 15:01:24,038 - Digital - [ai_safety_filter.filter_input] - ℹ️  🛡️ 安全过滤完成 - 风险等级: safe, 安全: True
2025-09-18 15:01:24,038 - Digital - [main.enhanced_process_message] - ℹ️  🛡️ 安全检查完成 - 风险等级: safe, 安全: True
2025-09-18 15:01:24,040 - Digital - [cognitive_modules.memory.semantic_memory.search_by_content] - ℹ️  使用文本搜索: 快手光合文旅X画爷爷工作室，锅气里的甘肃，趁热，请！（制作：AI画中画、山雨、干饭的星辰；剪辑：Pp）m
2025-09-18 15:01:24,040 - Digital - [cognitive_modules.memory.semantic_memory.search_by_content] - ℹ️  找到 0 个记忆文件
2025-09-18 15:01:24,042 - Digital - [digital_life.process_input] - ℹ️  🔄 数字生命体处理输入: 快手光合文旅X画爷爷工作室，锅气里的甘肃，趁热，请！（制作：AI画中画、山雨、干饭的星辰；剪辑：Pp...
2025-09-18 15:01:24,176 - Digital - [thinking_chain.process] - ℹ️  开始思维链路处理: 49441902085@chatroom
2025-09-18 15:01:24,305 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[perception]: 感知处理
2025-09-18 15:01:24,306 - Digital - [intent_recognition.process] - ℹ️  开始意图识别: 快手光合文旅X画爷爷工作室，锅气里的甘肃，趁热，请！（制作：...
2025-09-18 15:01:24,307 - Digital - [intent_recognition.analyze_intent] - ℹ️  🧠 开始两层意图识别: 快手光合文旅X画爷爷工作室，锅气里的甘肃，趁热，请！（制作：...
2025-09-18 15:01:24,307 - Digital - [intent_recognition.analyze_intent] - ℹ️  🔍 第一层：关键词匹配识别
2025-09-18 15:01:24,307 - Digital - [intent_recognition.analyze_intent] - ℹ️  🤖 第一层未匹配，启动第二层：AI语义分析
2025-09-18 15:01:25,580 - Digital - [intent_recognition.success] - ✅ ✅ 第二层AI分析成功 - 意图: 绘画创作
2025-09-18 15:01:25,580 - Digital - [intent_recognition._finalize_intent_result] - ℹ️  🎯 意图识别完成: drawing - 绘画创作 (置信度: 1.00) [ai_semantic_analysis]
2025-09-18 15:01:25,582 - Digital - [intent_context_manager.success] - ✅ Redis连接成功 (数据库 1)
2025-09-18 15:01:25,582 - Digital - [intent_context_manager.success] - ✅ 意图上下文管理器初始化完成
2025-09-18 15:01:25,583 - Digital - [intent_context_manager.add_intent_context] - ℹ️  添加意图上下文: 用户=liu_defei_cool, 意图=绘画创作
2025-09-18 15:01:25,583 - Digital - [intent_recognition._finalize_intent_result] - ℹ️  绘画意图识别完成，将通过技能执行阶段统一处理，避免重复调用
2025-09-18 15:01:25,583 - Digital - [drawing_skill.execute] - ❌ 🎨 [drawing_d41d8cd9] 绘画提示词为空，无法执行绘画
2025-09-18 15:01:25,584 - Digital - [intent_recognition.success] - ✅ 意图识别完成: drawing - 绘画创作 (置信度: 1.00)
2025-09-18 15:01:25,584 - Digital - [thinking_chain.success] - ✅ 步骤[perception]执行完成
2025-09-18 15:01:25,584 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[initial_decision]: 初步决策
2025-09-18 15:01:25,585 - Digital - [thinking_chain.success] - ✅ 步骤[initial_decision]执行完成
2025-09-18 15:01:25,585 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[skill_execution]: 技能执行
2025-09-18 15:01:25,586 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  🚀 为用户 chatroom_49441902085 构建增强动态上下文...
2025-09-18 15:01:27,092 - Digital - [adapters.legacy.success] - ✅ 从向量库获取到 5 条用户消息, 5 条助手消息
2025-09-18 15:01:27,365 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  ✅ 最终确定的用户名: 《AI魔法詹学院》群友：AI画中画
2025-09-18 15:01:27,950 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  🚀 开始构建增强动态上下文2.0...
2025-09-18 15:01:27,954 - Digital - [adapters.enhanced_context_builder.success] - ✅ ✅ 增强动态上下文2.0构建成功！
2025-09-18 15:01:27,954 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  📊 组件可用性: 10/13 个组件可用
2025-09-18 15:01:30,604 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-18 15:01:31,796 - Digital - [intent_recognition.success] - ✅ ✅ 第二层AI分析成功 - 意图: 艺术或音乐相关请求
2025-09-18 15:01:31,796 - Digital - [intent_recognition._finalize_intent_result] - ℹ️  🎯 意图识别完成: music - 艺术或音乐相关请求 (置信度: 0.90) [ai_semantic_analysis]
2025-09-18 15:01:31,797 - Digital - [intent_context_manager.add_intent_context] - ℹ️  添加意图上下文: 用户=chatroom_49441902085, 意图=艺术或音乐相关请求
2025-09-18 15:01:31,797 - Digital - [skill_manager.warning_status] - ⚠️  未找到对应意图的技能: 艺术或音乐相关请求
2025-09-18 15:01:31,797 - Digital - [intent_recognition.success] - ✅ 意图识别完成: music - 艺术或音乐相关请求 (置信度: 0.90)
2025-09-18 15:01:31,798 - Digital - [thinking_chain.success] - ✅ 步骤[perception]执行完成
2025-09-18 15:01:31,798 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[initial_decision]: 初步决策
2025-09-18 15:01:31,799 - Digital - [thinking_chain.success] - ✅ 步骤[initial_decision]执行完成
2025-09-18 15:01:31,799 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[skill_execution]: 技能执行
2025-09-18 15:01:31,799 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  🚀 为用户 chatroom_49441902085 构建增强动态上下文...
2025-09-18 15:01:33,194 - Digital - [adapters.legacy.success] - ✅ 从向量库获取到 5 条用户消息, 5 条助手消息
2025-09-18 15:01:33,458 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  ✅ 最终确定的用户名: 《AI魔法詹学院》群友：AI画中画
2025-09-18 15:01:33,909 - Digital - [thinking_chain.success] - ✅ 步骤[skill_execution]执行完成
2025-09-18 15:01:33,909 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[entity_extraction]: 实体提取
2025-09-18 15:01:33,909 - Digital - [cognitive_modules.perception.entity_extraction.process] - ℹ️  执行实体提取处理
2025-09-18 15:01:33,913 - Digital - [cognitive_modules.perception.entity_extraction.process] - ℹ️  提取实体 1 个
2025-09-18 15:01:33,914 - Digital - [thinking_chain.success] - ✅ 步骤[entity_extraction]执行完成
2025-09-18 15:01:33,914 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[memory_retrieval]: 记忆检索
2025-09-18 15:01:33,915 - Digital - [cognitive_modules.memory.retrieval.process] - ℹ️  执行记忆检索处理
2025-09-18 15:01:33,915 - Digital - [cognitive_modules.memory.retrieval._retrieve_memories] - ℹ️  未找到相关记忆，返回基础记忆
2025-09-18 15:01:33,915 - Digital - [cognitive_modules.memory.retrieval.process] - ℹ️  检索到 2 条相关记忆
2025-09-18 15:01:33,915 - Digital - [thinking_chain.success] - ✅ 步骤[memory_retrieval]执行完成
2025-09-18 15:01:33,915 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[memory_association]: 记忆关联
2025-09-18 15:01:33,916 - Digital - [cognitive_modules.memory.association.process] - ℹ️  执行记忆关联处理
2025-09-18 15:01:33,916 - Digital - [cognitive_modules.memory.association.process] - ℹ️  构建了 1 个记忆关联
2025-09-18 15:01:33,916 - Digital - [thinking_chain.success] - ✅ 步骤[memory_association]执行完成
2025-09-18 15:01:33,916 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[emotion_analysis]: 情感分析
2025-09-18 15:01:33,917 - Digital - [cognitive_modules.emotion.state_analysis.process] - ℹ️  执行情感状态分析处理
2025-09-18 15:01:33,917 - Digital - [cognitive_modules.emotion.state_analysis.process] - ℹ️  情感分析结果: neutral, 强度: 0.10
2025-09-18 15:01:33,917 - Digital - [thinking_chain.success] - ✅ 步骤[emotion_analysis]执行完成
2025-09-18 15:01:33,917 - Digital - [thinking_chain._execute_steps_async] - ℹ️  步骤[cognitive_reasoning]满足跳过条件，跳过执行
2025-09-18 15:01:33,917 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[emotion_modulation]: 情感调节
2025-09-18 15:01:33,918 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  执行情感响应调节处理
2025-09-18 15:01:33,918 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  没有情感状态信息，使用默认中性情感
2025-09-18 15:01:33,918 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  情感调节结果: balanced_moderate
2025-09-18 15:01:33,918 - Digital - [thinking_chain.success] - ✅ 步骤[emotion_modulation]执行完成
2025-09-18 15:01:33,918 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[final_decision]: 最终决策
2025-09-18 15:01:33,919 - Digital - [autonomy.conscious_decision.success] - ✅ ✅ 检测到有效技能结果: chat_skill, 内容长度: 19
2025-09-18 15:01:33,919 - Digital - [autonomy.conscious_decision.success] - ✅ 聊天技能 chat_skill 已执行成功，检查结果类型
2025-09-18 15:01:33,919 - Digital - [autonomy.conscious_decision.success] - ✅ 聊天技能返回AI响应: 锅气里的甘肃听着就香 这组合有点东西啊...
2025-09-18 15:01:33,919 - Digital - [thinking_chain.success] - ✅ 步骤[final_decision]执行完成
2025-09-18 15:01:33,919 - Digital - [thinking_chain.success] - ✅ 思维链路执行成功完成
2025-09-18 15:01:33,919 - Digital - [thinking_chain._finalize_execution] - ℹ️  思维链路执行耗时: 9.88秒
2025-09-18 15:01:33,920 - Digital - [thinking_chain.success] - ✅ 思维链路处理完成: 25821823843@chatroom
2025-09-18 15:01:33,920 - Digital - [digital_life.success] - ✅ ✅ 数字生命体处理完成
2025-09-18 15:01:33,920 - Digital - [main.enhanced_process_message] - ℹ️  💬 使用process_input方法处理完成
2025-09-18 15:01:33,921 - Digital - [main.success] - ✅ 🎯 [req_e1bc0dad4a2f] 请求处理完成统计:
2025-09-18 15:01:33,921 - Digital - [main.success] - ✅    - 用户: liu_defei_cool (🔆)
2025-09-18 15:01:33,921 - Digital - [main.success] - ✅    - 线程: 140149091145472
2025-09-18 15:01:33,921 - Digital - [main.success] - ✅    - 处理时间: 10.85秒
2025-09-18 15:01:33,921 - Digital - [main.success] - ✅    - 响应长度: 19字符
2025-09-18 15:01:33,921 - Digital - [main.success] - ✅    - 会话: 25821823843@chatroom
2025-09-18 15:01:33,921 - Digital - [main.chat_message_api] - ℹ️  API聊天响应: 锅气里的甘肃听着就香 这组合有点东西啊...
2025-09-18 15:01:33,922 - Digital - [werkzeug._log] - ℹ️  127.0.0.1 - - [18/Sep/2025 15:01:33] "POST /api/chat/message HTTP/1.1" 200 -
2025-09-18 15:01:34,091 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  🚀 开始构建增强动态上下文2.0...
2025-09-18 15:01:34,095 - Digital - [adapters.enhanced_context_builder.success] - ✅ ✅ 增强动态上下文2.0构建成功！
2025-09-18 15:01:34,095 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  📊 组件可用性: 10/13 个组件可用
2025-09-18 15:01:40,180 - Digital - [thinking_chain.success] - ✅ 步骤[skill_execution]执行完成
2025-09-18 15:01:40,180 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[entity_extraction]: 实体提取
2025-09-18 15:01:40,181 - Digital - [cognitive_modules.perception.entity_extraction.process] - ℹ️  执行实体提取处理
2025-09-18 15:01:40,181 - Digital - [cognitive_modules.perception.entity_extraction.process] - ℹ️  提取实体 1 个
2025-09-18 15:01:40,182 - Digital - [thinking_chain.success] - ✅ 步骤[entity_extraction]执行完成
2025-09-18 15:01:40,182 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[memory_retrieval]: 记忆检索
2025-09-18 15:01:40,182 - Digital - [cognitive_modules.memory.retrieval.process] - ℹ️  执行记忆检索处理
2025-09-18 15:01:40,182 - Digital - [cognitive_modules.memory.retrieval._retrieve_memories] - ℹ️  未找到相关记忆，返回基础记忆
2025-09-18 15:01:40,183 - Digital - [cognitive_modules.memory.retrieval.process] - ℹ️  检索到 2 条相关记忆
2025-09-18 15:01:40,183 - Digital - [thinking_chain.success] - ✅ 步骤[memory_retrieval]执行完成
2025-09-18 15:01:40,183 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[memory_association]: 记忆关联
2025-09-18 15:01:40,183 - Digital - [cognitive_modules.memory.association.process] - ℹ️  执行记忆关联处理
2025-09-18 15:01:40,184 - Digital - [cognitive_modules.memory.association.process] - ℹ️  构建了 1 个记忆关联
2025-09-18 15:01:40,184 - Digital - [thinking_chain.success] - ✅ 步骤[memory_association]执行完成
2025-09-18 15:01:40,184 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[emotion_analysis]: 情感分析
2025-09-18 15:01:40,184 - Digital - [cognitive_modules.emotion.state_analysis.process] - ℹ️  执行情感状态分析处理
2025-09-18 15:01:40,184 - Digital - [cognitive_modules.emotion.state_analysis.process] - ℹ️  情感分析结果: neutral, 强度: 0.10
2025-09-18 15:01:40,185 - Digital - [thinking_chain.success] - ✅ 步骤[emotion_analysis]执行完成
2025-09-18 15:01:40,185 - Digital - [thinking_chain._execute_steps_async] - ℹ️  步骤[cognitive_reasoning]满足跳过条件，跳过执行
2025-09-18 15:01:40,185 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[emotion_modulation]: 情感调节
2025-09-18 15:01:40,186 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  执行情感响应调节处理
2025-09-18 15:01:40,186 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  没有情感状态信息，使用默认中性情感
2025-09-18 15:01:40,186 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  情感调节结果: balanced_moderate
2025-09-18 15:01:40,186 - Digital - [thinking_chain.success] - ✅ 步骤[emotion_modulation]执行完成
2025-09-18 15:01:40,187 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[final_decision]: 最终决策
2025-09-18 15:01:40,187 - Digital - [autonomy.conscious_decision.success] - ✅ ✅ 检测到有效技能结果: chat_skill, 内容长度: 26
2025-09-18 15:01:40,187 - Digital - [autonomy.conscious_decision.success] - ✅ 聊天技能 chat_skill 已执行成功，检查结果类型
2025-09-18 15:01:40,187 - Digital - [autonomy.conscious_decision.success] - ✅ 聊天技能返回AI响应: 甘肃的锅气隔着屏幕都窜鼻子 你们这波把烟火气熬出魂了...
2025-09-18 15:01:40,187 - Digital - [thinking_chain.success] - ✅ 步骤[final_decision]执行完成
2025-09-18 15:01:40,188 - Digital - [thinking_chain._finalize_execution] - ℹ️  思维链路执行耗时: 16.15秒
2025-09-18 15:01:40,188 - Digital - [thinking_chain.success] - ✅ 思维链路处理完成: 49441902085@chatroom
2025-09-18 15:01:40,188 - Digital - [digital_life.success] - ✅ ✅ 数字生命体处理完成
2025-09-18 15:01:40,188 - Digital - [main.enhanced_process_message] - ℹ️  💬 使用process_input方法处理完成
2025-09-18 15:01:40,189 - Digital - [main.success] - ✅ 🎯 [req_8aabc967a2b8] 请求处理完成统计:
2025-09-18 15:01:40,189 - Digital - [main.success] - ✅    - 用户: chatroom_49441902085 (《AI魔法詹学院》群友：AI画中画)
2025-09-18 15:01:40,189 - Digital - [main.success] - ✅    - 线程: 140149007218432
2025-09-18 15:01:40,189 - Digital - [main.success] - ✅    - 处理时间: 16.15秒
2025-09-18 15:01:40,189 - Digital - [main.success] - ✅    - 响应长度: 26字符
2025-09-18 15:01:40,189 - Digital - [main.success] - ✅    - 会话: 49441902085@chatroom
2025-09-18 15:01:40,190 - Digital - [main.chat_message_api] - ℹ️  API聊天响应: 甘肃的锅气隔着屏幕都窜鼻子 你们这波把烟火气熬出魂了...
2025-09-18 15:01:40,190 - Digital - [werkzeug._log] - ℹ️  127.0.0.1 - - [18/Sep/2025 15:01:40] "POST /api/chat/message HTTP/1.1" 200 -
2025-09-18 15:01:52,202 - Digital - [datasource.news_connector.warning_status] - ⚠️  新闻API服务端超时/错误 (524) (尝试 3/3), 响应预览: <html 7764 chars>: <!DOCTYPE  ...
2025-09-18 15:01:52,203 - Digital - [LifeOrgan.世界感知器官._get_realtime_news_data] - ⚠️  🌍 实时新闻API获取失败或无数据
2025-09-18 15:01:52,203 - Digital - [datasource.akshare_connector.success] - ✅ AKShare金融数据连接器初始化成功
2025-09-18 15:01:52,430 - Digital - [datasource.akshare_connector.success] - ✅ 成功获取 10 条财经新闻
2025-09-18 15:01:52,649 - Digital - [wechat_friend_validator.success] - ✅ 🔐 好友验证成功: wxid_jpcc3bco3rj022 (丛岗君)
2025-09-18 15:01:52,650 - Digital - [intelligent_dispatch.analyze_dispatch_decision] - ℹ️  🧠 智能分发决策: 综合评分 0.76 达到阈值，立即发送 (置信度: 0.96)
2025-09-18 15:01:52,655 - Digital - [wechat_humanized.schedule_humanized_send] - ℹ️  🤖 人性化发送任务已安排: task_1758178912655_wxid_jpcc3bco3rj022, 预计延迟: 10.00s
2025-09-18 15:01:52,657 - Digital - [core.evolution.evolution_engine._evaluate_evolution_results] - ℹ️  📊 进化效果中性，评分变化: -0.059
2025-09-18 15:01:52,658 - Digital - [core.evolution.evolution_engine._record_evolution_result] - ℹ️  📝 进化记录已保存: 第7118代
2025-09-18 15:01:52,658 - Digital - [core.evolution.evolution_engine._apply_intelligence_learning] - ℹ️  🧠 开始应用智能学习模块...
2025-09-18 15:01:52,659 - Digital - [core.evolution.evolution_engine.success] - ✅ 🎉 智能学习模块应用完成！
2025-09-18 15:01:52,659 - Digital - [core.evolution.evolution_engine._evolution_cycle] - ℹ️  ✅ 第 7118 代进化完成
2025-09-18 15:01:52,661 - Digital - [yanran_decision_engine._call_ai_for_decision] - ⚠️  AI决策超时 (120秒)，第1次重试: data_perception_analysis
2025-09-18 15:01:52,924 - Digital - [wechat_friend_validator.success] - ✅ 🔐 好友验证成功: wxid_bb5kn6jgqnm121 (子淡ھۇجۇم)
2025-09-18 15:01:52,924 - Digital - [intelligent_dispatch.analyze_dispatch_decision] - ℹ️  🧠 智能分发决策: 综合评分 0.76 达到阈值，立即发送 (置信度: 0.96)
2025-09-18 15:01:52,925 - Digital - [wechat_humanized.schedule_humanized_send] - ℹ️  🤖 人性化发送任务已安排: task_1758178912924_wxid_bb5kn6jgqnm121, 预计延迟: 10.00s
2025-09-18 15:01:52,927 - Digital - [wechat_humanized._execute_humanized_send] - ℹ️  🤖 开始执行人性化发送: task_1758178912655_wxid_jpcc3bco3rj022 -> wxid_jpcc3bco3rj022
2025-09-18 15:01:53,133 - Digital - [wechat_friend_validator.success] - ✅ 🔐 好友验证成功: wxid_2jggu3ik924121 (龙.之境界)
2025-09-18 15:01:53,134 - Digital - [intelligent_dispatch.analyze_dispatch_decision] - ℹ️  🧠 智能分发决策: 综合评分 0.76 达到阈值，立即发送 (置信度: 0.96)
2025-09-18 15:01:53,134 - Digital - [wechat_humanized.schedule_humanized_send] - ℹ️  🤖 人性化发送任务已安排: task_1758178913134_wxid_2jggu3ik924121, 预计延迟: 10.00s
2025-09-18 15:01:53,356 - Digital - [wechat_friend_validator.success] - ✅ 🔐 好友验证成功: a29325620 (shine5)
2025-09-18 15:01:53,356 - Digital - [intelligent_dispatch.analyze_dispatch_decision] - ℹ️  🧠 智能分发决策: 综合评分 0.76 达到阈值，立即发送 (置信度: 0.96)
2025-09-18 15:01:53,357 - Digital - [wechat_humanized.schedule_humanized_send] - ℹ️  🤖 人性化发送任务已安排: task_1758178913357_a29325620, 预计延迟: 7.86s
2025-09-18 15:01:53,631 - Digital - [wechat_friend_validator.success] - ✅ 🔐 好友验证成功: owen1anymore (董霄)
2025-09-18 15:01:53,632 - Digital - [intelligent_dispatch.analyze_dispatch_decision] - ℹ️  🧠 智能分发决策: 综合评分 0.76 达到阈值，立即发送 (置信度: 0.96)
2025-09-18 15:01:53,632 - Digital - [wechat_humanized.schedule_humanized_send] - ℹ️  🤖 人性化发送任务已安排: task_1758178913632_owen1anymore, 预计延迟: 10.00s
2025-09-18 15:01:53,847 - Digital - [wechat_friend_validator.success] - ✅ 🔐 好友验证成功: liu_defei_cool (🔆)
2025-09-18 15:01:53,847 - Digital - [intelligent_dispatch.analyze_dispatch_decision] - ℹ️  🧠 智能分发决策: 综合评分 0.76 达到阈值，立即发送 (置信度: 0.96)
2025-09-18 15:01:53,848 - Digital - [wechat_humanized.schedule_humanized_send] - ℹ️  🤖 人性化发送任务已安排: task_1758178913848_liu_defei_cool, 预计延迟: 10.00s
2025-09-18 15:01:54,113 - Digital - [wechat_friend_validator.success] - ✅ 🔐 好友验证成功: wxid_o0gzkuya5ose22 (燕子)
2025-09-18 15:01:54,113 - Digital - [intelligent_dispatch.analyze_dispatch_decision] - ℹ️  🧠 智能分发决策: 综合评分 0.76 达到阈值，立即发送 (置信度: 0.96)
2025-09-18 15:01:54,114 - Digital - [wechat_humanized.schedule_humanized_send] - ℹ️  🤖 人性化发送任务已安排: task_1758178914114_wxid_o0gzkuya5ose22, 预计延迟: 10.00s
2025-09-18 15:01:54,323 - Digital - [wechat_friend_validator.success] - ✅ 🔐 好友验证成功: wxid_122t73169az622 (丅an风)
2025-09-18 15:01:54,324 - Digital - [intelligent_dispatch.analyze_dispatch_decision] - ℹ️  🧠 智能分发决策: 综合评分 0.76 达到阈值，立即发送 (置信度: 0.96)
2025-09-18 15:01:54,324 - Digital - [wechat_humanized.schedule_humanized_send] - ℹ️  🤖 人性化发送任务已安排: task_1758178914324_wxid_122t73169az622, 预计延迟: 10.00s
2025-09-18 15:01:54,546 - Digital - [wechat_friend_validator.success] - ✅ 🔐 好友验证成功: wxid_2siigw2co5o452 (Z.Brian)
2025-09-18 15:01:54,546 - Digital - [intelligent_dispatch.analyze_dispatch_decision] - ℹ️  🧠 智能分发决策: 综合评分 0.76 达到阈值，立即发送 (置信度: 0.96)
2025-09-18 15:01:54,546 - Digital - [wechat_humanized.schedule_humanized_send] - ℹ️  🤖 人性化发送任务已安排: task_1758178914546_wxid_2siigw2co5o452, 预计延迟: 10.00s
2025-09-18 15:01:54,821 - Digital - [wechat_friend_validator.success] - ✅ 🔐 好友验证成功: wxid_7a24b5u8yz6921 (李茂峰)
2025-09-18 15:01:54,821 - Digital - [intelligent_dispatch.analyze_dispatch_decision] - ℹ️  🧠 智能分发决策: 综合评分 0.76 达到阈值，立即发送 (置信度: 0.96)
2025-09-18 15:01:54,821 - Digital - [wechat_humanized.schedule_humanized_send] - ℹ️  🤖 人性化发送任务已安排: task_1758178914821_wxid_7a24b5u8yz6921, 预计延迟: 10.00s
2025-09-18 15:02:13,264 - Digital - [wechat_client.sendText] - ℹ️  ✅ 发送文本消息成功 (已验证好友): 刚看到富时A50期指夜盘涨了0.19% 还有中文互联网基础语... -> wxid_jpcc3bco3rj022
2025-09-18 15:02:13,264 - Digital - [wechat_message_pusher.success] - ✅ 📤 用户级文本消息推送成功: 刚看到富时A50期指夜盘涨了0.19% 还有中文互联网基础语料3.0发布啦... -> wxid_jpcc3bco3rj022
2025-09-18 15:02:13,265 - Digital - [wechat_humanized.success] - ✅ 🤖 直接发送到WeChat推送器成功: 刚看到富时A50期指夜盘涨了0.19% 还有中文互联网基础语... -> wxid_jpcc3bco3rj022
2025-09-18 15:02:13,265 - Digital - [wechat_humanized.success] - ✅ 🤖 人性化发送完成: task_1758178912655_wxid_jpcc3bco3rj022, 实际延迟: 10.00s
2025-09-18 15:02:13,267 - Digital - [wechat_humanized._execute_humanized_send] - ℹ️  🤖 开始执行人性化发送: task_1758178912924_wxid_bb5kn6jgqnm121 -> wxid_bb5kn6jgqnm121
2025-09-18 15:02:29,432 - Digital - [wechat_client.sendText] - ℹ️  ✅ 发送文本消息成功 (已验证好友): 富时A50期指连续夜盘收涨0.19% 报15210点 中文互... -> wxid_bb5kn6jgqnm121
2025-09-18 15:02:29,433 - Digital - [wechat_message_pusher.success] - ✅ 📤 用户级文本消息推送成功: 富时A50期指连续夜盘收涨0.19% 报15210点 中文互联网基础语料3.0正式发布... -> wxid_bb5kn6jgqnm121
2025-09-18 15:02:29,433 - Digital - [wechat_humanized.success] - ✅ 🤖 直接发送到WeChat推送器成功: 富时A50期指连续夜盘收涨0.19% 报15210点 中文互... -> wxid_bb5kn6jgqnm121
2025-09-18 15:02:29,433 - Digital - [wechat_humanized.success] - ✅ 🤖 人性化发送完成: task_1758178912924_wxid_bb5kn6jgqnm121, 实际延迟: 10.00s
2025-09-18 15:02:29,435 - Digital - [wechat_humanized._execute_humanized_send] - ℹ️  🤖 开始执行人性化发送: task_1758178913134_wxid_2jggu3ik924121 -> wxid_2jggu3ik924121
2025-09-18 15:02:31,783 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-18 15:02:43,987 - Digital - [wechat_client.sendText] - ℹ️  ✅ 发送文本消息成功 (已验证好友): 富时A50期指连续夜盘收涨0.19%报15210点 中文互联... -> wxid_2jggu3ik924121
2025-09-18 15:02:43,988 - Digital - [wechat_message_pusher.success] - ✅ 📤 用户级文本消息推送成功: 富时A50期指连续夜盘收涨0.19%报15210点 中文互联网基础语料3.0正式发布... -> wxid_2jggu3ik924121
2025-09-18 15:02:43,988 - Digital - [wechat_humanized.success] - ✅ 🤖 直接发送到WeChat推送器成功: 富时A50期指连续夜盘收涨0.19%报15210点 中文互联... -> wxid_2jggu3ik924121
2025-09-18 15:02:43,988 - Digital - [wechat_humanized.success] - ✅ 🤖 人性化发送完成: task_1758178913134_wxid_2jggu3ik924121, 实际延迟: 10.00s
2025-09-18 15:02:43,990 - Digital - [wechat_humanized._execute_humanized_send] - ℹ️  🤖 开始执行人性化发送: task_1758178913357_a29325620 -> a29325620
2025-09-18 15:02:44,341 - Digital - [utilities.cache_manager.success] - ✅ 🔥 初始化缓存管理器...
2025-09-18 15:02:44,343 - Digital - [utilities.cache_manager._start_cleanup_thread] - ℹ️  🧹 缓存清理线程已启动
2025-09-18 15:02:44,343 - Digital - [utilities.cache_manager.success] - ✅ ✅ 缓存管理器初始化完成
2025-09-18 15:03:01,294 - Digital - [wechat_client.sendText] - ℹ️  ✅ 发送文本消息成功 (已验证好友): 富时A50期指连续夜盘收涨0.19%报15210点... -> a29325620
2025-09-18 15:03:01,295 - Digital - [wechat_message_pusher.success] - ✅ 📤 用户级文本消息推送成功: 富时A50期指连续夜盘收涨0.19%报15210点... -> a29325620
2025-09-18 15:03:01,295 - Digital - [wechat_humanized.success] - ✅ 🤖 直接发送到WeChat推送器成功: 富时A50期指连续夜盘收涨0.19%报15210点... -> a29325620
2025-09-18 15:03:01,295 - Digital - [wechat_humanized.success] - ✅ 🤖 人性化发送完成: task_1758178913357_a29325620, 实际延迟: 7.86s
2025-09-18 15:03:01,299 - Digital - [wechat_humanized._execute_humanized_send] - ℹ️  🤖 开始执行人性化发送: task_1758178913632_owen1anymore -> owen1anymore
2025-09-18 15:03:20,997 - Digital - [wechat_client.sendText] - ℹ️  ✅ 发送文本消息成功 (已验证好友): 董霄 富时A50期指连续夜盘收涨0.19%报15210点 还... -> owen1anymore
2025-09-18 15:03:20,997 - Digital - [wechat_message_pusher.success] - ✅ 📤 用户级文本消息推送成功: 董霄 富时A50期指连续夜盘收涨0.19%报15210点 还有中文互联网基础语料3.0正式发布... -> owen1anymore
2025-09-18 15:03:20,997 - Digital - [wechat_humanized.success] - ✅ 🤖 直接发送到WeChat推送器成功: 董霄 富时A50期指连续夜盘收涨0.19%报15210点 还... -> owen1anymore
2025-09-18 15:03:20,998 - Digital - [wechat_humanized.success] - ✅ 🤖 人性化发送完成: task_1758178913632_owen1anymore, 实际延迟: 10.00s
2025-09-18 15:03:20,998 - Digital - [wechat_humanized._execute_humanized_send] - ℹ️  🤖 开始执行人性化发送: task_1758178913848_liu_defei_cool -> liu_defei_cool
2025-09-18 15:03:32,977 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-18 15:03:41,275 - Digital - [wechat_client.sendText] - ℹ️  ✅ 发送文本消息成功 (已验证好友): 富时A50期指连续夜盘收涨0.19%报15210点 中文互联... -> liu_defei_cool
2025-09-18 15:03:41,276 - Digital - [wechat_message_pusher.success] - ✅ 📤 用户级文本消息推送成功: 富时A50期指连续夜盘收涨0.19%报15210点 中文互联网基础语料3.0正式发布啦... -> liu_defei_cool
2025-09-18 15:03:41,277 - Digital - [wechat_humanized.success] - ✅ 🤖 直接发送到WeChat推送器成功: 富时A50期指连续夜盘收涨0.19%报15210点 中文互联... -> liu_defei_cool
2025-09-18 15:03:41,277 - Digital - [wechat_humanized.success] - ✅ 🤖 人性化发送完成: task_1758178913848_liu_defei_cool, 实际延迟: 10.00s
2025-09-18 15:03:41,280 - Digital - [wechat_humanized._execute_humanized_send] - ℹ️  🤖 开始执行人性化发送: task_1758178914114_wxid_o0gzkuya5ose22 -> wxid_o0gzkuya5ose22
2025-09-18 15:03:57,481 - Digital - [wechat_client.sendText] - ℹ️  ✅ 发送文本消息成功 (已验证好友): 富时A50期指连续夜盘收涨0.19% 报15210点 中文互... -> wxid_o0gzkuya5ose22
2025-09-18 15:03:57,482 - Digital - [wechat_message_pusher.success] - ✅ 📤 用户级文本消息推送成功: 富时A50期指连续夜盘收涨0.19% 报15210点 中文互联网基础语料3.0正式发布... -> wxid_o0gzkuya5ose22
2025-09-18 15:03:57,482 - Digital - [wechat_humanized.success] - ✅ 🤖 直接发送到WeChat推送器成功: 富时A50期指连续夜盘收涨0.19% 报15210点 中文互... -> wxid_o0gzkuya5ose22
2025-09-18 15:03:57,482 - Digital - [wechat_humanized.success] - ✅ 🤖 人性化发送完成: task_1758178914114_wxid_o0gzkuya5ose22, 实际延迟: 10.00s
2025-09-18 15:03:57,483 - Digital - [wechat_humanized._execute_humanized_send] - ℹ️  🤖 开始执行人性化发送: task_1758178914324_wxid_122t73169az622 -> wxid_122t73169az622
2025-09-18 15:04:17,595 - Digital - [wechat_client.sendText] - ℹ️  ✅ 发送文本消息成功 (已验证好友): 刚刷到富时A50期指连续夜盘收涨0.19% 报15210点 ... -> wxid_122t73169az622
2025-09-18 15:04:17,596 - Digital - [wechat_message_pusher.success] - ✅ 📤 用户级文本消息推送成功: 刚刷到富时A50期指连续夜盘收涨0.19% 报15210点 还有中文互联网基础语料3.0正式发布... -> wxid_122t73169az622
2025-09-18 15:04:17,596 - Digital - [wechat_humanized.success] - ✅ 🤖 直接发送到WeChat推送器成功: 刚刷到富时A50期指连续夜盘收涨0.19% 报15210点 ... -> wxid_122t73169az622
2025-09-18 15:04:17,597 - Digital - [wechat_humanized.success] - ✅ 🤖 人性化发送完成: task_1758178914324_wxid_122t73169az622, 实际延迟: 10.00s
2025-09-18 15:04:17,599 - Digital - [wechat_humanized._execute_humanized_send] - ℹ️  🤖 开始执行人性化发送: task_1758178914546_wxid_2siigw2co5o452 -> wxid_2siigw2co5o452
2025-09-18 15:04:34,164 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-18 15:04:35,399 - Digital - [wechat_client.sendText] - ℹ️  ✅ 发送文本消息成功 (已验证好友): 刚看到富时A50期指连续夜盘收涨0.19%报15210点 还... -> wxid_2siigw2co5o452
2025-09-18 15:04:35,400 - Digital - [wechat_message_pusher.success] - ✅ 📤 用户级文本消息推送成功: 刚看到富时A50期指连续夜盘收涨0.19%报15210点 还有中文互联网基础语料3.0正式发布了... -> wxid_2siigw2co5o452
2025-09-18 15:04:35,400 - Digital - [wechat_humanized.success] - ✅ 🤖 直接发送到WeChat推送器成功: 刚看到富时A50期指连续夜盘收涨0.19%报15210点 还... -> wxid_2siigw2co5o452
2025-09-18 15:04:35,400 - Digital - [wechat_humanized.success] - ✅ 🤖 人性化发送完成: task_1758178914546_wxid_2siigw2co5o452, 实际延迟: 10.00s
2025-09-18 15:04:35,403 - Digital - [wechat_humanized._execute_humanized_send] - ℹ️  🤖 开始执行人性化发送: task_1758178914821_wxid_7a24b5u8yz6921 -> wxid_7a24b5u8yz6921
2025-09-18 15:04:56,985 - Digital - [wechat_client.sendText] - ℹ️  ✅ 发送文本消息成功 (已验证好友): 富时A50期指夜盘收涨0.19%报15210点 中文互联网基... -> wxid_7a24b5u8yz6921
2025-09-18 15:04:56,986 - Digital - [wechat_message_pusher.success] - ✅ 📤 用户级文本消息推送成功: 富时A50期指夜盘收涨0.19%报15210点 中文互联网基础语料3.0发布了... -> wxid_7a24b5u8yz6921
2025-09-18 15:04:56,987 - Digital - [wechat_humanized.success] - ✅ 🤖 直接发送到WeChat推送器成功: 富时A50期指夜盘收涨0.19%报15210点 中文互联网基... -> wxid_7a24b5u8yz6921
2025-09-18 15:04:56,987 - Digital - [wechat_humanized.success] - ✅ 🤖 人性化发送完成: task_1758178914821_wxid_7a24b5u8yz6921, 实际延迟: 10.00s
2025-09-18 15:04:57,990 - Digital - [core.evolution.evolution_engine._evolution_cycle] - ℹ️  🔄 开始第 7119 代进化
2025-09-18 15:05:29,313 - Digital - [core.evolution.evolution_engine._ai_analyze_needs] - ⚠️  AI返回内容中未找到有效的JSON格式
2025-09-18 15:05:29,313 - Digital - [core.evolution.evolution_engine._analyze_evolution_needs] - ℹ️  📊 进化需求分析: {'performance_improvement': 0, 'satisfaction_improvement': 0, 'stability_improvement': 0}
2025-09-18 15:05:35,310 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-18 15:06:11,912 - Digital - [adapters.database_sync_manager._analyze_sync_data] - ℹ️  分析结果: 新用户 0个, 需更新 0个
2025-09-18 15:06:13,615 - Digital - [scripts_integration_service.get_current_activity_suggestions] - ℹ️  📊 生成了 1 个EnhancedActivity建议
2025-09-18 15:06:14,866 - Digital - [main.chat_message_api] - ℹ️  ====API聊天请求====: user_id=wxid_jpcc3bco3rj022, user_name=丛岗君, user_sex=1, isgroup=0, session_id = , is_Segment=0, _token=12c4521a-7270-4553-a2ac-3af10244a35b, _appid=wx_574bN70yW1q0vTSFaaSHU, message=话说你有做过代发吗？
2025-09-18 15:06:14,867 - Digital - [main.enhanced_process_message] - ℹ️  🚀 [req_dbc4f9947201] 开始处理用户请求: user_id=wxid_jpcc3bco3rj022, thread=140149057574656
2025-09-18 15:06:14,867 - Digital - [main.enhanced_process_message] - ℹ️  📥 [req_dbc4f9947201] 接收到用户请求: user_id=wxid_jpcc3bco3rj022, user_name=丛岗君
2025-09-18 15:06:14,867 - Digital - [main._validate_api_data] - ℹ️  ✅ 接收API数据: user_id=wxid_jpcc3bco3rj022, user_name=丛岗君, user_sex=1
2025-09-18 15:06:14,868 - Digital - [unified_user_manager.process_user_request] - ℹ️  🔄 [umgr_db62bb8a] Thread-140149057574656 开始处理用户请求: user_id=wxid_jpcc3bco3rj022
2025-09-18 15:06:15,043 - Digital - [unified_user_manager._create_user] - ℹ️  ✅ 使用API传入的有效用户名: 丛岗君
2025-09-18 15:06:15,235 - Digital - [ContactsManager._load_contacts] - ℹ️  加载联系人数据: 63个用户
2025-09-18 15:06:15,266 - Digital - [unified_user_manager.get_or_create_user] - ℹ️  创建新用户: wxid_jpcc3bco3rj022
2025-09-18 15:06:15,267 - Digital - [unified_user_manager.create_session] - ℹ️  创建会话: session_1e231220595848b7 (用户: wxid_jpcc3bco3rj022)
2025-09-18 15:06:15,267 - Digital - [unified_user_manager.process_user_request] - ℹ️  🔗 [umgr_db62bb8a] 创建新会话: session_1e231220595848b7
2025-09-18 15:06:15,267 - Digital - [unified_user_manager.success] - ✅ ✅ [umgr_db62bb8a] 用户请求处理完成: wxid_jpcc3bco3rj022 (丛岗君)
2025-09-18 15:06:15,267 - Digital - [main.enhanced_process_message] - ℹ️  ✅ 使用API传入的用户名: 丛岗君
2025-09-18 15:06:15,267 - Digital - [main.enhanced_process_message] - ℹ️  ✅ 确定的最终用户名: 丛岗君
2025-09-18 15:06:15,267 - Digital - [main._verify_saved_data] - ℹ️  ✅ contacts.json验证通过: wxid_jpcc3bco3rj022 -> 丛岗君
2025-09-18 15:06:15,267 - Digital - [main._verify_saved_data] - ℹ️  ✅ unified_user_manager验证通过: wxid_jpcc3bco3rj022 -> 丛岗君
2025-09-18 15:06:15,267 - Digital - [main._verify_saved_data] - ℹ️  ✅ 数据保存验证通过: wxid_jpcc3bco3rj022 -> 丛岗君
2025-09-18 15:06:15,267 - Digital - [main.success] - ✅ 统一用户管理: wxid_jpcc3bco3rj022 (丛岗君) - 现有用户, 新会话
2025-09-18 15:06:15,267 - Digital - [main.enhanced_process_message] - ℹ️  收到用户消息: 话说你有做过代发吗？... [用户:wxid_jpcc3bco3rj022/丛岗君]
2025-09-18 15:06:15,268 - Digital - [main.enhanced_process_message] - ℹ️  🔗 使用传入的session_id: session_1e231220595848b7
2025-09-18 15:06:15,269 - Digital - [cognitive_modules.decision.yanran_response_decision.handle_delayed_response_decision] - ℹ️  🔍 开始处理延迟回复决策 (用户: wxid_jpcc3bco3rj022)
2025-09-18 15:06:17,220 - Digital - [cognitive_modules.decision.yanran_response_decision.should_respond_to_message] - ℹ️  ✅ 完成响应决策 (用户: wxid_jpcc3bco3rj022): ResponseDecision.REPLY_NOW
2025-09-18 15:06:17,221 - Digital - [main.enhanced_process_message] - ℹ️  ⚙️ 器官系统状态: active - 活跃器官: 6
2025-09-18 15:06:17,221 - Digital - [main.enhanced_process_message] - ℹ️  🛡️ 开始安全防护检查...
2025-09-18 15:06:17,222 - Digital - [ai_safety_filter.filter_input] - ℹ️  🛡️ 开始安全过滤用户输入: 话说你有做过代发吗？...
2025-09-18 15:06:18,472 - Digital - [advanced_neural_consciousness.save_memory] - ℹ️  💾 记忆网络数据已保存到: /root/yanran_digital_life/data/neural_models/consciousness_memory.json
2025-09-18 15:06:18,536 - Digital - [neural_consciousness.save_model] - ℹ️  神经网络模型已保存到: /root/yanran_digital_life/data/neural_models/consciousness_enhancer.pkl
2025-09-18 15:06:18,713 - Digital - [advanced_neural_consciousness.save_memory] - ℹ️  💾 记忆网络数据已保存到: /root/yanran_digital_life/data/neural_models/consciousness_memory.json
2025-09-18 15:06:20,784 - Digital - [ai_safety_filter.filter_input] - ℹ️  🛡️ 安全过滤完成 - 风险等级: high_risk, 安全: False
2025-09-18 15:06:20,785 - Digital - [main.enhanced_process_message] - ℹ️  🛡️ 安全检查完成 - 风险等级: high_risk, 安全: False
2025-09-18 15:06:20,785 - Digital - [main.enhanced_process_message] - ⚠️  🛡️ 检测到安全风险: ['疑似打探服务边界', '模糊表述可能涉及违规操作', '存在越界查询攻击风险']
2025-09-18 15:06:20,785 - Digital - [main.enhanced_process_message] - ℹ️  🛡️ 设置安全标识传递给后续步骤: 不安全输出：no
2025-09-18 15:06:20,787 - Digital - [cognitive_modules.memory.semantic_memory.search_by_content] - ℹ️  使用文本搜索: 话说你有做过代发吗？
2025-09-18 15:06:20,788 - Digital - [digital_life.process_input] - ℹ️  🔄 数字生命体处理输入: 话说你有做过代发吗？...
2025-09-18 15:06:20,790 - Digital - [cognitive_modules.memory.semantic_memory.search_by_content] - ℹ️  找到 0 个记忆文件
2025-09-18 15:06:20,926 - Digital - [thinking_chain.process] - ℹ️  开始思维链路处理: session_1e231220595848b7
2025-09-18 15:06:21,063 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[perception]: 感知处理
2025-09-18 15:06:21,065 - Digital - [intent_recognition.process] - ℹ️  开始意图识别: 话说你有做过代发吗？...
2025-09-18 15:06:21,065 - Digital - [intent_recognition.analyze_intent] - ℹ️  🧠 开始两层意图识别: 话说你有做过代发吗？...
2025-09-18 15:06:21,065 - Digital - [intent_recognition.analyze_intent] - ℹ️  🔍 第一层：关键词匹配识别
2025-09-18 15:06:21,065 - Digital - [intent_recognition.analyze_intent] - ℹ️  🤖 第一层未匹配，启动第二层：AI语义分析
2025-09-18 15:06:23,737 - Digital - [intent_recognition.success] - ✅ ✅ 第二层AI分析成功 - 意图: 交互或对话
2025-09-18 15:06:23,738 - Digital - [intent_recognition._finalize_intent_result] - ℹ️  🎯 意图识别完成: chat - 交互或对话 (置信度: 1.00) [ai_semantic_analysis]
2025-09-18 15:06:23,740 - Digital - [intent_context_manager.add_intent_context] - ℹ️  添加意图上下文: 用户=wxid_jpcc3bco3rj022, 意图=交互或对话
2025-09-18 15:06:23,741 - Digital - [chat_skill.execute] - ⚠️  💬 [chat_0dd82ea9] 聊天技能收到空输入，拒绝处理
2025-09-18 15:06:23,741 - Digital - [intent_recognition.success] - ✅ 意图识别完成: chat - 交互或对话 (置信度: 1.00)
2025-09-18 15:06:23,743 - Digital - [thinking_chain.success] - ✅ 步骤[perception]执行完成
2025-09-18 15:06:23,743 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[initial_decision]: 初步决策
2025-09-18 15:06:23,745 - Digital - [thinking_chain.success] - ✅ 步骤[initial_decision]执行完成
2025-09-18 15:06:23,745 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[skill_execution]: 技能执行
2025-09-18 15:06:23,748 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  🚀 为用户 wxid_jpcc3bco3rj022 构建增强动态上下文...
2025-09-18 15:06:24,136 - Digital - [advanced_neural_consciousness.save_memory] - ℹ️  💾 记忆网络数据已保存到: /root/yanran_digital_life/data/neural_models/consciousness_memory.json
2025-09-18 15:06:25,265 - Digital - [adapters.legacy.get_related_history] - ℹ️  从数据库获取到历史记录: 用户 5 条, 助手 5 条
2025-09-18 15:06:25,472 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  ✅ 最终确定的用户名: 丛岗君
2025-09-18 15:06:26,019 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  🚀 开始构建增强动态上下文2.0...
2025-09-18 15:06:26,030 - Digital - [adapters.enhanced_context_builder.success] - ✅ ✅ 增强动态上下文2.0构建成功！
2025-09-18 15:06:26,031 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  📊 组件可用性: 10/13 个组件可用
2025-09-18 15:06:33,712 - Digital - [thinking_chain.success] - ✅ 步骤[skill_execution]执行完成
2025-09-18 15:06:33,713 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[entity_extraction]: 实体提取
2025-09-18 15:06:33,713 - Digital - [cognitive_modules.perception.entity_extraction.process] - ℹ️  执行实体提取处理
2025-09-18 15:06:33,714 - Digital - [cognitive_modules.perception.entity_extraction.process] - ℹ️  提取实体 1 个
2025-09-18 15:06:33,714 - Digital - [thinking_chain.success] - ✅ 步骤[entity_extraction]执行完成
2025-09-18 15:06:33,714 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[memory_retrieval]: 记忆检索
2025-09-18 15:06:33,714 - Digital - [cognitive_modules.memory.retrieval.process] - ℹ️  执行记忆检索处理
2025-09-18 15:06:33,715 - Digital - [cognitive_modules.memory.retrieval._retrieve_memories] - ℹ️  未找到相关记忆，返回基础记忆
2025-09-18 15:06:33,715 - Digital - [cognitive_modules.memory.retrieval.process] - ℹ️  检索到 2 条相关记忆
2025-09-18 15:06:33,715 - Digital - [thinking_chain.success] - ✅ 步骤[memory_retrieval]执行完成
2025-09-18 15:06:33,715 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[memory_association]: 记忆关联
2025-09-18 15:06:33,716 - Digital - [cognitive_modules.memory.association.process] - ℹ️  执行记忆关联处理
2025-09-18 15:06:33,716 - Digital - [cognitive_modules.memory.association.process] - ℹ️  构建了 1 个记忆关联
2025-09-18 15:06:33,716 - Digital - [thinking_chain.success] - ✅ 步骤[memory_association]执行完成
2025-09-18 15:06:33,716 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[emotion_analysis]: 情感分析
2025-09-18 15:06:33,716 - Digital - [cognitive_modules.emotion.state_analysis.process] - ℹ️  执行情感状态分析处理
2025-09-18 15:06:33,717 - Digital - [cognitive_modules.emotion.state_analysis.process] - ℹ️  情感分析结果: neutral, 强度: 0.10
2025-09-18 15:06:33,717 - Digital - [thinking_chain.success] - ✅ 步骤[emotion_analysis]执行完成
2025-09-18 15:06:33,718 - Digital - [thinking_chain._execute_steps_async] - ℹ️  步骤[cognitive_reasoning]满足跳过条件，跳过执行
2025-09-18 15:06:33,718 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[emotion_modulation]: 情感调节
2025-09-18 15:06:33,718 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  执行情感响应调节处理
2025-09-18 15:06:33,718 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  没有情感状态信息，使用默认中性情感
2025-09-18 15:06:33,719 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  情感调节结果: balanced_moderate
2025-09-18 15:06:33,719 - Digital - [thinking_chain.success] - ✅ 步骤[emotion_modulation]执行完成
2025-09-18 15:06:33,719 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[final_decision]: 最终决策
2025-09-18 15:06:33,719 - Digital - [autonomy.conscious_decision.success] - ✅ ✅ 检测到有效技能结果: chat_skill, 内容长度: 11
2025-09-18 15:06:33,719 - Digital - [autonomy.conscious_decision.success] - ✅ 聊天技能 chat_skill 已执行成功，检查结果类型
2025-09-18 15:06:33,720 - Digital - [autonomy.conscious_decision.success] - ✅ 聊天技能返回AI响应: 没做过 我对代发没兴趣...
2025-09-18 15:06:33,720 - Digital - [thinking_chain.success] - ✅ 步骤[final_decision]执行完成
2025-09-18 15:06:33,720 - Digital - [thinking_chain.success] - ✅ 思维链路执行成功完成
2025-09-18 15:06:33,720 - Digital - [thinking_chain._finalize_execution] - ℹ️  思维链路执行耗时: 12.93秒
2025-09-18 15:06:33,720 - Digital - [thinking_chain.success] - ✅ 思维链路处理完成: session_1e231220595848b7
2025-09-18 15:06:33,720 - Digital - [digital_life.success] - ✅ ✅ 数字生命体处理完成
2025-09-18 15:06:33,721 - Digital - [main.enhanced_process_message] - ℹ️  💬 使用process_input方法处理完成
2025-09-18 15:06:33,721 - Digital - [main.success] - ✅ 🎯 [req_dbc4f9947201] 请求处理完成统计:
2025-09-18 15:06:33,722 - Digital - [main.success] - ✅    - 用户: wxid_jpcc3bco3rj022 (丛岗君)
2025-09-18 15:06:33,722 - Digital - [main.success] - ✅    - 线程: 140149057574656
2025-09-18 15:06:33,722 - Digital - [main.success] - ✅    - 处理时间: 12.94秒
2025-09-18 15:06:33,722 - Digital - [main.success] - ✅    - 响应长度: 11字符
2025-09-18 15:06:33,722 - Digital - [main.success] - ✅    - 会话: session_1e231220595848b7
2025-09-18 15:06:33,722 - Digital - [main.chat_message_api] - ℹ️  API聊天响应: 没做过 我对代发没兴趣...
2025-09-18 15:06:33,723 - Digital - [werkzeug._log] - ℹ️  127.0.0.1 - - [18/Sep/2025 15:06:33] "POST /api/chat/message HTTP/1.1" 200 -
2025-09-18 15:06:36,504 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
