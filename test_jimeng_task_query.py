#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 老王测试jimeng API的正确任务查询方式
找出如何查询已提交任务的状态，而不是重复提交新任务
"""

import requests
import json
import time
from datetime import datetime

def test_jimeng_task_query():
    """测试jimeng API的任务查询机制"""
    
    print("🔥 老王测试jimeng API任务查询机制")
    print("=" * 70)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # API配置
    url = "http://124.221.30.195:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    # 测试数据
    data = {
        "model": "jimeng-4.0",
        "prompt": "测试任务查询机制",
        "negative_prompt": "低质量",
        "width": 1024,
        "height": 1024,
        "sample_strength": 0.8,
        "response_format": "url"
    }
    
    print(f"📤 步骤1: 提交初始任务...")
    print(f"🔗 URL: {url}")
    print(f"📋 请求体: {json.dumps(data, indent=2, ensure_ascii=False)}")
    print("-" * 50)
    
    try:
        # 步骤1: 提交任务
        response = requests.post(url, headers=headers, json=data, timeout=60)
        
        print(f"📥 初始响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📋 初始响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            created_time = result.get("created")
            data_list = result.get("data", [])
            
            print(f"🔍 任务标识: created={created_time}")
            print(f"🔍 初始data长度: {len(data_list)}")
            
            # 如果立即有结果，测试结束
            if data_list:
                print("✅ 任务立即完成，无需查询")
                return True
            
            # 如果没有结果，测试不同的查询方式
            if created_time:
                print(f"\n🔄 步骤2: 测试不同的任务查询方式...")
                
                # 方式1: 尝试GET请求查询
                print(f"\n🧪 方式1: 尝试GET请求查询任务状态...")
                try:
                    get_url = f"{url}?created={created_time}"
                    get_response = requests.get(get_url, headers=headers, timeout=30)
                    print(f"   GET响应状态码: {get_response.status_code}")
                    if get_response.status_code == 200:
                        get_result = get_response.json()
                        print(f"   GET响应: {json.dumps(get_result, indent=2, ensure_ascii=False)}")
                    else:
                        print(f"   GET请求失败: {get_response.text[:200]}")
                except Exception as e:
                    print(f"   GET请求异常: {e}")
                
                # 方式2: 尝试带created参数的POST请求
                print(f"\n🧪 方式2: 尝试带created参数的POST请求...")
                try:
                    query_data = data.copy()
                    query_data["created"] = created_time
                    query_response = requests.post(url, headers=headers, json=query_data, timeout=30)
                    print(f"   带created的POST响应状态码: {query_response.status_code}")
                    if query_response.status_code == 200:
                        query_result = query_response.json()
                        print(f"   带created的POST响应: {json.dumps(query_result, indent=2, ensure_ascii=False)}")
                        
                        # 检查是否返回了结果
                        query_data_list = query_result.get("data", [])
                        if query_data_list:
                            print(f"   ✅ 方式2成功！获得 {len(query_data_list)} 张图片")
                            return True
                    else:
                        print(f"   带created的POST请求失败: {query_response.text[:200]}")
                except Exception as e:
                    print(f"   带created的POST请求异常: {e}")
                
                # 方式3: 尝试查询接口
                print(f"\n🧪 方式3: 尝试专门的查询接口...")
                try:
                    query_url = f"http://124.221.30.195:47653/v1/images/status/{created_time}"
                    status_response = requests.get(query_url, headers=headers, timeout=30)
                    print(f"   状态查询响应码: {status_response.status_code}")
                    if status_response.status_code == 200:
                        status_result = status_response.json()
                        print(f"   状态查询响应: {json.dumps(status_result, indent=2, ensure_ascii=False)}")
                    else:
                        print(f"   状态查询失败: {status_response.text[:200]}")
                except Exception as e:
                    print(f"   状态查询异常: {e}")
                
                # 方式4: 重复相同请求（但等待更长时间）
                print(f"\n🧪 方式4: 重复相同请求（等待更长时间）...")
                try:
                    time.sleep(5)  # 等待5秒
                    repeat_response = requests.post(url, headers=headers, json=data, timeout=30)
                    print(f"   重复请求响应码: {repeat_response.status_code}")
                    if repeat_response.status_code == 200:
                        repeat_result = repeat_response.json()
                        repeat_created = repeat_result.get("created")
                        repeat_data = repeat_result.get("data", [])
                        
                        print(f"   重复请求created: {repeat_created}")
                        print(f"   重复请求data长度: {len(repeat_data)}")
                        
                        if repeat_created == created_time and repeat_data:
                            print(f"   ✅ 方式4成功！相同任务返回了结果")
                            return True
                        elif repeat_data:
                            print(f"   ⚠️ 方式4返回了新任务的结果")
                        else:
                            print(f"   ⚠️ 方式4仍然返回空结果")
                    else:
                        print(f"   重复请求失败: {repeat_response.text[:200]}")
                except Exception as e:
                    print(f"   重复请求异常: {e}")
                
                print(f"\n❌ 所有查询方式都失败了")
                return False
            else:
                print("❌ 没有任务标识，无法查询")
                return False
        else:
            print(f"❌ 初始请求失败: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"💥 异常: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始jimeng API任务查询测试...")
    
    success = test_jimeng_task_query()
    
    print("\n" + "=" * 70)
    if success:
        print("✅ 找到了正确的任务查询方式！")
    else:
        print("❌ 未找到有效的任务查询方式")
    
    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔥 任务查询测试完成！")
