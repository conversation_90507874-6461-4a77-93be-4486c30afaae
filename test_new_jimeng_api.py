#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 老王验证新版jimeng-free-api-all的脚本
测试新部署的API服务是否正常工作
"""

import requests
import json
import time
from datetime import datetime

def test_new_jimeng_api():
    """测试新版jimeng API"""
    
    print("🔥 老王验证新版jimeng-free-api-all")
    print("=" * 70)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # API配置 - 保持IP和端口不变
    url = "http://**************:47653/v1/images/generations"
    
    # 测试用的session_id（请替换为实际的session_id）
    test_session_ids = [
        "1e6aa5b4800b56a3e345bacacfaa7c09"
    ]
    
    # 测试用例
    test_cases = [
        {
            "name": "21:9尺寸测试",
            "data": {
                "model": "jimeng-4.0", 
                "prompt": "前置镜头俯拍15°（镜头脏渍形成左上角光斑），画面仅截取下巴到胸口：奶杏色针织开衫垮至右肩（肩头三道0.5cm勾丝），汗湿项链吊坠因手抖糊成银色光团。背景虚焦厨房：左侧妈妈碎花围裙身影（颠锅动作拖出6cm运动残影），右侧冰箱过曝成纯白（仅见“설렁탕”韩文便签边角）。窗光斜劈锁骨（形成锯齿状曝光明暗分界线），两缕棕发粘在汗湿颈侧（发梢虚化如毛玻璃）。镜头畸变使微波炉绿灯在开衫领口映出椭圆霉斑色光晕。",
                "negative_prompt": "低质量，模糊画面",
                "width": 1920,
                "height": 816,  # 21:9比例
                "sample_strength": 0.7,
                "response_format": "url"
            }
        }
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for test_case in test_cases:
        print(f"\n🧪 测试用例: {test_case['name']}")
        print("-" * 50)
        
        test_data = test_case['data']
        print(f"📋 请求参数:")
        print(f"   模型: {test_data['model']}")
        print(f"   提示词: {test_data['prompt']}")
        print(f"   尺寸: {test_data['width']}x{test_data['height']}")
        print(f"   精细度: {test_data['sample_strength']}")
        
        # 尝试每个session_id
        test_success = False
        for i, session_id in enumerate(test_session_ids):
            try:
                print(f"\n🔑 尝试session {i+1}/{len(test_session_ids)}: {session_id[:20]}...")
                
                headers = {
                    "Authorization": f"Bearer {session_id}",
                    "Content-Type": "application/json"
                }
                
                # 记录请求开始时间
                start_time = time.time()
                
                # 发送请求
                response = requests.post(url, headers=headers, json=test_data, timeout=300)
                
                # 记录请求结束时间
                end_time = time.time()
                request_time = end_time - start_time
                
                print(f"📥 响应状态码: {response.status_code}")
                print(f"⏱️ 请求耗时: {request_time:.2f}秒")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        print(f"📋 响应格式检查:")
                        print(f"   响应类型: {type(result)}")
                        print(f"   包含字段: {list(result.keys()) if isinstance(result, dict) else 'N/A'}")
                        
                        # 检查标准OpenAI格式
                        if isinstance(result, dict):
                            created = result.get("created")
                            data_list = result.get("data", [])
                            
                            print(f"   created: {created}")
                            print(f"   data长度: {len(data_list)}")
                            
                            if data_list and isinstance(data_list, list):
                                print(f"✅ 立即成功生成 {len(data_list)} 张图片:")
                                for idx, item in enumerate(data_list):
                                    if isinstance(item, dict) and "url" in item:
                                        url_preview = item["url"][:80] + "..." if len(item["url"]) > 80 else item["url"]
                                        print(f"   {idx+1}. {url_preview}")

                                test_success = True
                                success_count += 1
                                print(f"🎉 测试用例 '{test_case['name']}' 成功！")
                                break
                            elif created:
                                # 🔥 老王修复：如果data为空但有created，可能需要轮询等待
                                print(f"🔄 data为空但有created时间戳，开始轮询等待生成完成...")

                                max_polls = 20  # 最多轮询20次
                                poll_interval = 10  # 每10秒轮询一次

                                for poll_count in range(1, max_polls + 1):
                                    print(f"🔄 轮询 {poll_count}/{max_polls}...")
                                    time.sleep(poll_interval)

                                    try:
                                        # 重新请求检查结果
                                        poll_response = requests.post(url, headers=headers, json=test_data, timeout=60)

                                        if poll_response.status_code == 200:
                                            poll_result = poll_response.json()
                                            poll_data = poll_result.get("data", [])
                                            poll_created = poll_result.get("created")

                                            print(f"   轮询响应: created={poll_created}, data长度={len(poll_data)}")

                                            # 检查是否有结果
                                            if poll_data and len(poll_data) > 0:
                                                print(f"✅ 轮询成功！生成 {len(poll_data)} 张图片:")
                                                for idx, item in enumerate(poll_data):
                                                    if isinstance(item, dict) and "url" in item:
                                                        url_preview = item["url"][:80] + "..." if len(item["url"]) > 80 else item["url"]
                                                        print(f"   {idx+1}. {url_preview}")

                                                test_success = True
                                                success_count += 1
                                                print(f"🎉 测试用例 '{test_case['name']}' 轮询成功！")
                                                break
                                            else:
                                                print(f"   仍在生成中...")
                                        else:
                                            print(f"   轮询失败: HTTP {poll_response.status_code}")

                                    except Exception as poll_e:
                                        print(f"   轮询异常: {poll_e}")

                                if not test_success:
                                    print(f"⚠️ 轮询超时，未获得结果")
                            else:
                                print(f"⚠️ data字段为空且无created时间戳: {data_list}")
                        else:
                            print(f"⚠️ 响应格式不是字典: {result}")
                            
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析失败: {e}")
                        print(f"   原始响应: {response.text[:200]}...")
                        
                else:
                    print(f"❌ HTTP错误: {response.status_code}")
                    print(f"   错误内容: {response.text[:200]}...")
                    
            except requests.exceptions.Timeout:
                print(f"⏰ session {i+1} 请求超时（5分钟）")
                continue
            except Exception as e:
                print(f"💥 session {i+1} 异常: {e}")
                continue
        
        if not test_success:
            print(f"❌ 测试用例 '{test_case['name']}' 失败！所有session都无法成功")
    
    # 测试总结
    print("\n" + "=" * 70)
    print("📊 测试总结:")
    print(f"   总测试用例: {total_tests}")
    print(f"   成功用例: {success_count}")
    print(f"   失败用例: {total_tests - success_count}")
    print(f"   成功率: {(success_count/total_tests)*100:.1f}%")
    
    if success_count == total_tests:
        print("🎉 所有测试用例都成功！新版API工作正常！")
        print("✅ 可以安全地同步到drawing_skill.py")
    elif success_count > 0:
        print("⚠️ 部分测试用例成功，请检查失败的用例")
        print("🔍 建议检查session_id配置和网络连接")
    else:
        print("❌ 所有测试用例都失败！")
        print("🚨 请检查API服务是否正常部署")
        print("🔍 请检查IP、端口、session_id配置")
    
    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔥 新版jimeng API验证完成！")
    
    return success_count == total_tests

def test_api_compatibility():
    """测试API兼容性"""
    print("\n🔧 API兼容性测试:")
    print("-" * 30)
    
    # 测试不同的请求格式
    compatibility_tests = [
        {
            "name": "最小参数测试",
            "data": {
                "model": "jimeng-4.0",
                "prompt": "测试图片"
            }
        },
        {
            "name": "完整参数测试", 
            "data": {
                "model": "jimeng-4.0",
                "prompt": "完整参数测试图片",
                "negative_prompt": "低质量",
                "width": 512,
                "height": 512,
                "sample_strength": 0.6,
                "response_format": "url"
            }
        }
    ]
    
    url = "http://**************:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"  # 替换为实际session_id
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    for test in compatibility_tests:
        print(f"\n🧪 {test['name']}:")
        try:
            response = requests.post(url, headers=headers, json=test['data'], timeout=60)
            print(f"   状态码: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                data_count = len(result.get('data', []))
                print(f"   ✅ 成功，生成 {data_count} 张图片")
            else:
                print(f"   ❌ 失败: {response.text[:100]}")
        except Exception as e:
            print(f"   💥 异常: {e}")

if __name__ == "__main__":
    print("🚀 开始验证新版jimeng-free-api-all...")
    
    # 主要测试
    main_success = test_new_jimeng_api()
    
    # 兼容性测试
    test_api_compatibility()
    
    print("\n" + "=" * 70)
    if main_success:
        print("🎊 验证完成！新版API工作正常，可以同步到生产环境！")
    else:
        print("⚠️ 验证发现问题，请修复后再同步到生产环境！")
