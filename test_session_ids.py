#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试 jimeng session_id 有效性
🔥 老王专用脚本 - 验证哪些 session_id 还有效
"""

import requests
import json
import time

def test_session_id(session_id: str) -> bool:
    """测试单个 session_id 是否有效"""
    url = "http://124.221.30.195:47653/v1/images/generations"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "jimeng-4.0",
        "prompt": "测试图片",
        "negative_prompt": "",
        "width": 512,
        "height": 512,
        "sample_strength": 0.5,
        "response_format": "url"
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            # 检查是否有登录错误
            if "code" in result and result.get("code") == -2001:
                if "check login error" in result.get("message", ""):
                    print(f"❌ Session ID 无效: {session_id[:20]}... (登录验证失败)")
                    return False
            
            # 如果没有错误码或错误码为0，说明session_id有效
            if "code" not in result or result.get("code") == 0:
                print(f"✅ Session ID 有效: {session_id[:20]}...")
                return True
            else:
                print(f"⚠️  Session ID 可能有问题: {session_id[:20]}... (错误码: {result.get('code')})")
                return False
        else:
            print(f"❌ HTTP错误 {response.status_code}: {session_id[:20]}...")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {session_id[:20]}... - {e}")
        return False

def main():
    """主函数"""
    print("🔥 老王的 session_id 有效性测试脚本")
    print("=" * 50)
    
    # 当前配置文件中的 session_ids
    current_session_ids = [
        "1e6aa5b4800b56a3e345bacacfaa7c09",
        "96425b141fffc2443d5abdafb494c184", 
        "661e6f442106e10947d9e838a656293b"
    ]
    
    # 一些备用的 session_ids（你可以添加更多）
    backup_session_ids = [
        "3714d0ec74234a6f797c2e9d32d539d3",
        "60b8e545a59ff3fbd478cdba53ae7676"
    ]
    
    all_session_ids = current_session_ids + backup_session_ids
    
    print(f"📋 测试 {len(all_session_ids)} 个 session_id...")
    
    valid_session_ids = []
    
    for i, session_id in enumerate(all_session_ids, 1):
        print(f"\n--- 测试 {i}/{len(all_session_ids)} ---")
        if test_session_id(session_id):
            valid_session_ids.append(session_id)
        time.sleep(1)  # 避免请求过快
    
    print(f"\n" + "=" * 50)
    print(f"🎯 测试结果:")
    print(f"   总数: {len(all_session_ids)}")
    print(f"   有效: {len(valid_session_ids)}")
    print(f"   无效: {len(all_session_ids) - len(valid_session_ids)}")
    
    if valid_session_ids:
        print(f"\n✅ 有效的 session_ids:")
        for session_id in valid_session_ids:
            print(f"   {session_id}")
        
        # 生成配置文件格式
        session_ids_str = ",".join(valid_session_ids)
        print(f"\n📝 配置文件格式:")
        print(f'   "session_id": "{session_ids_str}"')
    else:
        print(f"\n❌ 没有找到有效的 session_id！")
        print(f"💡 建议：")
        print(f"   1. 检查网络连接")
        print(f"   2. 获取新的 session_id")
        print(f"   3. 联系API提供方")

if __name__ == "__main__":
    main()
