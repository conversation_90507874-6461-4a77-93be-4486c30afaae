#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试drawing_skill.py与后端API的兼容性 - 🔥 老王验证
"""

import sys
import os
import json
import time

# 添加项目根目录到路径
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(PROJECT_ROOT)

def test_drawing_skill_compatibility():
    """测试drawing_skill.py与后端API的兼容性"""
    print("🔥 老王测试drawing_skill.py兼容性")
    print("=" * 50)
    
    try:
        # 导入drawing_skill
        from cognitive_modules.skills.drawing_skill import DrawingSkill
        
        # 创建drawing_skill实例
        drawing_skill = DrawingSkill()
        print("✅ drawing_skill初始化成功")
        
        # 测试用例
        test_cases = [
            {
                "name": "正方形1:1",
                "input_text": "beautiful square garden",
                "expected_ratio": "1:1"
            },
            {
                "name": "宽屏16:9", 
                "input_text": "widescreen landscape view",
                "expected_ratio": "16:9"
            },
            {
                "name": "竖屏9:16",
                "input_text": "mobile portrait photo",
                "expected_ratio": "9:16"
            }
        ]
        
        success_count = 0
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n--- 兼容性测试 {i}: {test_case['name']} ---")
            print(f"📝 输入: {test_case['input_text']}")
            print(f"🎯 期望比例: {test_case['expected_ratio']}")
            
            try:
                start_time = time.time()
                
                # 调用drawing_skill的execute方法
                result = drawing_skill.execute(
                    input_text=test_case['input_text'],
                    user_id="test_user"
                )
                
                elapsed_time = time.time() - start_time
                print(f"⏱️  执行耗时: {elapsed_time:.2f}秒")
                
                # 检查结果
                if result and isinstance(result, dict):
                    success = result.get("success", False)
                    message = result.get("message", "")
                    result_data = result.get("result", {})
                    
                    if success:
                        print(f"✅ 兼容性测试成功！")
                        print(f"   消息: {message}")
                        
                        if isinstance(result_data, dict):
                            image_url = result_data.get("image_url", "")
                            service = result_data.get("service", "")
                            if image_url:
                                print(f"   图片URL: {image_url[:80]}...")
                                print(f"   服务: {service}")
                        
                        success_count += 1
                    else:
                        print(f"⚠️  兼容性测试失败: {message}")
                        print(f"   详细信息: {result}")
                else:
                    print(f"❌ 返回结果格式异常: {result}")
                    
            except Exception as e:
                print(f"❌ 兼容性测试异常: {e}")
                import traceback
                traceback.print_exc()
            
            # 间隔一下避免频繁请求
            if i < len(test_cases):
                print(f"⏳ 等待30秒...")
                time.sleep(30)
        
        print(f"\n" + "=" * 50)
        print(f"🎯 兼容性测试结果: {success_count}/{len(test_cases)} 成功")
        
        return success_count >= 2  # 至少2个成功就算通过
        
    except Exception as e:
        print(f"❌ drawing_skill导入或初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dimension_mapping():
    """测试尺寸映射功能"""
    print(f"\n🎨 测试尺寸映射功能")
    print("=" * 40)
    
    try:
        from cognitive_modules.skills.drawing_skill import DrawingSkill
        
        drawing_skill = DrawingSkill()
        
        # 测试各种比例的尺寸映射
        test_ratios = [
            "1:1", "16:9", "9:16", "4:3", "3:4", "21:9", "3:2", "2:3"
        ]
        
        print("📐 尺寸映射测试:")
        for ratio in test_ratios:
            width, height, keling_w, keling_h = drawing_skill._get_image_dimensions(ratio)
            print(f"   {ratio:>4} → {width:>4}x{height:<4} (可灵: {keling_w}x{keling_h})")
        
        print("\n✅ 尺寸映射功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 尺寸映射测试失败: {e}")
        return False

def test_jimeng_api_direct():
    """直接测试jimeng API调用"""
    print(f"\n🎯 直接测试jimeng API调用")
    print("=" * 40)
    
    try:
        from cognitive_modules.skills.drawing_skill import DrawingSkill
        
        drawing_skill = DrawingSkill()
        
        # 直接调用jimeng_generate_images方法
        result = drawing_skill.jimeng_generate_images(
            prompt="beautiful 4K landscape",
            model="jimeng-4.0",
            negative_prompt="low quality",
            width=4096,  # 4K正方形
            height=4096,
            sample_strength=0.7
        )
        
        print(f"📋 jimeng API直接调用结果:")
        print(f"   类型: {type(result)}")
        
        if isinstance(result, dict):
            if "image_urls" in result:
                image_urls = result["image_urls"]
                print(f"   ✅ 成功获得 {len(image_urls)} 张图片")
                for i, url in enumerate(image_urls):
                    print(f"      图片 {i+1}: {url[:80]}...")
                return True
            elif "error" in result:
                error_msg = result["error"]
                print(f"   ❌ API错误: {error_msg}")
                return False
            else:
                print(f"   ⚠️  未知格式: {result}")
                return False
        else:
            print(f"   ❌ 返回格式异常: {result}")
            return False
            
    except Exception as e:
        print(f"❌ jimeng API直接调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔥🔥🔥 老王的drawing_skill.py兼容性验证 🔥🔥🔥")
    print("=" * 60)
    
    # 测试1: 尺寸映射功能
    result1 = test_dimension_mapping()
    
    # 测试2: jimeng API直接调用
    result2 = test_jimeng_api_direct()
    
    # 测试3: drawing_skill完整流程
    result3 = test_drawing_skill_compatibility()
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"🎯 最终兼容性验证结果:")
    print(f"   尺寸映射功能: {'✅ 成功' if result1 else '❌ 失败'}")
    print(f"   jimeng API直接调用: {'✅ 成功' if result2 else '❌ 失败'}")
    print(f"   drawing_skill完整流程: {'✅ 成功' if result3 else '❌ 失败'}")
    
    if result1 and result2 and result3:
        print(f"\n🎉 恭喜！drawing_skill.py完全兼容后端API！")
        print(f"💡 兼容的功能:")
        print(f"   1. ✅ 官方4K标准尺寸映射")
        print(f"   2. ✅ 完整参数传递 (width/height/sample_strength)")
        print(f"   3. ✅ 智能比例识别和映射")
        print(f"   4. ✅ 多服务降级机制 (jimeng->keling->minimax)")
        print(f"   5. ✅ 错误处理和重试机制")
        print(f"\n🚀 可以正式使用drawing_skill.py进行绘画！")
    elif result1 and result2:
        print(f"\n⚠️  基础功能正常，完整流程需要优化")
    else:
        print(f"\n😤 还有兼容性问题需要继续修复")

if __name__ == "__main__":
    main()
