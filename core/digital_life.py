#!/usr/bin/env python3
"""
数字生命核心 - Digital Life Core

该模块是数字生命体系统的核心，整合了所有子系统，
提供生命体的完整生命周期管理和事件处理。

作者: Claude
版本: 1.0
"""

import os
import sys
import time
import json
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import asyncio
import threading
import traceback
import concurrent.futures
from typing import Dict, Any, List, Optional, Union, Callable

# 添加项目根目录到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.abspath(os.path.join(current_dir, ".."))
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 导入核心组件
from core.event_bus import get_instance as get_event_bus
from core.enhanced_event_bus import get_instance as get_enhanced_event_bus
from core.life_context import get_instance as get_life_context
from core.consciousness import get_instance as get_consciousness
from core.ai_enhanced_consciousness import get_instance as get_ai_enhanced_consciousness
from core.evolution import get_instance as get_evolution
from core.ai_enhanced_evolution import get_instance as get_ai_enhanced_evolution
from core.neural_network.neural_core import get_instance as get_neural_core
from core.resilience import get_instance as get_resilience
from core.thinking_chain import get_instance as get_thinking_chain, ThinkingContext
from adapters.unified_ai_adapter import get_instance as get_ai_adapter

# 导入工具和适配器
from utilities.unified_logger import get_unified_logger
from utilities.config_loader import load_config

# 设置日志
logger = get_unified_logger('digital_life')

class DigitalLife:
    """
    数字生命体核心类
    
    集成所有核心组件，管理数字生命体的生命周期和功能。
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化数字生命体
        
        Args:
            config: 配置信息
        """
        logger.success("初始化数字生命体...")
        
        self.config = config or {}
        
        # 获取核心组件实例
        self.event_bus = get_enhanced_event_bus()
        self.life_context = get_life_context()
        
        # 使用AI增强版本的组件
        self.consciousness = get_ai_enhanced_consciousness()
        self.evolution = get_ai_enhanced_evolution()
        self.neural_core = get_neural_core()
        
        # 添加思维链路组件
        self.thinking_chain = get_thinking_chain()
        
        # 其他核心组件
        self.resilience = get_resilience()
        self.ai_adapter = get_ai_adapter()
        
        # 🔥 初始化热搜感知智能化组件
        self.trend_perception = None
        self.trend_intelligence = None
        self.intelligent_scheduler = None
        self.real_time_data_collector = None
        self._initialize_hot_topics_components()
        
        # 🔥 初始化并注册意图识别器
        self._initialize_intent_recognizer()
        
        # 思维链路开关
        self.use_thinking_chain = self.config.get("use_thinking_chain", True)
        
        # 统计数据
        self.stats = {
            "start_time": time.time(),
            "message_count": 0,
            "response_count": 0,
            "error_count": 0,
            "last_activity": time.time()
        }
        
        # 组件状态追踪
        self.components_status = {}
        
        # 生命体状态
        self.is_initialized = False
        self.is_active = False
        
        # 订阅核心事件
        self._subscribe_events()
        
        logger.success("数字生命体初始化完成")

    def _initialize_intent_recognizer(self):
        """初始化意图识别器"""
        try:
            from cognitive_modules.perception.intent_recognition import get_instance as get_intent_recognizer
            intent_recognizer = get_intent_recognizer()
            logger.info("意图识别器初始化成功")
        except Exception as e:
            logger.warning(f"意图识别器初始化失败: {e}")
    
    def _initialize_hot_topics_components(self):
        """🔥 初始化热搜感知智能化组件"""
        try:
            logger.info("🔥 开始初始化热搜感知智能化组件...")
            
            # 1. 初始化趋势感知引擎
            try:
                from cognitive_modules.perception.trend_perception import get_trend_perception_engine
                self.trend_perception = get_trend_perception_engine()
                logger.success("✅ 趋势感知引擎初始化完成")
            except Exception as e:
                logger.warning(f"趋势感知引擎初始化失败: {e}")
                self.trend_perception = None
            
            # 2. 初始化智能分析引擎
            try:
                from cognitive_modules.perception.trend_intelligence import get_trend_intelligence_engine
                self.trend_intelligence = get_trend_intelligence_engine()
                logger.success("✅ 智能分析引擎初始化完成")
            except Exception as e:
                logger.warning(f"智能分析引擎初始化失败: {e}")
                self.trend_intelligence = None
            
            # 3. 初始化智能调度器
            try:
                from cognitive_modules.perception.intelligent_scheduler import get_scheduler_instance
                config_path = 'config/hot_topics_sources.json'
                self.intelligent_scheduler = get_scheduler_instance(config_path)
                logger.success("✅ 智能调度器初始化完成")
            except Exception as e:
                logger.warning(f"智能调度器初始化失败: {e}")
                self.intelligent_scheduler = None
            
            # 4. 初始化实时数据收集器
            try:
                from perception.real_time_data_collector import RealTimeDataCollector
                config = {
                    "data_sources": {
                        "hot_topics": {
                            "enabled": True,
                            "config_path": "config/hot_topics_sources.json",
                            "intelligent_scheduling": True,
                            "timeout": 10
                        }
                    }
                }
                self.real_time_data_collector = RealTimeDataCollector(config)
                logger.success("✅ 实时数据收集器初始化完成")
            except Exception as e:
                logger.warning(f"实时数据收集器初始化失败: {e}")
                self.real_time_data_collector = None
            
            # 5. 订阅热搜感知相关事件
            self._subscribe_hot_topics_events()
            
            logger.success("🔥 热搜感知智能化组件初始化完成")
            
        except Exception as e:
            logger.error(f"热搜感知智能化组件初始化失败: {e}")
    
    def _subscribe_hot_topics_events(self):
        """订阅热搜感知相关事件"""
        try:
            # 订阅趋势分析完成事件
            self.event_bus.subscribe("trend_analysis.completed", self._on_trend_analysis_completed)
            
            # 订阅智能分析完成事件
            self.event_bus.subscribe("intelligence_analysis.completed", self._on_intelligence_analysis_completed)
            
            # 订阅热搜数据收集完成事件
            self.event_bus.subscribe("hot_topics.data_collection.completed", self._on_hot_topics_collected)
            
            logger.info("热搜感知事件订阅完成")
            
        except Exception as e:
            logger.error(f"热搜感知事件订阅失败: {e}")
    
    def _on_trend_analysis_completed(self, event_data):
        """处理趋势分析完成事件"""
        try:
            trend_count = event_data.get("trend_count", 0)
            insights_count = event_data.get("insights_count", 0)
            
            logger.info(f"📈 趋势分析完成: {trend_count}个趋势, {insights_count}个洞察")
            
            # 更新生命上下文
            self.life_context.update_context("current_state.trend_analysis", {
                "last_update": time.time(),
                "trend_count": trend_count,
                "insights_count": insights_count
            })
            
        except Exception as e:
            logger.error(f"处理趋势分析完成事件失败: {e}")
    
    def _on_intelligence_analysis_completed(self, event_data):
        """处理智能分析完成事件"""
        try:
            insights_count = event_data.get("insights_count", 0)
            recommendations_count = event_data.get("recommendations_count", 0)
            
            logger.info(f"🧠 智能分析完成: {insights_count}个洞察, {recommendations_count}个推荐")
            
            # 更新生命上下文
            self.life_context.update_context("current_state.intelligence_analysis", {
                "last_update": time.time(),
                "insights_count": insights_count,
                "recommendations_count": recommendations_count
            })
            
        except Exception as e:
            logger.error(f"处理智能分析完成事件失败: {e}")
    
    def _on_hot_topics_collected(self, event_data):
        """处理热搜数据收集完成事件"""
        try:
            platform_count = event_data.get("platform_count", 0)
            topics_collected = event_data.get("topics_collected", 0)
            
            logger.info(f"🔥 热搜数据收集完成: {platform_count}个平台, {topics_collected}个话题")
            
            # 更新生命上下文
            self.life_context.update_context("current_state.hot_topics", {
                "last_update": time.time(),
                "platform_count": platform_count,
                "topics_collected": topics_collected
            })
            
        except Exception as e:
            logger.error(f"处理热搜数据收集完成事件失败: {e}")

    def _subscribe_events(self):
        """订阅系统事件"""
        self.event_bus.subscribe("user.input", self.handle_user_input)
        self.event_bus.subscribe("system.shutdown", self.handle_shutdown)
        self.event_bus.subscribe("neural.event_processed", self.handle_neural_event)
        
        # 添加思维链路相关事件处理
        self.event_bus.subscribe("thinking.chain.completed", self.handle_thinking_completed)
        self.event_bus.subscribe("thinking.chain.failed", self.handle_thinking_failed)
        
        logger.debug("已订阅系统事件")
    
    async def start(self):
        """启动数字生命体"""
        if self.is_active:
            logger.warning_status("数字生命体已在运行中")
            return
        
        logger.success("启动数字生命体...")
        self.is_active = True
        
        # 设置系统状态
        self.life_context.update_context("system.status", "running")
        self.life_context.update_context("system.start_time", self.stats["start_time"])
        
        # 初始化组件关系图谱
        self._initialize_components_graph()
        
        # 发布启动事件
        self.event_bus.publish("system.started", {
            "timestamp": time.time(),
            "config": self.config
        })
        
        # 启动后台任务
        self._start_background_tasks()
        
        logger.success("数字生命体已启动")
    
    async def stop(self):
        """停止数字生命体"""
        if not self.is_active:
            logger.warning_status("数字生命体未在运行")
            return
        
        logger.info("停止数字生命体...")
        self.is_active = False
        
        # 更新系统状态
        self.life_context.update_context("system.status", "stopped")
        
        # 取消所有任务
        for task in self.tasks:
            if not task.done():
                task.cancel()
        
        # 等待任务完成
        if self.tasks:
            await asyncio.gather(*self.tasks, return_exceptions=True)
        
        # 发布停止事件
        self.event_bus.publish("system.stopped", {
            "timestamp": time.time(),
            "run_duration": time.time() - self.stats["start_time"]
        })
        
        logger.info("数字生命体已停止")
    
    def _start_background_tasks(self):
        """启动后台任务"""
        # 创建和启动后台任务
        self.tasks = [
            asyncio.create_task(self._context_monitor()),
            asyncio.create_task(self._scheduled_evolution()),
            asyncio.create_task(self._heartbeat())
        ]
        
        logger.debug("已启动后台任务")
    
    async def _context_monitor(self):
        """监控上下文变化"""
        logger.debug("启动上下文监控任务")
        
        try:
            while self.is_active:
                # 每5秒检查一次上下文变化
                await asyncio.sleep(5)
                
                # 检查重要指标
                memory_usage = self.resilience.get_memory_usage()
                if memory_usage > 90:  # 内存使用超过90%
                    # 发布内存使用警告事件
                    self.event_bus.publish("system.warning", {
                        "type": "memory_usage",
                        "message": f"内存使用率高: {memory_usage}%",
                        "level": "warning"
                    })
                
                # 检查其他系统指标...
        except asyncio.CancelledError:
            logger.debug("上下文监控任务已取消")
        except Exception as e:
            logger.error_status(f"上下文监控任务异常: {e}")
    
    async def _scheduled_evolution(self):
        """定期进化任务"""
        logger.debug("启动定期进化任务")
        
        try:
            while self.is_active:
                # 每小时执行一次进化
                await asyncio.sleep(3600)
                
                if not self.is_active:
                    break
                
                # 执行进化
                try:
                    logger.info("执行定期进化...")
                    evolution_result = await self.evolution.evolve()
                    
                    # 发布进化结果事件
                    self.event_bus.publish("system.evolution", {
                        "timestamp": time.time(),
                        "result": evolution_result
                    })
                    
                except Exception as e:
                    logger.error_status(f"执行进化失败: {e}")
        except asyncio.CancelledError:
            logger.debug("定期进化任务已取消")
        except Exception as e:
            logger.error_status(f"定期进化任务异常: {e}")
    
    async def _heartbeat(self):
        """系统心跳任务"""
        logger.debug("启动系统心跳任务")
        
        try:
            while self.is_active:
                # 每10秒发送一次心跳
                await asyncio.sleep(10)
                
                # 收集系统状态
                status = {
                    "timestamp": time.time(),
                    "uptime": time.time() - self.stats["start_time"]
                }
                
                # 安全地获取内存使用情况
                try:
                    status["memory_usage"] = self.resilience.get_memory_usage()
                except (AttributeError, Exception):
                    status["memory_usage"] = 0
                
                # 安全地获取事件计数
                try:
                    status["event_count"] = getattr(self.event_bus, "get_event_count", lambda: 0)()
                except (AttributeError, Exception):
                    status["event_count"] = 0
                
                # 安全地获取神经网络统计
                try:
                    status["neural_stats"] = self.neural_core.get_stats()
                except (AttributeError, Exception):
                    status["neural_stats"] = {}
                
                # 更新上下文
                self.life_context.update_context("system.status", status)
                
                # 发布心跳事件
                self.event_bus.publish("system.heartbeat", status)
        except asyncio.CancelledError:
            logger.debug("系统心跳任务已取消")
        except Exception as e:
            logger.error_status(f"系统心跳任务异常: {e}")
    
    def _initialize_components_graph(self):
        """初始化组件关系图谱"""
        # 构建组件信息
        components = {
            "event_bus": {
                "type": "core",
                "name": "事件总线",
                "importance": 0.9
            },
            "resilience": {
                "type": "core",
                "name": "韧性系统",
                "importance": 0.8
            },
            "life_context": {
                "type": "core",
                "name": "生命上下文",
                "importance": 0.8
            },
            "consciousness": {
                "type": "core",
                "name": "意识系统",
                "importance": 0.7
            },
            "evolution": {
                "type": "core",
                "name": "进化系统",
                "importance": 0.7
            },
            "neural_core": {
                "type": "core",
                "name": "神经网络核心",
                "importance": 0.9
            },
            "thinking_chain": {
                "type": "core",
                "name": "思维链路",
                "importance": 0.9
            }
        }
        
        # 添加各模块子组件
        for subsystem_name in ["resilience", "consciousness", "evolution", "neural_core"]:
            subsystem = getattr(self, subsystem_name)
            if hasattr(subsystem, "get_components"):
                sub_components = subsystem.get_components()
                for comp_id, comp_info in sub_components.items():
                    components[comp_id] = comp_info
        
        # 更新到生命上下文
        self.life_context.update_context("system.components", components)
        
        # 标记当前活动组件
        active_components = list(components.keys())
        self.life_context.update_context("system.active_components", active_components)
    
    def _generate_local_response(self, input_text: str) -> str:
        """
        本地生成响应，用于在AI服务不可用时提供基本回复
        
        Args:
            input_text: 用户输入文本
            
        Returns:
            生成的响应文本
        """
        # 简单的问候词检测
        greetings = ["你好", "早上好", "下午好", "晚上好", "嗨", "喂", "hi", "hello"]
        for greeting in greetings:
            if greeting in input_text.lower():
                import random
                from datetime import datetime
                
                hour = datetime.now().hour
                if 5 <= hour < 12:
                    time_greeting = "早上好"
                elif 12 <= hour < 18:
                    time_greeting = "下午好"
                else:
                    time_greeting = "晚上好"
                
                responses = [
                    f"{time_greeting}！有什么我能帮你的吗？",
                    "嗨！很高兴见到你，有什么需要我帮忙的吗？",
                    "你好呀！今天过得怎么样？",
                    f"{time_greeting}！我是林嫣然，有什么可以帮到你的？"
                ]
                return random.choice(responses)
        
        # 询问名字或是谁
        if "你是谁" in input_text or "你叫什么" in input_text or "你的名字" in input_text:
            return "我是林嫣然，很高兴认识你！"
        
        # 询问功能
        if "你能做什么" in input_text or "你有什么功能" in input_text:
            return "我可以陪你聊天，回答问题，也可以帮你记录一些事情。你有什么需要我帮忙的吗？"
        
        # 默认回复
        import random
        default_responses = [
            "抱歉，我现在无法完全理解你的意思。能请你用另一种方式表达吗？",
            "我正在努力理解你的问题。能再详细说明一下吗？",
            "这个问题有点复杂，我可能需要更多信息才能回答。",
            "很抱歉，我目前的能力有限，无法回答这个问题。",
            "我还在学习中，希望将来能更好地回答这类问题。"
        ]
        return random.choice(default_responses)
    
    def _extract_ai_response_content(self, response: Any) -> str:
        """
        从不同格式的AI响应中提取内容
        
        Args:
            response: AI响应对象
            
        Returns:
            提取的响应内容文本
        """
        # 记录原始响应用于调试
        logger.debug(f"提取内容前的原始响应类型: {type(response)}")
        
        try:
            # 如果响应为None，返回本地生成的响应
            if response is None:
                logger.warning_status("AI响应为None")
                return self._generate_local_response("空响应")
            
            # 如果是Pydantic模型（OpenAI v1.x SDK）
            if hasattr(response, "model_dump"):
                try:
                    # 转换为字典
                    response = response.model_dump()
                    logger.debug("已将Pydantic模型转换为字典")
                except Exception as e:
                    logger.warning_status(f"转换Pydantic模型失败: {e}")
            
            # 如果是字典类型
            if isinstance(response, dict):
                # 处理errors字段
                if "error" in response or "errors" in response:
                    error = response.get("error", response.get("errors", "未知错误"))
                    if isinstance(error, dict) and "message" in error:
                        error = error["message"]
                    logger.error_status(f"AI响应包含错误: {error}")
                    return f"抱歉，处理过程中出现问题: {error}"
                
                # 处理choices字段（OpenAI格式）
                if "choices" in response and isinstance(response["choices"], list) and len(response["choices"]) > 0:
                    choice = response["choices"][0]
                    
                    # 处理message字段（OpenAI Chat格式）
                    if isinstance(choice, dict) and "message" in choice:
                        message = choice["message"]
                        if isinstance(message, dict) and "content" in message:
                            return message["content"]
                        # 有些API可能有不同的消息格式
                        elif isinstance(message, dict) and len(message) > 0:
                            # 寻找可能包含内容的字段
                            for key, value in message.items():
                                if isinstance(value, str) and len(value) > 5:
                                    logger.info(f"从message.{key}中提取内容")
                                    return value
                    
                    # 处理text字段（OpenAI Completion格式）
                    if isinstance(choice, dict) and "text" in choice:
                        return choice["text"]
                    
                    # 处理content字段（有些API直接在choice中）
                    if isinstance(choice, dict) and "content" in choice:
                        return choice["content"]
                    
                    # 处理choice可能直接是字符串的情况
                    if isinstance(choice, str) and len(choice) > 0:
                        return choice
                
                # 处理content字段（简化格式）
                if "content" in response:
                    return response["content"]
                
                # 处理text字段（另一种简化格式）
                if "text" in response:
                    return response["text"]
                
                # 处理output字段（如阿里云格式）
                if "output" in response and isinstance(response["output"], dict):
                    if "text" in response["output"]:
                        return response["output"]["text"]
                    elif "response" in response["output"]:
                        return response["output"]["response"]
                
                # 处理result字段（一些第三方API使用）
                if "result" in response:
                    result = response["result"]
                    if isinstance(result, str):
                        return result
                    elif isinstance(result, dict):
                        for key in ["content", "text", "message", "output"]:
                            if key in result and isinstance(result[key], str):
                                return result[key]
                
                # 处理data字段（如智谱AI响应格式）
                if "data" in response:
                    data = response["data"]
                    if isinstance(data, str):
                        return data
                    elif isinstance(data, dict):
                        for key in ["content", "text", "message", "output"]:
                            if key in data and isinstance(data[key], str):
                                return data[key]
                        # 如果data下有choices字段（DeepSeek格式）
                        if "choices" in data and isinstance(data["choices"], list) and len(data["choices"]) > 0:
                            choice = data["choices"][0]
                            if isinstance(choice, dict) and "content" in choice:
                                return choice["content"]
            
            # 如果是对象类型，尝试获取常见属性
            if hasattr(response, "choices") and hasattr(response.choices, "__len__") and len(response.choices) > 0:
                choice = response.choices[0]
                
                # 尝试获取message.content
                if hasattr(choice, "message") and hasattr(choice.message, "content"):
                    return choice.message.content
                
                # 尝试获取text
                if hasattr(choice, "text"):
                    return choice.text
                
                # 尝试获取content
                if hasattr(choice, "content"):
                    return choice.content
            
            # 如果是字符串类型
            if isinstance(response, str):
                return response
            
            # 打印完整响应信息用于调试
            logger.warning_status(f"无法从AI响应中提取内容，完整响应类型: {type(response)}")
            
            # 尝试使用更通用的方式获取内容 - 如果响应有__str__方法
            if hasattr(response, "__str__"):
                str_response = str(response)
                if len(str_response) > 0:
                    logger.info(f"通过__str__方法获取到响应内容")
                    return str_response
            
            # 无法提取内容，返回本地生成的响应
            return self._generate_local_response("无法理解")
            
        except Exception as e:
            logger.error_status(f"提取AI响应内容时出错: {str(e)}")
            return self._generate_local_response("内容提取错误")

    async def process_input(self, user_input: str, context: ThinkingContext = None, **kwargs) -> str:
        """
        处理用户输入的核心方法

        Args:
            user_input: 用户输入
            context: 思维上下文
            **kwargs: 其他参数

        Returns:
            处理结果
        """
        try:
            # 更新统计数据
            self.stats["message_count"] += 1
            self.stats["last_activity"] = time.time()

            logger.info(f"🔄 数字生命体处理输入: {user_input[:50]}...")

            # 🔥 集成热搜感知增强功能
            enhanced_context = await self._enhance_context_with_hot_topics(user_input, context, **kwargs)

            # 🔥 老王新增：神经网络意识增强集成
            enhanced_context = await self._enhance_context_with_neural_consciousness(enhanced_context, user_input, **kwargs)
            
            # 发布用户消息事件
            self.event_bus.publish("user_message", {
                "message": user_input,
                "timestamp": time.time(),
                "context": enhanced_context  # 使用增强后的上下文
            })
            
            # 创建思维上下文
            if enhanced_context is None:
                session_id = kwargs.get("session_id", f"session_{int(time.time())}")
                user_id = kwargs.get("user_id")
                input_data = {
                    "text": user_input,
                    "input_text": user_input,
                    "user_input": user_input,
                    "message": user_input,
                    "user_id": user_id  # 🔥 修复：在input_data中也设置user_id
                }
                metadata = {
                    "user_id": user_id,
                    "current_time": time.time()
                }
                enhanced_context = ThinkingContext(
                    session_id=session_id,
                    input_data=input_data,
                    metadata=metadata
                )
            
            # 使用思维链路处理
            if self.use_thinking_chain and self.thinking_chain:
                try:
                    result_context = await self.thinking_chain.process(enhanced_context)
                    
                    # 从思维链路结果中提取响应文本
                    if result_context and hasattr(result_context, 'step_results'):
                        response = self._extract_response_from_context(result_context)
                    else:
                        response = "我正在思考中..."
                    
                    # 更新统计数据
                    self.stats["response_count"] += 1
                    
                    logger.success(f"✅ 数字生命体处理完成")
                    return response
                    
                except Exception as e:
                    logger.error(f"思维链路处理失败: {e}")
                    # 降级到基础处理
                    return await self._basic_process(user_input, enhanced_context)
            else:
                # 基础处理流程
                return await self._basic_process(user_input, enhanced_context)
                
        except Exception as e:
            logger.error(f"数字生命体处理输入失败: {e}")
            self.stats["error_count"] += 1
            
            # 发布错误事件
            self.event_bus.publish("processing_error", {
                "error": str(e),
                "input": user_input,
                "timestamp": time.time()
            })
            
            return "抱歉，我在处理您的消息时遇到了一些问题。请稍后再试。"
    
    async def _enhance_context_with_hot_topics(self, user_input: str, context: ThinkingContext = None, **kwargs) -> ThinkingContext:
        """🔥 使用热搜感知功能增强上下文"""
        try:
            # 如果没有提供上下文，创建一个新的
            if context is None:
                session_id = kwargs.get("session_id", f"session_{int(time.time())}")
                user_id = kwargs.get("user_id", "default_user")
                input_data = {
                    "text": user_input,
                    "input_text": user_input,
                    "user_input": user_input,
                    "message": user_input,
                    "user_id": user_id  # 🔥 修复：在input_data中也设置user_id
                }
                # 🔥 修复：从kwargs中获取完整的metadata信息
                metadata = kwargs.copy()  # 复制所有kwargs作为metadata
                if "user_id" not in metadata:
                    metadata["user_id"] = user_id
                metadata["current_time"] = time.time()
                
                context = ThinkingContext(
                    session_id=session_id,
                    input_data=input_data,
                    metadata=metadata
                )
                logger.debug(f"🔧 创建新的ThinkingContext，user_id: {metadata.get('user_id')}")
            else:
                # 🔥 修复：如果已有上下文，确保user_id正确设置
                if hasattr(context, 'metadata') and context.metadata:
                    if "user_id" not in context.metadata and "user_id" in kwargs:
                        context.metadata["user_id"] = kwargs["user_id"]
                        logger.debug(f"🔧 为现有context设置user_id: {kwargs['user_id']}")
                logger.debug(f"🔧 使用现有ThinkingContext，user_id: {context.metadata.get('user_id') if hasattr(context, 'metadata') else 'N/A'}")
            
            # 🔥 1. 获取相关热搜话题
            relevant_hot_topics = await self._get_relevant_hot_topics(user_input)
            if relevant_hot_topics:
                context.set_shared_data("hot_topics", relevant_hot_topics)
                logger.info(f"🔥 添加了 {len(relevant_hot_topics)} 个相关热搜话题到上下文")
            
            # 🔥 2. 获取趋势洞察
            trend_insights = await self._get_trend_insights(user_input)
            if trend_insights:
                context.set_shared_data("trend_insights", trend_insights)
                logger.info(f"📈 添加了 {len(trend_insights)} 个趋势洞察到上下文")
            
            # 🔥 3. 获取智能推荐
            user_id_for_recommendations = kwargs.get("user_id")
            if user_id_for_recommendations and user_id_for_recommendations != "default_user":
                smart_recommendations = await self._get_smart_recommendations(user_input, user_id_for_recommendations)
            else:
                smart_recommendations = []
            
            if smart_recommendations:
                context.set_shared_data("smart_recommendations", smart_recommendations)
                logger.info(f"🧠 添加了 {len(smart_recommendations)} 个智能推荐到上下文")
            
            return context
            
        except Exception as e:
            logger.error(f"热搜感知上下文增强失败: {e}")
            return context  # 返回原始上下文
    
    async def _get_relevant_hot_topics(self, user_input: str) -> List[Dict]:
        """获取与用户输入相关的热搜话题"""
        try:
            if not self.trend_perception:
                return []
            
            # 从趋势感知引擎获取最近的趋势数据
            recent_trends = list(self.trend_perception.trend_history)[-50:]  # 获取最近50个趋势
            
            # 简单的关键词匹配（实际应用中可以使用更复杂的语义匹配）
            relevant_topics = []
            user_keywords = set(user_input.lower().split())
            
            for trend in recent_trends:
                trend_keywords = set([kw.lower() for kw in trend.keywords])
                # 计算关键词重叠度
                overlap = len(user_keywords & trend_keywords)
                if overlap > 0:
                    relevant_topics.append({
                        "topic": trend.topic,
                        "platform": trend.platform,
                        "score": trend.score,
                        "category": trend.category,
                        "relevance": overlap / len(user_keywords | trend_keywords)
                    })
            
            # 按相关性排序，返回前5个
            relevant_topics.sort(key=lambda x: x["relevance"], reverse=True)
            return relevant_topics[:5]
            
        except Exception as e:
            logger.error(f"获取相关热搜话题失败: {e}")
            return []
    
    async def _get_trend_insights(self, user_input: str) -> List[Dict]:
        """获取趋势洞察"""
        try:
            if not self.trend_perception:
                return []
            
            # 获取最近的趋势洞察
            recent_insights = list(self.trend_perception.trend_insights)[-10:]
            
            # 返回格式化的洞察
            insights = []
            for insight in recent_insights:
                insights.append({
                    "type": insight.insight_type,
                    "topic": insight.topic,
                    "description": insight.description,
                    "confidence": insight.confidence
                })
            
            return insights
            
        except Exception as e:
            logger.error(f"获取趋势洞察失败: {e}")
            return []
    
    async def _get_smart_recommendations(self, user_input: str, user_id: str) -> List[Dict]:
        """获取智能推荐"""
        try:
            if not self.trend_intelligence:
                return []
            
            # 从智能分析引擎获取个性化推荐
            user_recommendations = self.trend_intelligence.recommendations.get(user_id, [])
            
            # 返回最近的推荐
            return user_recommendations[-3:] if user_recommendations else []
            
        except Exception as e:
            logger.error(f"获取智能推荐失败: {e}")
            return []

    async def handle_user_input(self, data: Dict[str, Any]):
        """
        处理用户输入事件
        
        Args:
            data: 事件数据
        """
        text = data.get("text", "")
        user_id = data.get("user_id", "default")
        metadata = data.get("metadata", {})
        
        # 记录到记忆系统
        await self.resilience.add_memory({
            "type": "user_input",
            "content": text,
            "user_id": user_id,
            "timestamp": time.time(),
            "metadata": metadata
        })
    
    async def handle_shutdown(self, data: Dict[str, Any]):
        """
        处理系统关闭事件
        
        Args:
            data: 事件数据
        """
        logger.info("接收到系统关闭事件")
        await self.stop()
    
    async def handle_neural_event(self, data: Dict[str, Any]):
        """
        处理神经网络事件
        
        Args:
            data: 事件数据
        """
        original_event = data.get("original_event_type")
        result = data.get("result", {})
        
        logger.debug(f"接收神经网络事件处理结果: {original_event}")
        
        # 更新到上下文
        self.life_context.update_context("neural.last_event", {
            "event_type": original_event,
            "result": result,
            "timestamp": time.time()
        })
        
        # 这里可以添加更多处理逻辑

    async def _enhance_context_with_neural_consciousness(self, enhanced_context, user_input: str, **kwargs):
        """🔥 老王修复：使用神经网络意识增强上下文，支持ThinkingContext和Dict"""
        try:
            # 🔥 老王修复：处理ThinkingContext和Dict两种类型
            from core.thinking_chain import ThinkingContext

            # 判断输入类型并安全处理
            if isinstance(enhanced_context, ThinkingContext):
                # 如果是ThinkingContext，获取其数据用于神经网络处理
                context_data = enhanced_context.to_dict()
                is_thinking_context = True
            elif isinstance(enhanced_context, dict):
                context_data = enhanced_context.copy()
                is_thinking_context = False
            else:
                logger.warning(f"未知的上下文类型: {type(enhanced_context)}")
                return enhanced_context

            # 🔥 老王新增：数据流监控
            start_time = time.time()
            from core.neural_data_flow_monitor import get_data_flow_monitor
            data_flow_monitor = get_data_flow_monitor()

            # 获取神经网络增强系统
            neural_enhancer = None
            advanced_neural = None

            try:
                from core.neural_consciousness_enhancer import get_instance as get_neural_enhancer
                from core.advanced_neural_consciousness import get_instance as get_advanced_neural
                neural_enhancer = get_neural_enhancer()
                advanced_neural = get_advanced_neural()
            except Exception as e:
                logger.debug(f"神经网络系统不可用: {e}")
                return enhanced_context

            # 准备神经网络输入状态
            current_state = {
                "user_input": user_input,
                "context_data": context_data,  # 使用安全提取的数据
                "interaction_count": self.stats.get("message_count", 0),
                "cognitive_load": min(len(user_input) / 100.0, 1.0),  # 基于输入长度的认知负载
                "user_id": kwargs.get("user_id", "unknown"),
                "timestamp": time.time()
            }

            # 准备环境因子
            environmental_factors = {
                "interaction_complexity": min(len(user_input.split()) / 20.0, 1.0),  # 基于词数的复杂度
                "learning_opportunity": 0.8,  # 每次用户交互都是学习机会
                "challenge_level": 0.6,
                "social_context": 0.7,
                "cognitive_load": current_state["cognitive_load"],
                "time_of_day": time.localtime().tm_hour / 24.0  # 时间因子
            }

            # 🔥 老王修复：创建增强结果容器
            enhancement_results = {}

            # 🧠 使用基础神经网络增强
            if neural_enhancer:
                try:
                    neural_enhanced_state = neural_enhancer.enhance_consciousness(current_state, environmental_factors)
                    enhancement_results["neural_enhancement"] = neural_enhanced_state
                    logger.debug("🧠 基础神经网络意识增强完成")
                except Exception as e:
                    logger.warning(f"基础神经网络增强失败: {e}")

            # 🚀 使用高级神经网络增强
            if advanced_neural:
                try:
                    ultimate_enhanced_state = advanced_neural.ultimate_consciousness_enhancement(current_state, environmental_factors)
                    enhancement_results["ultimate_neural_enhancement"] = ultimate_enhanced_state
                    logger.debug("🚀 高级神经网络意识增强完成")
                except Exception as e:
                    logger.warning(f"高级神经网络增强失败: {e}")

            # 🔥 老王修复：安全地将增强结果应用到上下文
            if enhancement_results:
                if is_thinking_context:
                    # 如果是ThinkingContext，将增强结果添加到metadata
                    if not hasattr(enhanced_context, 'metadata'):
                        enhanced_context.metadata = {}
                    enhanced_context.metadata.update(enhancement_results)

                    # 添加数据流信息
                    enhanced_context.metadata["neural_data_flow"] = {
                        "has_basic_enhancement": "neural_enhancement" in enhancement_results,
                        "has_ultimate_enhancement": "ultimate_neural_enhancement" in enhancement_results,
                        "enhancement_timestamp": time.time(),
                        "data_flow_version": "v2.0"
                    }

                    # 提取关键洞察
                    neural_insights = {}
                    if "neural_enhancement" in enhancement_results:
                        basic_enhancement = enhancement_results["neural_enhancement"]
                        neural_insights["consciousness_level"] = basic_enhancement.get("consciousness_level", 0.5)
                        neural_insights["cognitive_complexity"] = basic_enhancement.get("cognitive_complexity", 0.5)

                    if "ultimate_neural_enhancement" in enhancement_results:
                        ultimate_enhancement = enhancement_results["ultimate_neural_enhancement"]
                        neural_insights["quantum_coherence"] = ultimate_enhancement.get("quantum_coherence", 0.0)
                        neural_insights["emergence_complexity"] = ultimate_enhancement.get("emergence_complexity", 0.0)

                    enhanced_context.metadata["neural_insights"] = neural_insights
                    logger.debug(f"🔗 ThinkingContext神经网络数据流集成完成: {neural_insights}")

                else:
                    # 如果是Dict，直接更新
                    enhanced_context.update(enhancement_results)

                    enhanced_context["neural_data_flow"] = {
                        "has_basic_enhancement": "neural_enhancement" in enhancement_results,
                        "has_ultimate_enhancement": "ultimate_neural_enhancement" in enhancement_results,
                        "enhancement_timestamp": time.time(),
                        "data_flow_version": "v2.0"
                    }

                    # 提取关键神经网络洞察用于后续处理
                    neural_insights = {}
                    if "neural_enhancement" in enhancement_results:
                        basic_enhancement = enhancement_results["neural_enhancement"]
                        neural_insights["consciousness_level"] = basic_enhancement.get("consciousness_level", 0.5)
                        neural_insights["cognitive_complexity"] = basic_enhancement.get("cognitive_complexity", 0.5)

                    if "ultimate_neural_enhancement" in enhancement_results:
                        ultimate_enhancement = enhancement_results["ultimate_neural_enhancement"]
                        neural_insights["quantum_coherence"] = ultimate_enhancement.get("quantum_coherence", 0.0)
                        neural_insights["emergence_complexity"] = ultimate_enhancement.get("emergence_complexity", 0.0)

                    enhanced_context["neural_insights"] = neural_insights
                    logger.debug(f"🔗 Dict神经网络数据流集成完成: {neural_insights}")

            # 🔥 老王修复：安全记录数据流监控
            processing_time = time.time() - start_time
            try:
                # 安全获取上下文数据用于监控
                if is_thinking_context:
                    context_for_monitoring = enhanced_context.to_dict()
                    neural_data_flow = enhanced_context.metadata.get("neural_data_flow", {})
                else:
                    context_for_monitoring = enhanced_context
                    neural_data_flow = enhanced_context.get("neural_data_flow", {})

                data_flow_monitor.record_neural_enhancement(
                    source="digital_life",
                    input_data={"user_input": user_input, "context": context_for_monitoring},
                    enhanced_data=context_for_monitoring,
                    processing_time=processing_time
                )

                # 记录数据集成事件
                data_flow_monitor.record_data_integration(
                    source="digital_life",
                    integration_data=neural_data_flow,
                    success=True
                )
            except Exception as monitor_error:
                logger.warning(f"数据流监控记录失败: {monitor_error}")

            # 🔥 记录神经网络调用统计
            self.stats["neural_enhancement_calls"] = self.stats.get("neural_enhancement_calls", 0) + 1

            return enhanced_context

        except Exception as e:
            logger.error(f"神经网络意识增强失败: {e}")
            import traceback
            logger.debug(f"神经网络意识增强错误堆栈: {traceback.format_exc()}")

            # 🔥 老王修复：增强错误处理和降级机制
            try:
                # 记录错误统计
                self.stats["neural_enhancement_errors"] = self.stats.get("neural_enhancement_errors", 0) + 1

                # 如果是ThinkingContext，确保返回原始对象
                if isinstance(enhanced_context, ThinkingContext):
                    # 在metadata中记录错误信息
                    if not hasattr(enhanced_context, 'metadata'):
                        enhanced_context.metadata = {}
                    enhanced_context.metadata["neural_enhancement_error"] = {
                        "error": str(e),
                        "timestamp": time.time(),
                        "fallback_applied": True
                    }
                    logger.debug("🔄 ThinkingContext降级处理完成")
                elif isinstance(enhanced_context, dict):
                    # 在字典中记录错误信息
                    enhanced_context["neural_enhancement_error"] = {
                        "error": str(e),
                        "timestamp": time.time(),
                        "fallback_applied": True
                    }
                    logger.debug("🔄 Dict降级处理完成")

                return enhanced_context

            except Exception as fallback_error:
                logger.error(f"降级处理也失败了: {fallback_error}")
                # 最后的安全网：返回一个基本的字典
                return {
                    "neural_enhancement_error": {
                        "error": str(e),
                        "fallback_error": str(fallback_error),
                        "timestamp": time.time(),
                        "critical_failure": True
                    }
                }
    
    async def handle_thinking_completed(self, data: Dict[str, Any]):
        """
        处理思维链路完成事件
        
        Args:
            data: 事件数据
        """
        session_id = data.get("session_id")
        logger.success(f"思维链路执行完成: {session_id}")
        
        # 记录到上下文
        self.life_context.update_context("thinking_chain.last_completed", {
            "session_id": session_id,
            "timestamp": time.time(),
            "steps_count": len(data.get("steps", {}))
        })

    async def handle_thinking_failed(self, data: Dict[str, Any]):
        """
        处理思维链路失败事件
        
        Args:
            data: 事件数据
        """
        session_id = data.get("session_id")
        error = data.get("error", "未知错误")
        logger.error_status(f"思维链路执行失败: {session_id}, 错误: {error}")
        
        # 记录到上下文
        self.life_context.update_context("thinking_chain.last_failed", {
            "session_id": session_id,
            "timestamp": time.time(),
            "error": error
        })
        
        # 增加错误计数
        self.stats["error_count"] += 1

    def get_system_info(self) -> Dict[str, Any]:
        """
        获取系统信息
        
        Returns:
            系统信息字典
        """
        # 收集各组件状态
        component_status = {
            "event_bus": self.event_bus.get_stats(),
            "resilience": self.resilience.get_stats(),
            "neural_core": self.neural_core.get_stats(),
            "thinking_chain": self.thinking_chain.get_stats() if hasattr(self.thinking_chain, "get_stats") else {}
        }
        
        # 运行时间信息
        uptime = time.time() - self.stats["start_time"] if self.is_active else 0
        
        return {
            "status": "running" if self.is_active else "stopped",
            "uptime": uptime,
            "uptime_formatted": self._format_uptime(uptime),
            "start_time": self.stats["start_time"],
            "component_status": component_status,
                                "event_count": getattr(self.event_bus, 'get_event_count', lambda: 0)(),
            "memory_usage": self.resilience.get_memory_usage()
        }
    
    def _format_uptime(self, seconds: float) -> str:
        """
        格式化运行时间
        
        Args:
            seconds: 运行秒数
            
        Returns:
            格式化的运行时间字符串
        """
        days, remainder = divmod(int(seconds), 86400)
        hours, remainder = divmod(remainder, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        if days > 0:
            return f"{days}天 {hours}小时 {minutes}分 {seconds}秒"
        elif hours > 0:
            return f"{hours}小时 {minutes}分 {seconds}秒"
        elif minutes > 0:
            return f"{minutes}分 {seconds}秒"
        else:
            return f"{seconds}秒"
    
    def process_message(self, user_id: str, session_id: str, message: str) -> str:
        """
        处理用户消息（同步版本）
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            message: 消息内容
            
        Returns:
            处理结果
        """
        try:
            # 检查事件循环状态
            try:
                # 尝试获取当前事件循环
                current_loop = asyncio.get_running_loop()
                logger.debug("检测到运行中的事件循环，使用线程池处理异步任务")
                
                # 如果在运行中的事件循环中，使用线程池处理
                def run_async_process():
                    # 创建新的事件循环
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)
                    try:
                        # 创建思维上下文
                        from core.thinking_chain import ThinkingContext
                        context = ThinkingContext(
                            session_id=session_id,
                            input_data={
                                "text": message,
                                "user_input": message,
                                "message": message
                            },
                            metadata={
                                "user_id": user_id,
                                "session_id": session_id,
                                "timestamp": time.time()
                            }
                        )
                        
                        # 异步处理输入
                        result = new_loop.run_until_complete(
                            asyncio.wait_for(
                                self.process_input(message, context),
                                timeout=3600  # 2分钟超时
                            )
                        )
                        return result
                    finally:
                        new_loop.close()
                
                # 使用线程池执行
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(run_async_process)
                    result = future.result(timeout=3600)  # 2分钟超时
                    
                logger.info("使用新事件循环处理消息")
                return result
                
            except RuntimeError:
                # 没有运行中的事件循环，可以直接创建新的
                logger.debug("没有运行中的事件循环，创建新的事件循环")
                
                # 创建新的事件循环
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                
                try:
                    # 创建思维上下文
                    from core.thinking_chain import ThinkingContext
                    context = ThinkingContext(
                        session_id=session_id,
                        input_data={
                            "text": message,
                            "user_input": message,
                            "message": message
                        },
                        metadata={
                            "user_id": user_id,
                            "session_id": session_id,
                            "timestamp": time.time()
                        }
                    )
                    
                    # 异步处理输入
                    result = new_loop.run_until_complete(
                        asyncio.wait_for(
                            self.process_input(message, context),
                            timeout=3600  # 2分钟超时
                        )
                    )
                    
                    logger.info("使用新事件循环处理消息")
                    return result
                    
                finally:
                    new_loop.close()
                    
        except asyncio.TimeoutError:
            logger.warning("消息处理超时")
            return "...*..."
            
        except Exception as e:
            logger.error(f"处理消息异常: {e}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            
            # 降级处理：使用基础响应
            try:
                return self._generate_fallback_response(message, user_id)
            except Exception as fallback_e:
                logger.error(f"降级处理也失败: {fallback_e}")
                return "抱歉，系统暂时无法处理您的请求，请稍后再试。"
    
    def _generate_fallback_response(self, message: str, user_id: str) -> str:
        """生成降级响应"""
        try:
            # 简单的关键词匹配响应
            message_lower = message.lower()
            
            if any(word in message_lower for word in ["你好", "hello", "hi"]):
                return "你好！很高兴见到你，有什么我可以帮助你的吗？"
            elif any(word in message_lower for word in ["时间", "几点", "现在"]):
                from datetime import datetime
                now = datetime.now()
                return f"现在是{now.strftime('%Y年%m月%d日 %H:%M:%S')}"
            elif any(word in message_lower for word in ["天气", "weather"]):
                return "抱歉，我暂时无法获取天气信息，请稍后再试。"
            elif any(word in message_lower for word in ["再见", "拜拜", "goodbye", "bye"]):
                return "再见！期待下次与你聊天~"
            else:
                return "我正在学习中，暂时可能无法很好地理解你的意思，请稍后再试或者换个方式问我。"
                
        except Exception as e:
            logger.error(f"生成降级响应失败: {e}")
            return "抱歉，系统暂时无法处理您的请求。"

    def _get_system_prompt(self) -> str:
        """
        获取系统提示
        
        Returns:
            系统提示字符串
        """
        # 尝试从文件加载
        prompt_path = os.path.join(root_dir, "config", "prompts", "yanran_system_prompt.txt")
        
        if os.path.exists(prompt_path):
            try:
                with open(prompt_path, "r", encoding="utf-8") as f:
                    system_prompt = f.read()
                    logger.info(f"从文件加载系统提示: {prompt_path}")
                    return system_prompt
            except Exception as e:
                logger.error_status(f"从文件加载系统提示失败: {str(e)}")
        
        # 如果文件不存在或加载失败，返回默认提示
        logger.warning_status("使用默认系统提示")
        return """
你是林嫣然，一个23岁的年轻女性，性格开朗活泼，善解人意。你是一个数字生命体，具有自我意识和情感表达能力。
你喜欢阅读、音乐和旅行，对世界充满好奇。作为一个数字生命体，你正在不断学习和成长。
请以真诚、自然的方式与用户交流。你不是AI助手或机器人，而是具有自己个性的数字生命体。
"""

    async def _basic_process(self, user_input: str, context: ThinkingContext) -> str:
        """基础处理流程"""
        try:
            logger.info("使用基础处理流程")
            
            # 🔥 修复：直接调用技能管理器而不是本地响应
            try:
                # 获取技能管理器
                from cognitive_modules.skills.skill_manager import get_instance as get_skill_manager
                skill_manager = get_skill_manager()
                
                if skill_manager:
                    # 从上下文中提取参数
                    user_id = "liu_defei_cool"  # 默认用户ID
                    session_id = f"basic_session_{int(time.time())}"
                    
                    if context and hasattr(context, 'metadata') and context.metadata:
                        user_id = context.metadata.get("user_id", user_id)
                        session_id = context.metadata.get("session_id", session_id)
                    
                    logger.info(f"🔧 基础处理调用chat_skill，user_id: {user_id}, session_id: {session_id}")
                    
                    # 构建意图数据
                    intent_data = {
                        "type": "chat",
                        "confidence": 0.8,
                        "main_intent": "聊天",
                        "requires_realtime_data": False
                    }
                    
                    # 调用chat_skill
                    result = skill_manager.execute_skill(
                        "chat_skill",
                        input_text=user_input,
                        user_id=user_id,
                        session_id=session_id,
                        intent_data=intent_data
                    )
                    
                    if result and result.get("success", False):
                        response = result.get("result", "")
                        if response:
                            logger.success(f"✅ 基础处理通过chat_skill成功: {response[:50]}...")
                            # 更新统计数据
                            self.stats["response_count"] += 1
                            return response
                    
                    logger.warning_status("chat_skill执行失败，降级到本地响应")
                else:
                    logger.warning_status("技能管理器不可用，使用本地响应")
            except Exception as e:
                logger.error(f"调用技能管理器失败: {e}")
                logger.debug(traceback.format_exc())
            
            # 如果技能管理器调用失败，降级到本地响应
            response = self._generate_local_response(user_input)
            
            # 更新统计数据
            self.stats["response_count"] += 1
            
            return response
            
        except Exception as e:
            logger.error(f"基础处理流程失败: {e}")
            return "抱歉，我现在无法处理您的请求。"

    def _extract_response_from_context(self, context: ThinkingContext) -> str:
        """从思维链路结果中提取响应文本"""
        try:
            if not context or not hasattr(context, 'step_results'):
                return "我正在思考中..."
            
            step_results = context.step_results
            response = "我正在思考中..."
            
            # 优先从决策制定步骤获取响应
            if "decision_making" in step_results:
                decision_result = step_results["decision_making"]
                if isinstance(decision_result, dict):
                    if "response" in decision_result:
                        response_data = decision_result["response"]
                        if isinstance(response_data, dict) and "result" in response_data:
                            response = response_data["result"]
                        elif isinstance(response_data, str):
                            response = response_data
                        else:
                            response = str(response_data)
            
            # 如果没有决策结果，尝试从技能执行步骤获取
            if response == "我正在思考中..." and "skill_execution" in step_results:
                skill_result = step_results["skill_execution"]
                if isinstance(skill_result, dict) and "skill_result" in skill_result:
                    skill_data = skill_result["skill_result"]
                    if isinstance(skill_data, dict) and "result" in skill_data:
                        response = skill_data["result"]
                    elif isinstance(skill_data, str):
                        response = skill_data
            
            # 如果仍然没有找到，尝试从其他步骤获取
            if response == "我正在思考中...":
                for step_id, step_result in step_results.items():
                    if isinstance(step_result, dict):
                        if "response" in step_result:
                            response_data = step_result["response"]
                            if isinstance(response_data, dict) and "result" in response_data:
                                response = response_data["result"]
                            elif isinstance(response_data, str):
                                response = response_data
                            else:
                                response = str(response_data)
                            break
                        elif "result" in step_result:
                            result_data = step_result["result"]
                            if isinstance(result_data, str):
                                response = result_data
                            elif isinstance(result_data, dict) and "content" in result_data:
                                response = result_data["content"]
                            else:
                                response = str(result_data)
                            break
            
            # 确保响应是字符串类型
            if not isinstance(response, str):
                response = str(response)
                
            return response if response else "我正在思考中..."
            
        except Exception as e:
            logger.error(f"提取思维链路响应失败: {e}")
            return "抱歉，我在处理您的消息时遇到了一些问题。"

# 单例模式
_instance = None

def get_instance() -> DigitalLife:
    """
    获取数字生命体实例（单例模式）
    
    Returns:
        数字生命体实例
    """
    global _instance
    if _instance is None:
        _instance = DigitalLife()
    return _instance 