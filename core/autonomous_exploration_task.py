#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自主探索调度任务 - Autonomous Exploration Task

该任务负责将完整的自主探索系统集成到调度器中，实现：
1. 定期话题发现和分析
2. 在线搜索最新信息
3. 自主迭代生成新活动
4. 探索效果评估和学习
5. 地理位置探索和推荐

作者: 香草 💕
创建时间: 2025-01-08
版本: v1.0.0
"""

import os
import sys
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, current_dir)

from core.universal_scheduler import BaseTask, TaskResult, TaskStatus, TaskPriority
from utilities.unified_logger import get_unified_logger
from connectors.database.mysql_connector import MySQLConnector

class AutonomousExplorationTask(BaseTask):
    """自主探索任务 - 集成完整的自主探索系统到调度器"""
    
    def __init__(self, mysql_connector: MySQLConnector):
        super().__init__("autonomous_exploration", "自主探索任务", TaskPriority.MEDIUM)
        self.mysql_connector = mysql_connector
        self.logger = get_unified_logger('autonomous_exploration_task')
        
        # 探索引擎（延迟初始化）
        self.exploration_engine = None
        self.scripts_integration_service = None
        
        # 任务统计
        self.run_count = 0
        self.success_count = 0
        self.failure_count = 0
        self.last_run = None
        
        # 探索配置
        self.config = {
            'max_topics_per_session': 3,           # 每次最多探索3个话题
            'exploration_timeout': 300,            # 5分钟超时
            'min_interval_hours': 2,               # 最小间隔2小时
            'max_daily_explorations': 8,           # 每日最多8次探索
            'save_exploration_results': True,      # 保存探索结果
            'generate_activities_from_exploration': True  # 从探索生成活动
        }
        
        self.logger.info("🔍 自主探索调度任务初始化完成")
    
    def execute(self, context: Dict[str, Any]) -> TaskResult:
        """执行自主探索任务"""
        start_time = datetime.now()
        
        try:
            # 检查是否在合适的时间执行探索
            if not self._should_explore_now():
                return self._create_skip_result(start_time, "未到探索时间或已达每日限制")
            
            # 延迟初始化探索引擎
            if not self._ensure_exploration_engine():
                return self._create_error_result(start_time, "探索引擎初始化失败")
            
            # 执行自主探索
            exploration_result = self._run_autonomous_exploration()
            
            if exploration_result and exploration_result.get('success', False):
                # 根据探索结果生成新活动
                if self.config['generate_activities_from_exploration']:
                    activity_result = self._generate_activities_from_exploration(exploration_result)
                    exploration_result['generated_activities'] = activity_result
                
                # 更新统计
                self.run_count += 1
                self.success_count += 1
                self.last_run = start_time
                
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                self.logger.success(f"🔍 自主探索完成: 发现 {exploration_result.get('topics_explored', 0)} 个话题")
                
                return TaskResult(
                    task_id=self.task_id,
                    status=TaskStatus.COMPLETED,
                    start_time=start_time,
                    end_time=end_time,
                    duration=duration,
                    success=True,
                    result_data={
                        'topics_explored': exploration_result.get('topics_explored', 0),
                        'activities_generated': len(exploration_result.get('generated_activities', [])),
                        'exploration_confidence': exploration_result.get('avg_confidence', 0.0),
                        'new_discoveries': exploration_result.get('new_discoveries', []),
                        'exploration_session_id': exploration_result.get('session_id', ''),
                        'method': 'autonomous_exploration'
                    },
                    error_message=None
                )
            else:
                return self._create_error_result(start_time, "自主探索执行失败")
            
        except Exception as e:
            self.run_count += 1
            self.failure_count += 1
            self.last_run = start_time
            
            self.logger.error(f"🔍 自主探索任务执行失败: {e}")
            return self._create_error_result(start_time, str(e))
    
    def _should_explore_now(self) -> bool:
        """检查是否应该现在执行探索"""
        current_time = datetime.now()
        current_hour = current_time.hour
        
        # 林嫣然的作息时间：深夜不探索
        if current_hour >= 23 or current_hour < 7:
            self.logger.info(f"🌙 深夜休息时间 ({current_hour}:00)，跳过自主探索")
            return False
        
        # 检查最小间隔
        if self.last_run:
            time_since_last = current_time - self.last_run
            if time_since_last.total_seconds() < self.config['min_interval_hours'] * 3600:
                self.logger.info("⏰ 距离上次探索间隔太短，跳过")
                return False
        
        # 检查每日限制
        if self._get_today_exploration_count() >= self.config['max_daily_explorations']:
            self.logger.info("📈 今日探索次数已达上限，跳过")
            return False
        
        return True
    
    def _get_today_exploration_count(self) -> int:
        """获取今日已执行的探索次数"""
        try:
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            
            # 从数据库查询今日的探索记录
            success, result, error = self.mysql_connector.execute_query("""
                SELECT COUNT(*) as count FROM exploration_activities 
                WHERE created_at >= %s
            """, (today_start,))
            
            if success and result and len(result) > 0:
                return result[0].get('count', 0)
            else:
                return 0
        except Exception as e:
            self.logger.warning(f"🔍 获取今日探索次数失败: {e}")
            return 0
    
    def _ensure_exploration_engine(self) -> bool:
        """确保探索引擎已初始化"""
        try:
            if self.exploration_engine is None:
                from services.autonomous_exploration_engine.autonomous_exploration_engine import AutonomousExplorationEngine
                self.exploration_engine = AutonomousExplorationEngine()
                self.logger.info("🔍 自主探索引擎初始化完成")
            
            if self.scripts_integration_service is None:
                from services.script_integration_service import get_scripts_integration_service
                self.scripts_integration_service = get_scripts_integration_service()
                self.logger.info("📊 Scripts集成服务连接完成")
            
            return True
        except Exception as e:
            self.logger.error(f"🔍 探索引擎初始化失败: {e}")
            return False
    
    def _run_autonomous_exploration(self) -> Dict[str, Any]:
        """运行自主探索（在线程中执行异步任务）"""
        import concurrent.futures
        import threading
        import asyncio
        
        def run_exploration_session():
            """在新的事件循环中运行探索会话"""
            new_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(new_loop)
            
            try:
                # 启动探索会话
                session_result = new_loop.run_until_complete(
                    self.exploration_engine.start_exploration_session(
                        user_id="linyanran",
                        exploration_type="autonomous_discovery",
                        max_topics=self.config['max_topics_per_session']
                    )
                )
                
                if not session_result or session_result.get('session_id') is None:
                    self.logger.error("🔍 探索会话启动失败")
                    return {"success": False, "error": "会话启动失败"}
                
                session_id = session_result['session_id']
                explored_topics = []
                total_confidence = 0.0
                all_exploration_results = []  # 🔥 收集所有探索结果用于汇总
                
                # 🔥 数字生命核心设计：进行3轮多样化探索，模拟人类收集多条线索
                self.logger.info("🔍 开始数字生命3轮自主探索，收集多样化信息...")
                
                for i in range(self.config['max_topics_per_session']):
                    try:
                        self.logger.info(f"🔍 第 {i+1} 轮探索：发现新话题...")
                        
                        # 每轮重新发现话题，确保多样性
                        topics = new_loop.run_until_complete(
                            self.exploration_engine.discover_trending_topics(
                                context={'location': '上海市黄浦区', 'interests': ['生活', '文化', '美食']}
                            )
                        )
                        
                        if not topics:
                            self.logger.warning(f"🔍 第 {i+1} 轮未发现话题，跳过")
                            continue
                        
                        # 探索第一个话题（每轮可能不同，这就是多样性的来源）
                        topic = topics[0]
                        self.logger.info(f"🔍 第 {i+1} 轮探索话题: {topic.title}")
                        
                        exploration_result = new_loop.run_until_complete(
                            self.exploration_engine.explore_topic(topic)
                        )
                        
                        if exploration_result:
                            # 收集探索结果，不立即保存
                            all_exploration_results.append(exploration_result)
                            explored_topics.append({
                                'round': i+1,
                                'topic': topic.title,
                                'category': topic.category,
                                'confidence': exploration_result.confidence_score,
                                'content_generated': len(exploration_result.generated_content) > 0
                            })
                            total_confidence += exploration_result.confidence_score
                            
                            self.logger.info(f"🔍 第 {i+1} 轮探索完成: {topic.title} (置信度: {exploration_result.confidence_score:.2f})")
                        
                    except Exception as topic_error:
                        self.logger.warning(f"🔍 第 {i+1} 轮探索失败: {topic_error}")
                        continue
                
                # 🔥 关键：3轮探索完成后，汇总信息生成最终活动
                if all_exploration_results:
                    self.logger.info(f"🔍 3轮探索完成，开始汇总 {len(all_exploration_results)} 个探索结果...")
                    final_activity_result = self._aggregate_exploration_results(all_exploration_results)
                    
                    # 只保存1个汇总后的最终活动
                    if final_activity_result and self.config['generate_activities_from_exploration']:
                        save_result = self._save_aggregated_activity(final_activity_result)
                        self.logger.info(f"🔍 最终汇总活动已保存: {save_result}")
                else:
                    self.logger.warning("🔍 未收集到有效探索结果，无法生成最终活动")
                
                avg_confidence = total_confidence / len(explored_topics) if explored_topics else 0.0
                
                return {
                    "success": True,
                    "session_id": session_id,
                    "topics_explored": len(explored_topics),
                    "explored_topics": explored_topics,
                    "avg_confidence": avg_confidence,
                    "new_discoveries": [t['topic'] for t in explored_topics if t['confidence'] > 0.7]
                }
                
            except Exception as e:
                self.logger.error(f"🔍 探索会话执行失败: {e}")
                return {"success": False, "error": str(e)}
            finally:
                new_loop.close()
        
        try:
            # 使用线程池执行异步探索
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(run_exploration_session)
                result = future.result(timeout=self.config['exploration_timeout'])
            
            return result
        except concurrent.futures.TimeoutError:
            self.logger.error("🔍 自主探索超时")
            return {"success": False, "error": "探索超时"}
        except Exception as e:
            self.logger.error(f"🔍 自主探索执行失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _generate_activities_from_exploration(self, exploration_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """基于探索结果生成新活动"""
        generated_activities = []
        
        try:
            explored_topics = exploration_result.get('explored_topics', [])
            
            for topic_info in explored_topics[:2]:  # 最多生成2个活动
                if topic_info['confidence'] > 0.6:  # 只为高置信度话题生成活动
                    
                    # 使用异步方法生成活动
                    def generate_activity_sync():
                        new_loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(new_loop)
                        try:
                            result = new_loop.run_until_complete(
                                self.scripts_integration_service.generate_and_save_activity(
                                    user_id="linyanran",
                                    activity_type="exploration",
                                    time_slot=self._get_current_time_slot(),
                                    context={
                                        'exploration_topic': topic_info['topic'],
                                        'exploration_category': topic_info['category'],
                                        'confidence': topic_info['confidence'],
                                        'preferences': ['探索', '学习', '发现']
                                    }
                                )
                            )
                            return result
                        finally:
                            new_loop.close()
                    
                    activity_result = generate_activity_sync()
                    
                    if activity_result and activity_result.get('success'):
                        generated_activities.append({
                            'topic': topic_info['topic'],
                            'activity_title': activity_result.get('activity_title'),
                            'activity_content': activity_result.get('activity_script', '')[:100] + '...',
                            'reality_score': activity_result.get('reality_score', 0.0),
                            'saved_to_scripts': activity_result.get('saved_to_scripts', False)
                        })
        
        except Exception as e:
            self.logger.warning(f"🔍 从探索生成活动失败: {e}")
        
        return generated_activities
    
    def _get_current_time_slot(self) -> str:
        """获取当前时间段"""
        current_hour = datetime.now().hour
        
        if 6 <= current_hour < 12:
            return 'morning'
        elif 12 <= current_hour < 18:
            return 'afternoon'
        elif 18 <= current_hour < 22:
            return 'evening'
        else:
            return 'night'
    
    def _create_skip_result(self, start_time: datetime, reason: str) -> TaskResult:
        """创建跳过执行的结果"""
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        return TaskResult(
            task_id=self.task_id,
            status=TaskStatus.COMPLETED,
            start_time=start_time,
            end_time=end_time,
            duration=duration,
            success=True,
            result_data={'skipped': True, 'reason': reason},
            error_message=None
        )
    
    def _create_error_result(self, start_time: datetime, error_msg: str) -> TaskResult:
        """创建错误结果"""
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        return TaskResult(
            task_id=self.task_id,
            status=TaskStatus.FAILED,
            start_time=start_time,
            end_time=end_time,
            duration=duration,
            success=False,
            result_data=None,
            error_message=error_msg
        )
    
    def get_task_stats(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        success_rate = self.success_count / max(self.run_count, 1)
        
        return {
            'task_id': self.task_id,
            'run_count': self.run_count,
            'success_count': self.success_count,
            'failure_count': self.failure_count,
            'success_rate': success_rate,
            'last_run': self.last_run.isoformat() if self.last_run else None,
            'config': self.config,
            'today_explorations': self._get_today_exploration_count()
        }

    def _aggregate_exploration_results(self, exploration_results: List[Any]) -> Dict[str, Any]:
        """
        汇总3轮探索结果，生成最终综合活动
        
        这是数字生命活动优化的核心：模拟人类思维收集多条线索后的综合决策
        """
        try:
            if not exploration_results:
                return None
            
            self.logger.info("🔍 开始汇总探索结果，模拟人类综合分析过程...")
            
            # 收集所有探索内容
            all_topics = []
            all_generated_content = []
            all_activity_suggestions = []
            total_confidence = 0.0
            
            for result in exploration_results:
                all_topics.append(result.topic.title)
                all_generated_content.extend(result.generated_content)
                all_activity_suggestions.extend(result.activity_suggestions)
                total_confidence += result.confidence_score
            
            # 计算平均置信度
            avg_confidence = total_confidence / len(exploration_results)
            
            # 🔥 数字生命智能汇总：选择最优内容组合
            best_content = self._select_best_content(all_generated_content)
            best_activity = self._synthesize_final_activity(all_activity_suggestions, all_topics)
            
            # 生成汇总报告
            aggregated_result = {
                'aggregation_type': 'digital_life_synthesis',
                'source_explorations': len(exploration_results),
                'topics_explored': all_topics,
                'final_activity': best_activity,
                'selected_content': best_content,
                'confidence_score': avg_confidence,
                'synthesis_metadata': {
                    'total_content_pieces': len(all_generated_content),
                    'total_suggestions': len(all_activity_suggestions),
                    'synthesis_timestamp': datetime.now().isoformat()
                }
            }
            
            self.logger.info(f"🔍 探索结果汇总完成: 从{len(exploration_results)}轮探索中综合出最优活动")
            return aggregated_result
            
        except Exception as e:
            self.logger.error(f"🔍 汇总探索结果失败: {e}")
            return None
    
    def _select_best_content(self, all_content: List[Dict]) -> Dict[str, Any]:
        """从所有生成内容中选择最优内容"""
        if not all_content:
            return {}
        
        # 按质量评分排序，选择最优内容
        sorted_content = sorted(
            all_content, 
            key=lambda x: x.get('quality_score', 0), 
            reverse=True
        )
        
        best = sorted_content[0]
        self.logger.info(f"🔍 选择最优内容: {best.get('type', 'unknown')} (质量分: {best.get('quality_score', 0)})")
        return best
    
    def _synthesize_final_activity(self, all_suggestions: List[Dict], topics: List[str]) -> str:
        """综合所有建议，生成最终活动描述"""
        if not all_suggestions:
            # 基于话题生成兜底活动
            topic_summary = "、".join(topics[:3])  # 最多3个话题
            return f"基于今日探索的{topic_summary}等话题，进行深度思考和体验分享"
        
        # 选择评分最高的活动建议
        best_suggestion = max(all_suggestions, key=lambda x: x.get('feasibility_score', 0))
        
        # 融合多个话题的精华
        topic_essence = f"融合{len(topics)}个探索话题的精华"
        final_activity = f"{best_suggestion.get('title', '综合活动')}: {topic_essence}"
        
        self.logger.info(f"🔍 综合最终活动: {final_activity}")
        return final_activity
    
    def _save_aggregated_activity(self, aggregated_result: Dict[str, Any]) -> bool:
        """保存汇总后的最终活动（只保存一次）- 🔥 老王修复：直接生成真实活动详情，不保存一句话汇总"""
        try:
            if not self.scripts_integration_service:
                self.logger.warning("🔍 Scripts集成服务不可用，无法保存最终活动")
                return False

            # 🔥 老王修复：直接调用enhanced_activity_generator生成真实活动，跳过一句话汇总
            self.logger.info("🔍 直接生成真实活动详情，不使用一句话汇总...")

            # 构建探索上下文 - 基于探索结果生成真实活动
            exploration_context = {
                'user_id': 'linyanran',
                'activity_type': 'autonomous_exploration',
                'exploration_topics': aggregated_result.get('topics_explored', []),
                'exploration_summary': aggregated_result.get('final_activity', ''),
                'confidence_score': aggregated_result.get('avg_confidence', 0.7),
                'source': 'autonomous_exploration_engine'
            }

            # 🔥 关键修复：直接调用enhanced_activity_generator生成详细活动
            realistic_activity = self._generate_realistic_exploration_activity(exploration_context)

            if not realistic_activity or not realistic_activity.get('success'):
                self.logger.error("🔍 真实活动生成失败，这是P0级别问题！")
                # 🔥 老王修复：P0问题不能有备用方案，必须生成真实活动
                self.logger.error("🔍 按照用户要求，不能保存一句话汇总，必须生成详细活动")
                return False
            else:
                # 🔥 老王修复：获取生成的详细活动
                generated_activity = realistic_activity.get('activity_script', '')
                if not generated_activity:
                    # 尝试从enhanced_script中获取
                    enhanced_script = realistic_activity.get('enhanced_script', {})
                    generated_activity = enhanced_script.get('activity_description', '')

                if not generated_activity or len(generated_activity) < 40:
                    self.logger.error(f"🔍 生成的活动太短或为空: '{generated_activity}'")
                    self.logger.error("🔍 这违反了用户要求，不能保存简短汇总")
                    return False

                final_activity_data = {
                    'user_id': 'linyanran',
                    'activity_type': 'digital_life_exploration',
                    'time_slot': self._get_current_time_slot(),
                    'activity': generated_activity,
                    'mood': '充实且有收获',
                    'weather': '适宜探索',
                }
                self.logger.success(f"🔍 真实活动生成成功: {len(final_activity_data['activity'])}字符")
                self.logger.info(f"🔍 生成的活动内容: {final_activity_data['activity'][:100]}...")

            # 🔥 关键：只调用一次保存，避免重复
            import asyncio
            save_success = asyncio.run(
                self.scripts_integration_service._save_activity_to_scripts(
                    final_activity_data['time_slot'],
                    final_activity_data['weather'],
                    final_activity_data['activity'],
                    final_activity_data['mood'],
                    final_activity_data['user_id']
                )
            )

            if save_success:
                self.logger.success(f"🔍 最终汇总活动保存成功: {final_activity_data['activity'][:500]}...")
            else:
                self.logger.error("🔍 最终汇总活动保存失败")

            return save_success

        except Exception as e:
            self.logger.error(f"🔍 保存汇总活动异常: {e}")
            return False
    
    def _get_previous_activity_context(self) -> Dict[str, Any]:
        """获取前置活动上下文 - 🔥 老王新增：为探索活动提供延续性"""
        try:
            from connectors.database.mysql_connector import get_instance as get_mysql_connector
            mysql = get_mysql_connector()

            if not mysql or not mysql.is_available:
                self.logger.warning("🔍 MySQL连接器不可用，无法获取前置活动")
                return {}

            # 获取最新的3条活动记录
            success, results, error = mysql.get_latest_scripts(limit=3)
            if not success or not results:
                self.logger.warning(f"🔍 获取前置活动失败: {error}")
                return {}

            latest_activity = results[0]  # 最新的活动

            context = {
                'has_previous': True,
                'latest_activity': latest_activity.get('activity', ''),
                'latest_mood': latest_activity.get('mood', ''),
                'latest_weather': latest_activity.get('weather', ''),
                'latest_time': latest_activity.get('created_at', ''),
                'recent_activities': [r.get('activity', '') for r in results[:3]]
            }

            self.logger.info(f"🔍 获取前置活动上下文成功: {len(context['latest_activity'])}字符")
            return context

        except Exception as e:
            self.logger.error(f"🔍 获取前置活动上下文失败: {e}")
            return {}

    def _build_exploration_context(self, aggregated_result: Dict[str, Any], previous_context: Dict[str, Any]) -> Dict[str, Any]:
        """构建探索上下文 - 🔥 老王新增：整合探索结果和前置活动"""
        try:
            context = {
                'user_id': 'linyanran',
                'activity_type': 'autonomous_exploration',
                'time_slot': self._get_current_time_slot(),

                # 探索结果信息
                'exploration_topics': aggregated_result.get('topics_explored', []),
                'exploration_content': aggregated_result.get('selected_content', {}),
                'exploration_confidence': aggregated_result.get('confidence_score', 0.0),
                'exploration_summary': aggregated_result.get('final_activity', ''),

                # 前置活动信息（用于延续性）
                'previous_activity': previous_context.get('latest_activity', ''),
                'previous_mood': previous_context.get('latest_mood', ''),
                'previous_weather': previous_context.get('latest_weather', ''),

                # 生成参数
                'preferences': ['探索', '学习', '发现', '思考'],
                'location': '上海',  # 默认位置
                'weather_preference': '适宜探索',

                # 特殊标记
                'is_exploration_based': True,
                'requires_continuity': True
            }

            self.logger.info(f"🔍 探索上下文构建完成: {len(context['exploration_topics'])}个话题")
            return context

        except Exception as e:
            self.logger.error(f"🔍 构建探索上下文失败: {e}")
            return {}

    def _generate_realistic_exploration_activity(self, exploration_context: Dict[str, Any]) -> Dict[str, Any]:
        """生成基于探索的真实活动 - 🔥 老王新增：调用enhanced_activity_generator"""
        try:
            # 导入enhanced_activity_generator
            from services.enhanced_activity_generator.enhanced_activity_script_generator import EnhancedActivityScriptGenerator

            # 创建生成器实例
            activity_generator = EnhancedActivityScriptGenerator()

            # 异步调用生成方法
            import asyncio

            def run_generation():
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    result = new_loop.run_until_complete(
                        activity_generator.generate_realistic_activity(
                            user_id=exploration_context['user_id'],
                            activity_type=exploration_context['activity_type'],
                            current_context=exploration_context
                        )
                    )
                    return result
                finally:
                    new_loop.close()

            result = run_generation()

            if result and result.get('success'):
                self.logger.success(f"🔍 基于探索的真实活动生成成功")
                return result
            else:
                self.logger.error(f"🔍 基于探索的真实活动生成失败: {result}")
                return None

        except Exception as e:
            self.logger.error(f"🔍 生成基于探索的真实活动异常: {e}")
            return None

    def _force_generate_new_activity(self, exploration_context: Dict[str, Any]) -> Dict[str, Any]:
        """强制生成新活动，绕过重复检测 - 🔥 老王新增"""
        try:
            # 导入必要模块
            import asyncio
            import time
            from services.enhanced_activity_generator.enhanced_activity_script_generator import EnhancedActivityScriptGenerator

            # 创建生成器实例
            activity_generator = EnhancedActivityScriptGenerator()

            # 临时修改活动类型，绕过重复检测
            original_activity_type = exploration_context.get('activity_type', 'autonomous_exploration')
            exploration_context['activity_type'] = f"{original_activity_type}_force_{int(time.time())}"

            def run_generation():
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    result = new_loop.run_until_complete(
                        activity_generator.generate_realistic_activity(
                            user_id=exploration_context['user_id'],
                            activity_type=exploration_context['activity_type'],
                            current_context=exploration_context
                        )
                    )
                    return result
                finally:
                    new_loop.close()

            result = run_generation()

            # 恢复原始活动类型
            exploration_context['activity_type'] = original_activity_type

            if result and result.get('success'):
                self.logger.success(f"🔍 强制生成新活动成功")
                return result
            else:
                self.logger.error(f"🔍 强制生成新活动失败: {result}")
                return None

        except Exception as e:
            self.logger.error(f"🔍 强制生成新活动异常: {e}")
            return None

    def _get_current_time_slot(self) -> str:
        """获取当前时间段"""
        current_hour = datetime.now().hour
        if 6 <= current_hour < 12:
            return 'morning'
        elif 12 <= current_hour < 18:
            return 'afternoon'
        else:
            return 'evening'

def create_autonomous_exploration_task(mysql_connector: MySQLConnector) -> AutonomousExplorationTask:
    """创建自主探索任务实例"""
    return AutonomousExplorationTask(mysql_connector)
