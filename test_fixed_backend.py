#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修复后的后端API - 🔥 老王验证脚本
"""

import requests
import json
import time

def test_fixed_backend_api():
    """测试修复后的后端API"""
    print("🔥 老王测试修复后的后端API")
    print("=" * 50)
    
    # 后端API地址
    api_url = "http://124.221.30.195:47653/v1/images/generations"
    
    # 测试用例
    test_cases = [
        {
            "name": "英文简单提示词",
            "prompt": "cat",
            "expected_success": True
        },
        {
            "name": "中文简单提示词",
            "prompt": "猫",
            "expected_success": True
        },
        {
            "name": "英文复杂提示词",
            "prompt": "a beautiful sunset over the ocean with golden light",
            "expected_success": True
        },
        {
            "name": "中文复杂提示词",
            "prompt": "扎着高马尾的女孩的照片，嘴唇咬着黑色皮筋，青春靓丽，俏皮可爱",
            "expected_success": True
        }
    ]
    
    # 测试session_ids
    session_ids = [
        "1e6aa5b4800b56a3e345bacacfaa7c09",
        "3714d0ec74234a6f797c2e9d32d539d3", 
        "60b8e545a59ff3fbd478cdba53ae7676"
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {test_case['name']} ---")
        print(f"📝 提示词: {test_case['prompt']}")
        
        # 使用组合的session_ids
        combined_session_ids = ",".join(session_ids)
        
        headers = {
            "Authorization": f"Bearer {combined_session_ids}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "jimeng-4.0",
            "prompt": test_case["prompt"]
        }
        
        try:
            start_time = time.time()
            print(f"🚀 发送请求...")
            
            response = requests.post(api_url, headers=headers, json=data, timeout=300)
            elapsed_time = time.time() - start_time
            
            print(f"⏱️  请求耗时: {elapsed_time:.2f}秒")
            print(f"📡 状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                # 检查OpenAI格式的响应
                if "data" in result and isinstance(result["data"], list):
                    data_list = result["data"]
                    created = result.get("created")
                    
                    print(f"📋 创建时间: {created}")
                    print(f"📋 数据长度: {len(data_list)}")
                    
                    if data_list and len(data_list) > 0:
                        print(f"🎉 成功获得 {len(data_list)} 张图片！")
                        for j, item in enumerate(data_list):
                            if isinstance(item, dict) and "url" in item:
                                print(f"   图片 {j+1}: {item['url'][:80]}...")
                        success_count += 1
                    else:
                        print(f"⚠️  数据为空")
                else:
                    print(f"⚠️  响应格式异常: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   错误详情: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
                except:
                    print(f"   响应文本: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        # 间隔一下避免请求过快
        if i < len(test_cases):
            print(f"⏳ 等待10秒后进行下一个测试...")
            time.sleep(10)
    
    print(f"\n" + "=" * 50)
    print(f"🎯 测试结果: {success_count}/{len(test_cases)} 成功")
    
    if success_count == len(test_cases):
        print(f"🎉 所有测试通过！后端API修复成功！")
        return True
    elif success_count > 0:
        print(f"⚠️  部分测试成功，需要进一步优化")
        return True
    else:
        print(f"❌ 所有测试失败，需要继续调试")
        return False

def test_single_session_id():
    """测试单个session_id"""
    print(f"\n🧪 测试单个session_id")
    print("=" * 30)
    
    api_url = "http://124.221.30.195:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "jimeng-4.0",
        "prompt": "beautiful flower"
    }
    
    print(f"📝 提示词: {data['prompt']}")
    print(f"🔑 Session ID: {session_id[:20]}...")
    
    try:
        start_time = time.time()
        response = requests.post(api_url, headers=headers, json=data, timeout=300)
        elapsed_time = time.time() - start_time
        
        print(f"⏱️  请求耗时: {elapsed_time:.2f}秒")
        print(f"📡 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📋 响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if "data" in result and result["data"]:
                print(f"🎉 单个session_id测试成功！")
                return True
            else:
                print(f"⚠️  单个session_id测试无结果")
        else:
            print(f"❌ 单个session_id测试失败: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ 单个session_id测试异常: {e}")
    
    return False

def main():
    """主函数"""
    print("🔥🔥🔥 老王的后端API修复验证 🔥🔥🔥")
    print("=" * 60)
    
    # 测试1: 修复后的后端API
    result1 = test_fixed_backend_api()
    
    # 测试2: 单个session_id
    result2 = test_single_session_id()
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"🎯 最终验证结果:")
    print(f"   后端API测试: {'✅ 成功' if result1 else '❌ 失败'}")
    print(f"   单个session_id: {'✅ 成功' if result2 else '❌ 失败'}")
    
    if result1 and result2:
        print(f"\n🎉 恭喜！后端API修复完成，可以正常使用了！")
        print(f"💡 修复要点:")
        print(f"   1. 重写了轮询逻辑，直接使用get_history_by_ids")
        print(f"   2. 修正了轮询条件，检查item_list而不是状态码")
        print(f"   3. 优化了轮询间隔和超时设置")
        print(f"   4. 匹配了前端成功案例的API参数")
    elif result1:
        print(f"\n⚠️  后端API基本功能正常，但单个session_id需要优化")
    else:
        print(f"\n😤 后端API仍有问题，需要继续调试")

if __name__ == "__main__":
    main()
