#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
绘画技能模块 - Drawing Skill

提供AI绘画功能，支持多个绘画服务：jimeng（默认）、keling、minimax
"""

import os
import sys
import json
import time
from utilities.unified_logger import get_unified_logger, setup_unified_logging
import traceback
import requests
import re
from typing import Dict, List, Any, Optional

# 设置项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if PROJECT_ROOT not in sys.path:
    sys.path.append(PROJECT_ROOT)

# 导入工具和依赖
from utilities.unified_logger import get_unified_logger
from core.enhanced_event_bus import get_instance as get_event_bus
from core.life_context import get_instance as get_life_context
from core.skill_manager import Skill

# 尝试导入OpenAI（如果可用）
try:
    import openai
except ImportError:
    openai = None

# 初始化日志
logger = get_unified_logger("drawing_skill")


class Config:
    """配置类"""

    def __init__(self):
        # 从配置文件读取MiniMax API密钥
        config_path = os.path.join(PROJECT_ROOT, "config", "skills", "drawing_skill.json")
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    minimax_config = config.get("minimax", {})
                    self.voice_key = minimax_config.get("api_key", "")
                    self.minimax_enabled = minimax_config.get("enabled", False)
                    logger.success(
                        f"MiniMax配置加载成功: enabled={self.minimax_enabled}, api_key={'已配置' if self.voice_key else '未配置'}")
            except Exception as e:
                logger.error_status(f"读取MiniMax配置失败: {e}")
                self.voice_key = os.environ.get("MINIMAX_API_KEY", "")
                self.minimax_enabled = False
        else:
            self.voice_key = os.environ.get("MINIMAX_API_KEY", "")
            self.minimax_enabled = False


class DrawingSkill(Skill):
    """绘画技能类，提供AI绘画功能"""

    def __init__(self):
        """初始化绘画技能"""
        logger.success("初始化绘画技能...")

        # 调用父类初始化
        super().__init__(
            skill_id="drawing_skill",
            name="AI绘画技能",
            description="提供文本到图像的AI绘画功能，支持jimeng、keling、minimax",
            version="2.0.0"
        )

        # 获取事件总线和生命上下文
        self.event_bus = get_event_bus()
        self.life_context = get_life_context()

        # 加载配置
        self.config = self._load_config()

        # 添加能力标签
        self.capabilities = ["drawing", "image_generation", "art"]

        # 初始化OpenAI
        self.openai_available = self._init_openai_config()

        # 🔥 老王修复：从配置文件加载绘画服务配置
        self.jimeng_session_id = self._load_jimeng_session_id()
        self.is_keling = "1"  # 🔥 老王修复：开启可灵AI服务 0-Close 1-Open

        # 预设提示词
        self.pre_create_image = """你现在是视觉效果设计师，负责将用户输入的内容，始终围绕用户的核心意图和要素，并且进行质量、创意上的合理专业发散，并转换成丰富的视觉效果描述，然后用户会将你输出的描述用midjourney/即梦/可灵或其他AI画画工具渲染出来。\n\n 输出格式: image_prompt:你优化后的视觉效果的中文描述 \n\n aspect_ratio:你认为该视觉效果最佳的宽高比(可选值有：1:1 16:9 4:3 3:2 2:3 3:4 9:16 21:9，默认值为9:16)"""

        self.pre_image_model = "yanran_draw"

        # 记忆存储
        self.dro_context_memory = {}
        self.dro_internet = ""

        # 初始化事件处理器
        self._register_event_handlers()

        logger.success("绘画技能初始化完成: AI绘画技能 v2.0.0")

    def _load_jimeng_session_id(self) -> str:
        """🔥 老王修复：从配置文件加载jimeng session_id，支持多个session_id"""
        try:
            # 从drawing_skill.json配置文件加载
            config = self._load_config()
            jimeng_config = config.get("jimeng", {})
            session_id = jimeng_config.get("session_id", "")

            if session_id:
                # 如果有多个session_id，只返回第一个作为默认值
                # 其他session_id会在失败时作为备用
                first_session_id = session_id.split(",")[0].strip()
                logger.success(f"✅ 从配置文件加载jimeng session_id: {first_session_id[:20]}...")
                return first_session_id
            else:
                logger.warning("⚠️ 配置文件中未找到jimeng session_id")
        except Exception as e:
            logger.error_status(f"❌ 加载jimeng配置失败: {e}")

        # 使用默认的session_id（备用）
        default_session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
        logger.info(f"🔄 使用默认jimeng session_id: {default_session_id[:20]}...")
        return default_session_id

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        config_path = os.path.join(PROJECT_ROOT, "config", "skills", "drawing_skill.json")
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.debug(f"已加载绘画技能配置: {config_path}")
                return config
            except Exception as e:
                logger.error_status(f"加载绘画配置失败: {e}")

        # 返回默认配置
        return {
            "timeout": 600,
            "default_service": "jimeng",
            "services": ["jimeng", "keling", "minimax"]
        }

    def _init_openai_config(self) -> bool:
        """初始化OpenAI配置"""
        if not openai:
            logger.warning_status("OpenAI库未安装，提示词优化功能将不可用")
            return False

        try:
            # 从环境变量或配置文件获取API密钥
            api_key = os.environ.get("OPENAI_API_KEY")
            api_base = os.environ.get("OPENAI_API_BASE")

            if not api_key:
                # 尝试从配置文件获取
                config_path = os.path.join(PROJECT_ROOT, "config", "openai_config.json")
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        openai_config = json.load(f)
                        api_key = openai_config.get("api_key")
                        api_base = openai_config.get("api_base")

            if api_key:
                # 创建OpenAI客户端
                self.openai_client = openai.OpenAI(
                    api_key=api_key,
                    base_url=api_base
                )
                logger.success("OpenAI配置初始化成功")
                return True
            else:
                logger.warning_status("未找到OpenAI API密钥，提示词优化功能将不可用")
                return False
        except Exception as e:
            logger.error_status(f"初始化OpenAI配置失败: {e}")
            return False

    def _register_event_handlers(self):
        """注册事件处理器"""
        logger.success("初始化绘画技能事件处理器")

        try:
            # 注册绘画请求事件处理器
            self.event_bus.subscribe("drawing.request", self._handle_drawing_request)
            logger.info("已注册绘画请求事件处理器")
        except Exception as e:
            logger.error_status(f"注册事件处理器失败: {e}")

    def on_load(self) -> bool:
        """技能加载时调用"""
        logger.info("绘画技能加载")
        return True

    def on_unload(self) -> bool:
        """技能卸载时调用"""
        logger.info("绘画技能卸载")
        try:
            self.event_bus.unsubscribe("drawing.request", self._handle_drawing_request)
        except Exception as e:
            logger.warning_status(f"取消事件订阅失败: {e}")
        return True

    def on_enable(self) -> bool:
        """技能启用时调用"""
        self.enabled = True
        logger.info("绘画技能已启用")
        return True

    def on_disable(self) -> bool:
        """技能禁用时调用"""
        self.enabled = False
        logger.info("绘画技能已禁用")
        return True

    def can_handle(self, context: Dict[str, Any]) -> bool:
        """检查技能是否可以处理给定上下文"""
        if not self.enabled:
            return False

        # 检查是否是绘画请求
        intent = context.get("intent", {})
        intent_type = intent.get("type", "")

        if intent_type == "drawing" or intent_type == "image_generation":
            return True

        # 检查关键词
        if "text" in context:
            text = context["text"].lower()
            drawing_keywords = ["画一张", "绘画", "生成图片", "生成图像", "draw", "generate image"]

            for keyword in drawing_keywords:
                if keyword in text:
                    return True

        return False

    def handle(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理请求"""
        # 获取参数
        prompt = context.get("text", "")
        if not prompt and "prompt" in context:
            prompt = context["prompt"]
        if not prompt and "input_text" in context:
            prompt = context["input_text"]

        if not prompt:
            return {
                "success": False,
                "message": "绘画提示词不能为空",
                "result": None
            }

        # 更新使用次数和最后使用时间
        self.last_used = context.get("timestamp", 0)
        self.usage_count += 1

        # 执行绘画
        return self.execute(input_text=prompt, **context)

    def _handle_drawing_request(self, data: Dict[str, Any]):
        """处理绘画请求事件"""
        logger.debug(f"收到绘画请求: {data.get('prompt', '')[:50]}...")

        request_id = data.get("request_id", "")
        prompt = data.get("prompt", "")

        # 执行绘画
        result = self.execute(input_text=prompt, **data)

        # 🔥 老王修复：安全处理result，防止NoneType错误
        if result and isinstance(result, dict):
            success = result.get("success", False)
            message = result.get("message", "")
            result_data = result.get("result", None)
        else:
            success = False
            message = "绘画执行失败"
            result_data = None

        # 发布结果事件
        self.event_bus.publish("drawing.response", {
            "request_id": request_id,
            "success": success,
            "message": message,
            "result": result_data
        })

    def execute(self, input_text: str, user_id: str = "default", **kwargs) -> Dict[str, Any]:
        """
        执行绘画 - 按照主人的要求：默认使用jimeng，失败时依次尝试keling、minimax

        Args:
            input_text: 绘画提示词
            user_id: 用户ID
            **kwargs: 其他参数

        Returns:
            绘画结果
        """
        # 🔥 老王修复：优化重复调用检测和缓存机制
        import hashlib
        import threading

        # 创建执行ID用于日志追踪
        execution_id = f"drawing_{hashlib.md5(input_text.encode('utf-8')).hexdigest()[:8]}"
        thread_id = threading.current_thread().ident

        logger.debug(f"🎨 [{execution_id}] Thread-{thread_id} 绘画技能执行开始")
        logger.debug(f"🎨 [{execution_id}] 参数提取完成: user_id={user_id}, input_length={len(input_text)}")

        # 🔥 老王修复：改进缓存键生成逻辑，基于内容而非时间窗口
        content_hash = hashlib.md5(input_text.encode('utf-8')).hexdigest()[:12]
        request_key = f"{user_id}_{content_hash}"  # 移除时间窗口，纯粹基于内容

        # 初始化缓存
        if not hasattr(self, '_request_cache'):
            self._request_cache = {}

        if not hasattr(self, '_request_timestamps'):
            self._request_timestamps = {}

        # 🔥 老王修复：定期清理过期缓存，避免内存泄漏
        current_time = time.time()
        if not hasattr(self, '_last_cache_cleanup'):
            self._last_cache_cleanup = current_time

        # 每10分钟清理一次过期缓存
        if current_time - self._last_cache_cleanup > 600:
            self._cleanup_expired_cache()
            self._last_cache_cleanup = current_time

        # 🔥 老王修复：检查重复调用（5分钟内相同内容）
        if request_key in self._request_cache:
            last_request_time = self._request_timestamps.get(request_key, 0)
            time_since_last = current_time - last_request_time

            # 5分钟内的相同请求返回缓存
            if time_since_last < 300:  # 改为300秒（5分钟）
                cached_result = self._request_cache[request_key]
                logger.warning(
                    f"🔄 [{execution_id}] 检测到重复绘画请求，距离上次请求 {time_since_last:.1f}秒，返回缓存结果")
                return cached_result
            else:
                # 超过5分钟，删除旧缓存
                logger.debug(f"🔄 [{execution_id}] 相同内容的请求已过期({time_since_last:.1f}秒)，重新执行")
                del self._request_cache[request_key]
                del self._request_timestamps[request_key]

        # 🔥 老王修复：不要在开始时设置时间戳，应该在成功完成后设置

        # 🔥 老王修复：增强input_text参数验证，避免空content传给AI
        if not input_text or not input_text.strip():
            logger.error(f"🎨 [{execution_id}] 绘画提示词为空，无法执行绘画")
            return {
                "success": False,
                "message": "绘画提示词不能为空",
                "result": None,
                "error": "empty_input_text"
            }

        # 清理和验证输入文本
        input_text = input_text.strip()
        logger.debug(f"🎨 [{execution_id}] 执行绘画: {input_text[:50]}...")

        try:
            # 需要画画
            logger.debug("需要画画")
            if not hasattr(self, 'dro_context_memory') or self.dro_context_memory is None:
                self.dro_context_memory = {}

            if user_id not in self.dro_context_memory:
                self.dro_context_memory[user_id] = []

            # 🔥 老王修复：确保添加到记忆中的content不为空
            if input_text and input_text.strip():
                self.dro_context_memory[user_id].append({"role": "user", "content": input_text})
                logger.debug(f"🎨 [{execution_id}] 添加用户消息到记忆: {input_text[:30]}...")
            else:
                logger.error(f"🎨 [{execution_id}] 尝试添加空内容到记忆，跳过")
                return {
                    "success": False,
                    "message": "绘画提示词内容为空",
                    "result": None,
                    "error": "empty_content_after_strip"
                }

            if len(self.dro_context_memory[user_id]) > 6:  # 5次记忆
                self.dro_context_memory[user_id].pop(0)

            dro_messages = [{"role": "system", "content": self.pre_create_image}]
            dro_messages.extend(self.dro_context_memory[user_id])

            # 🔥 老王修复：验证消息内容，确保没有空content
            valid_messages = []
            for msg in dro_messages:
                if msg.get("content") and msg["content"].strip():
                    valid_messages.append(msg)
                else:
                    logger.warning(f"🎨 [{execution_id}] 跳过空消息: {msg}")

            if not valid_messages or len(valid_messages) < 2:  # 至少需要system和user消息
                logger.error(f"🎨 [{execution_id}] 有效消息不足，无法进行提示词优化")
                return {
                    "success": False,
                    "message": "消息内容不足，无法生成绘画",
                    "result": None,
                    "error": "insufficient_valid_messages"
                }

            logger.debug(f"🎨 [{execution_id}] 有效消息数量: {len(valid_messages)}")
            logger.debug(f"dro_messages：{valid_messages}")

            # 优化提示词
            ai_image_prompt, ai_aspect_ratio = self._optimize_prompt(valid_messages, input_text, user_id)
            logger.debug(f"🎨 [{execution_id}] 优化后的提示词: {ai_image_prompt[:50]}...")

            # 设置图片尺寸
            image_width, image_height, keling_width, keling_height = self._get_image_dimensions(ai_aspect_ratio)

            # 按照主人要求的顺序尝试绘画服务：jimeng -> keling -> minimax
            services_to_try = [
                ("jimeng", lambda: self._try_jimeng_service(ai_image_prompt, image_width, image_height)),
                ("keling", lambda: self._try_keling_service(ai_image_prompt, keling_width, keling_height)),
                ("minimax", lambda: self._try_minimax_service(ai_image_prompt, ai_aspect_ratio))
            ]

            start_time = time.time()

            for service_name, service_func in services_to_try:
                logger.debug(f"🎨 [{execution_id}] 尝试使用 {service_name} 引擎")
                try:
                    result = service_func()
                    # 🔥 老王修复：增强result的None检查，防止NoneType错误
                    if result and isinstance(result, dict) and result.get("success"):
                        elapsed_time = time.time() - start_time
                        logger.success(f"✅ {service_name}服务绘画成功！")

                        # 🔥 老王修复：安全处理result，避免字符串调用.get()方法
                        result_data = result.get("result")
                        image_url = ""

                        # 安全提取image_url
                        if isinstance(result_data, dict):
                            image_url = result_data.get("image_url", "")
                        elif "image_url" in result:
                            image_url = result.get("image_url", "")

                        final_result = {
                            "success": True,
                            "message": f"绘画成功（{service_name}服务）",
                            "result": result_data,
                            "image_url": image_url,
                            "service": service_name,
                            "execution_time": elapsed_time
                        }

                        # 🔥 老王修复：只有成功完成后才设置缓存和时间戳
                        self._request_cache[request_key] = final_result
                        self._request_timestamps[request_key] = current_time  # 使用请求开始时间

                        logger.debug(f"🎨 [{execution_id}] 绘画完成 (耗时: {elapsed_time:.2f}s)")
                        return final_result
                    else:
                        # 🔥 老王修复：安全处理result为None的情况
                        if result is None:
                            error_msg = "返回None"
                        elif isinstance(result, dict):
                            error_msg = result.get('message', '返回空数据')
                        else:
                            error_msg = f"返回格式异常: {type(result)}"
                        logger.warning(f"🎨 [{execution_id}] {service_name} 引擎失败: {error_msg}")
                except Exception as e:
                    logger.error_status(f"🎨 [{execution_id}] {service_name} 引擎异常: {e}")
                    continue

            # 所有服务都失败
            elapsed_time = time.time() - start_time
            logger.error_status(f"❌ 🎨 [{execution_id}] 所有画图引擎都失败 (耗时: {elapsed_time:.2f}s)")

            failure_result = {
                "success": False,
                "message": "抱歉，画图失败了，请稍后再试",
                "result": None,
                "error": "所有画图引擎都失败",
                "execution_time": elapsed_time
            }

            # 🔥 老王修复：失败结果缓存时间较短（30秒），避免长时间重复失败
            if elapsed_time > 30:  # 只有耗时较长的失败才缓存
                self._request_cache[request_key] = failure_result
                # 设置较短的失败缓存时间：当前时间 - 30秒，意味着30秒后过期
                self._request_timestamps[request_key] = current_time - 30

            return failure_result

        except Exception as e:
            elapsed_time = time.time() - start_time if 'start_time' in locals() else 0
            logger.error_status(f"💥 🎨 [{execution_id}] 绘画处理异常: {e}")
            traceback.print_exc()

            error_result = {
                "success": False,
                "message": f"绘画处理异常: {e}",
                "result": None,
                "error": str(e),
                "execution_time": elapsed_time
            }

            # 异常结果不缓存，允许重试
            return error_result

    def _cleanup_expired_cache(self):
        """清理过期缓存"""
        if not hasattr(self, '_request_cache') or not hasattr(self, '_request_timestamps'):
            return

        current_time = time.time()
        expired_keys = []

        for key in list(self._request_cache.keys()):
            if key in self._request_timestamps:
                last_time = self._request_timestamps[key]
                # 清理超过10分钟的缓存
                if current_time - last_time > 600:
                    expired_keys.append(key)
            else:
                # 没有时间戳记录的也清理
                expired_keys.append(key)

        for key in expired_keys:
            self._request_cache.pop(key, None)
            self._request_timestamps.pop(key, None)

        if expired_keys:
            logger.debug(f"🧹 清理了{len(expired_keys)}个过期缓存项")

    def _optimize_prompt(self, dro_messages, input_text, user_id):
        """优化提示词"""
        try:
            # 检查是否是自拍照需求
            is_selfie = self._is_selfie_request(input_text)

            # 如果是自拍照，添加角色特征描述
            if is_selfie:
                character_description = self._get_character_appearance()
                if character_description:
                    # 在系统提示词中添加角色特征
                    enhanced_system_prompt = dro_messages[0][
                                                 "content"] + f"\n\n特别说明：如果用户要求自拍照，请根据以下角色特征描述生成提示词：\n{character_description}"
                    dro_messages[0]["content"] = enhanced_system_prompt
                    logger.debug("已添加林嫣然角色特征到自拍照提示词")

            # 使用统一的AI服务适配器而不是直接调用OpenAI
            from adapters.ai_service_adapter import get_instance as get_ai_service_adapter
            ai_adapter = get_ai_service_adapter()

            # 准备消息格式
            messages = dro_messages

            # 调用AI服务适配器
            response = ai_adapter.get_completion(
                messages=messages,
                model="yanran_draw",
                temperature=0.9,
                max_tokens=6000,
                timeout=120  # 降低超时时间避免长时间等待
            )

            # 处理AI服务适配器的响应
            from_response = ""
            if response:
                if isinstance(response, str):
                    # 如果是字符串，直接使用
                    from_response = response.strip()
                elif isinstance(response, dict):
                    # 如果是字典，尝试提取content
                    if "choices" in response and len(response["choices"]) > 0:
                        choice = response["choices"][0]
                        if "message" in choice and "content" in choice["message"]:
                            from_response = choice["message"]["content"].strip()
                    elif "content" in response:
                        from_response = response["content"].strip()
                    else:
                        logger.warning_status(f"未知的响应格式: {response}")

            if from_response:
                logger.debug(f"解析后的画图prompt：{from_response[:100]}...")

                # 确保用户ID在记忆中存在
                if user_id not in self.dro_context_memory:
                    self.dro_context_memory[user_id] = []

                self.dro_context_memory[user_id].append({"role": "assistant", "content": from_response})

                ai_image_prompt, ai_aspect_ratio = self.extract_ai_response_data(from_response, input_text)

                logger.debug(f"解析后的画图prompt：{ai_image_prompt}")
                logger.debug(f"解析后的尺寸：{ai_aspect_ratio}")

                return ai_image_prompt, ai_aspect_ratio

        except Exception as e:
            logger.error_status(f"提示词优化异常: {e}")

        # 优化失败或不可用，使用原始提示词
        logger.warning_status("提示词优化失败，使用原始提示词")
        return input_text, "9:16"

    def _is_selfie_request(self, input_text: str) -> bool:
        """检测是否是自拍照请求"""
        selfie_keywords = [
            "自拍", "自拍照", "你自己的照片", "你的照片", "你的样子",
            "你的形象", "给我画你的样子", "画你", "你的肖像", "你的头像",
            "林嫣然", "嫣然", "你自己"
        ]

        input_lower = input_text.lower()
        for keyword in selfie_keywords:
            if keyword in input_lower:
                logger.debug(f"检测到自拍照关键词: {keyword}")
                return True
        return False

    def _get_character_appearance(self) -> str:
        """获取林嫣然的角色特征描述"""
        try:
            import os
            import json

            # 读取角色特征配置文件
            config_path = os.path.join(PROJECT_ROOT, "config", "digital_life_features.json")
            if not os.path.exists(config_path):
                logger.warning_status("未找到角色特征配置文件")
                return ""

            with open(config_path, 'r', encoding='utf-8') as f:
                features = json.load(f)

            # 提取外观描述
            appearance = features.get("appearance", {})
            if not appearance:
                logger.warning_status("角色特征配置中未找到外观描述")
                return ""

            # 构建详细的外观描述
            description_parts = []

            # 基本描述
            if "description" in appearance:
                description_parts.append(f"整体外观：{appearance['description']}")

            # 身高体型
            if "height" in appearance:
                description_parts.append(f"身高：{appearance['height']}")
            if "body_shape" in appearance:
                description_parts.append(f"身材：{appearance['body_shape']}")

            # 面部特征
            face = appearance.get("face", {})
            if face:
                face_parts = []
                if "eyes" in face:
                    face_parts.append(f"眼睛：{face['eyes']}")
                if "nose" in face:
                    face_parts.append(f"鼻子：{face['nose']}")
                if "mouth" in face:
                    face_parts.append(f"嘴唇：{face['mouth']}")
                if "face_shape" in face:
                    face_parts.append(f"脸型：{face['face_shape']}")
                if face_parts:
                    description_parts.append(f"面部特征：{', '.join(face_parts)}")

            # 发型
            hair = appearance.get("hair", {})
            if hair:
                hair_parts = []
                if "style" in hair:
                    hair_parts.append(f"发型：{hair['style']}")
                if "color" in hair:
                    hair_parts.append(f"发色：{hair['color']}")
                if "bangs" in hair:
                    hair_parts.append(f"刘海：{hair['bangs']}")
                if hair_parts:
                    description_parts.append(f"头发：{', '.join(hair_parts)}")

            # 穿衣风格
            if "clothing_style" in appearance:
                description_parts.append(f"穿衣风格：{appearance['clothing_style']}")

            character_description = "\n".join(description_parts)
            logger.debug("成功获取林嫣然角色特征描述")
            return character_description

        except Exception as e:
            logger.error_status(f"获取角色特征描述失败: {e}")
            return ""

    def _get_image_dimensions(self, ai_aspect_ratio):
        """🔥 老王修复：使用官方4K标准尺寸，与后端API完全兼容"""

        # 🎯 官方4K标准尺寸映射 - 与后端API保持一致
        if "21:9" in ai_aspect_ratio:
            return 6197, 2656, 21, 9    # 4K超宽屏 (ratio_type: 8)
        elif "16:9" in ai_aspect_ratio:
            return 5404, 3040, 16, 9    # 4K宽屏 (ratio_type: 3)
        elif "3:2" in ai_aspect_ratio:
            return 4992, 3328, 3, 2     # 4K照片横屏 (ratio_type: 7)
        elif "4:3" in ai_aspect_ratio:
            return 4693, 3520, 4, 3     # 4K传统屏幕 (ratio_type: 4)
        elif "1:1" in ai_aspect_ratio:
            return 4096, 4096, 1, 1     # 4K正方形 (ratio_type: 1)
        elif "3:4" in ai_aspect_ratio:
            return 3520, 4693, 3, 4     # 4K竖屏 (ratio_type: 2)
        elif "2:3" in ai_aspect_ratio:
            return 3328, 4992, 2, 3     # 4K照片竖屏 (ratio_type: 6)
        elif "9:16" in ai_aspect_ratio:
            return 3040, 5404, 9, 16    # 4K手机竖屏 (ratio_type: 5)
        elif "智能" in ai_aspect_ratio or "auto" in ai_aspect_ratio.lower():
            return 4096, 4096, 1, 1     # 智能模式默认4K正方形
        else:
            return 3040, 5404, 9, 16    # 默认4K手机竖屏

    def _try_jimeng_service(self, ai_image_prompt, image_width, image_height):
        """尝试即梦服务"""
        try:
            # 🔥 老王修复：从配置文件获取模型版本
            jimeng_config = self.config.get("jimeng", {})
            model_version = jimeng_config.get("model", "jimeng-4.0")

            result = self.jimeng_generate_images(
                prompt=ai_image_prompt,
                model=model_version,
                negative_prompt="低质量，模糊画面，混乱布局，扭曲的场景，畸形的肢体，不协调的颜色，不真实",
                width=image_width,
                height=image_height,
                sample_strength=0.8,
                session_id=self.jimeng_session_id
            )

            # 🔥 老王优化：简化jimeng错误处理（API已稳定）
            if isinstance(result, dict):
                # 检查基本错误
                if result.get('code') != 0 and result.get('code') is not None:
                    error_msg = result.get('message', '未知错误')
                    logger.warning(f"⚠️ jimeng API错误: {error_msg}")
                    return {
                        "success": False,
                        "message": f"即梦服务错误: {error_msg}",
                        "result": None
                    }

                # 检查成功结果
                if "image_urls" in result and result['image_urls']:
                    logger.success(f"✅ 即梦4.0生成成功！")
                    logger.debug(f"即梦创建时间: {result['created']}")
                    logger.debug(f"即梦生成了 {len(result['image_urls'])} 张图像")

                    self.dro_internet = result['image_urls'][0]
                    return {
                        "success": True,
                        "message": "绘画成功（即梦4.0服务）",
                        "result": {
                            "image_url": self.dro_internet,
                            "service": "jimeng-4.0",
                            "prompt": ai_image_prompt
                        }
                    }
                else:
                    error_msg = result.get('error', result.get('message', 'Unknown error'))
                    error_details = result.get('details', {})

                    # 🔥 老王修复：详细记录即梦4.0失败信息
                    logger.error_status(f"❌ 即梦4.0生成失败: {error_msg}")

                    if isinstance(error_details, dict):
                        if 'possible_causes' in error_details:
                            logger.warning("🔍 可能原因:")
                            for cause in error_details['possible_causes']:
                                logger.warning(f"   - {cause}")

                        if 'suggestions' in error_details:
                            logger.info("💡 建议解决方案:")
                            for suggestion in error_details['suggestions']:
                                logger.info(f"   - {suggestion}")

                    return {
                        "success": False,
                        "message": f"即梦4.0服务失败: {error_msg}",
                        "result": None
                    }
            else:
                logger.warning(f"即梦返回非字典格式: {type(result)}")
                return {
                    "success": False,
                    "message": "即梦服务返回格式异常",
                    "result": None
                }

        except Exception as e:
            logger.error_status(f"即梦绘画异常: {e}")
            return {
                "success": False,
                "message": f"即梦服务异常: {e}",
                "result": None
            }

    def _try_keling_service(self, prompt: str, width: int, height: int) -> Dict[str, Any]:
        """尝试可灵绘画服务"""
        try:
            # 🔥 老王修复：改进可灵AI初始化和错误处理
            logger.debug(f"尝试可灵绘画服务: {prompt[:50]}...")

            # 检查可灵服务是否开启
            if self.is_keling == "0":
                logger.debug("可灵服务未开启，跳过")
                return {"success": False, "message": "可灵服务未开启"}

            # 尝试导入可灵模块
            try:
                # 检查keling目录是否存在
                keling_path = os.path.join(PROJECT_ROOT, "keling")
                if not os.path.exists(keling_path):
                    logger.warning_status("可灵模块目录不存在")
                    return {"success": False, "message": "可灵模块目录不存在"}

                # 检查配置文件
                config_path = os.path.join(keling_path, "config.json")
                if not os.path.exists(config_path):
                    logger.warning_status("可灵配置文件不存在")
                    return {"success": False, "message": "可灵配置文件不存在"}

                # 读取配置
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                kling_cookie = config.get("kling_cookie", "")
                if not kling_cookie:
                    logger.warning_status("可灵cookie配置为空")
                    return {"success": False, "message": "可灵cookie配置为空"}

                # 添加keling路径到sys.path
                if keling_path not in sys.path:
                    sys.path.append(keling_path)

                # 🔥 老王修复：改进可灵模块导入，使用绝对导入避免相对导入问题
                try:
                    # 尝试绝对导入
                    import importlib.util
                    kling_pic_path = os.path.join(keling_path, "kling_pic.py")

                    if os.path.exists(kling_pic_path):
                        # 使用importlib动态导入
                        spec = importlib.util.spec_from_file_location("kling_pic", kling_pic_path)
                        kling_pic_module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(kling_pic_module)

                        KlingPicGenerator = kling_pic_module.KlingPicGenerator
                        logger.success("可灵模块动态导入成功")
                    else:
                        # 尝试传统导入方式
                        from kling_pic import KlingPicGenerator
                        logger.success("可灵模块传统导入成功")

                except ImportError as e:
                    logger.error_status(f"可灵模块导入失败: {e}")
                    return {"success": False, "message": f"可灵模块导入失败: {e}"}
                except Exception as e:
                    logger.error_status(f"可灵模块动态导入失败: {e}")
                    return {"success": False, "message": f"可灵模块动态导入失败: {e}"}

                # 创建可灵生成器
                generator = KlingPicGenerator(kling_cookie)

                # 调用生成图片
                logger.debug(f"调用可灵生成图片: {prompt}, 尺寸: {width}x{height}")
                result = generator.generate_image(prompt, width, height)

                # 🔥 老王修复：增强可灵返回结果的处理逻辑，防止NoneType错误
                logger.debug(f"可灵原始返回结果: {result}, 类型: {type(result)}")

                # 处理None返回值
                if result is None:
                    logger.warning_status("可灵返回None结果")
                    return {"success": False, "message": "可灵返回None结果"}

                # 处理字典格式返回值（单个结果）
                if isinstance(result, dict):
                    if 'url' in result and result['url']:
                        image_url = result['url']
                        logger.success(f"可灵绘画成功(字典格式): {image_url}")
                        return {
                            "success": True,
                            "image_url": image_url,
                            "result": image_url,
                            "message": "可灵绘画完成"
                        }
                    else:
                        logger.warning_status(f"可灵返回字典格式异常: {result}")
                        return {"success": False, "message": "可灵返回字典格式异常"}

                # 处理列表格式返回值
                if isinstance(result, list) and len(result) > 0:
                    # 检查结果格式
                    image_url = None
                    first_item = result[0]

                    if isinstance(first_item, str) and first_item:
                        image_url = first_item
                    elif isinstance(first_item, dict) and 'url' in first_item and first_item['url']:
                        image_url = first_item['url']

                    if image_url:
                        logger.success(f"可灵绘画成功(列表格式): {image_url}")
                        return {
                            "success": True,
                            "image_url": image_url,
                            "result": image_url,
                            "message": "可灵绘画完成"
                        }
                    else:
                        logger.warning_status(f"可灵返回列表格式异常: {result}")
                        return {"success": False, "message": "可灵返回列表格式异常"}
                else:
                    logger.warning_status(f"可灵返回空结果或格式不支持: {result}")
                    return {"success": False, "message": "可灵返回空结果或格式不支持"}

            except Exception as import_error:
                logger.error_status(f"可灵模块处理异常: {import_error}")
                return {"success": False, "message": f"可灵模块处理异常: {import_error}"}

        except Exception as e:
            logger.error_status(f"可灵绘画服务异常: {e}")
            return {"success": False, "message": f"可灵绘画服务异常: {e}"}

    def _try_minimax_service(self, ai_image_prompt, ai_aspect_ratio):
        """尝试MiniMax服务"""
        try:
            # 检查MiniMax是否启用
            config = Config()
            logger.success(
                f"MiniMax配置加载成功: enabled={config.minimax_enabled}, api_key={'已配置' if config.voice_key else '未配置'}")

            if not config.minimax_enabled or not config.voice_key:
                return {
                    "success": False,
                    "message": "MiniMax服务未启用或API密钥未配置",
                    "result": None
                }

            minimax_result = self.Create_Image_MiniMax(ai_image_prompt, ai_aspect_ratio)
            logger.debug(f"MiniMax返回结果: {minimax_result}")

            # 检查MiniMax返回格式 - 修复格式判断
            if (minimax_result and
                    isinstance(minimax_result, dict) and
                    "data" in minimax_result and
                    isinstance(minimax_result["data"], dict) and
                    "image_urls" in minimax_result["data"] and
                    isinstance(minimax_result["data"]["image_urls"], list) and
                    len(minimax_result["data"]["image_urls"]) > 0):

                # 提取第一张图片的URL
                image_url = minimax_result["data"]["image_urls"][0]
                if image_url:
                    self.dro_internet = image_url
                    logger.success(f"MiniMax绘画成功，图片URL: {image_url}")
                    return {
                        "success": True,
                        "message": "绘画成功（MiniMax服务）",
                        "result": {
                            "image_url": self.dro_internet,
                            "service": "minimax",
                            "prompt": ai_image_prompt
                        }
                    }

            # 检查是否有错误信息
            if isinstance(minimax_result, dict) and "error" in minimax_result:
                error_msg = minimax_result['error']
                logger.error_status(f"MiniMax API错误: {error_msg}")
                return {
                    "success": False,
                    "message": f"MiniMax服务错误: {error_msg}",
                    "result": None
                }
            else:
                logger.error_status(f"MiniMax返回格式异常: {minimax_result}")
                return {
                    "success": False,
                    "message": "MiniMax服务返回格式异常",
                    "result": None
                }

        except Exception as e:
            logger.error_status(f"MiniMax绘画异常: {e}")
            return {
                "success": False,
                "message": f"MiniMax服务异常: {e}",
                "result": None
            }

    def Create_Image_MiniMax(self, ai_image_prompt, ai_aspect_ratio):
        """MiniMax绘画服务"""
        try:
            api_key = Config().voice_key
            if not api_key:
                logger.error_status("MiniMax API密钥未配置")
                return {
                    "error": "API密钥未配置",
                    "details": "请检查配置文件中的minimax.api_key"
                }

            # 确保提示词不为空
            if not ai_image_prompt or ai_image_prompt.strip() == "":
                logger.error_status("MiniMax提示词为空")
                return {
                    "error": "提示词为空",
                    "details": f"ai_image_prompt: '{ai_image_prompt}'"
                }

            url = "https://api.minimax.chat/v1/image_generation"
            payload = json.dumps({
                "model": "image-01",
                "prompt": ai_image_prompt.strip(),  # 确保去除空白字符
                "aspect_ratio": ai_aspect_ratio,
                "response_format": "url",
                "n": 1,
                "prompt_optimizer": True
            })
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }

            logger.debug(f"MiniMax请求URL: {url}")
            logger.debug(f"MiniMax请求头: {headers}")
            logger.debug(f"MiniMax请求体: {payload}")
            logger.debug(f"MiniMax提示词: '{ai_image_prompt}'")
            logger.debug(f"MiniMax比例: '{ai_aspect_ratio}'")

            response = requests.request("POST", url, headers=headers, data=payload, timeout=600)

            logger.debug(f"MiniMax响应状态: {response.status_code}")
            logger.debug(f"MiniMax响应内容: {response.text[:500]}")

            result = response.json()

            # 检查API返回的错误信息
            if "base_resp" in result and result["base_resp"].get("status_code") != 0:
                error_msg = result["base_resp"].get("status_msg", "Unknown error")
                logger.error_status(f"MiniMax API错误: {error_msg}")
                return {
                    "error": f"MiniMax API错误: {error_msg}",
                    "details": result
                }

            return result

        except requests.exceptions.Timeout:
            logger.error_status("MiniMax API请求超时")
            return {
                "error": "API请求超时",
                "details": "请求超过600秒未响应"
            }
        except requests.exceptions.RequestException as e:
            logger.error_status(f"MiniMax网络请求异常: {e}")
            return {
                "error": f"网络请求异常: {e}",
                "details": str(e)
            }
        except Exception as e:
            logger.error_status(f"MiniMax API调用异常: {e}")
            return {
                "error": f"API调用异常: {e}",
                "details": str(e)
            }

    def jimeng_generate_images(self, prompt, model="jimeng-4.0", negative_prompt="", width=1440, height=2560,
                               sample_strength=0.8,
                               session_id=None):
        """
        调用即梦图像生成API - 🔥 老王修复：按照最新API规范重写

        参数:
        - prompt: 提示词，必填。jimeng-4.0 支持多图生成
        - model: 模型名称，默认为"jimeng-4.0"（支持模型：jimeng-4.0）
        - negative_prompt: 反向提示词，默认空字符串
        - width: 图像宽度，默认1440
        - height: 图像高度，默认2560
        - sample_strength: 精细度，取值范围0-1，默认0.8
        - session_id: 会话ID，支持多个sessionid用逗号分隔

        返回:
        - 标准格式响应: {"created": timestamp, "image_urls": [url1, url2, ...]}
        """

        # 🔥 老王修复：获取多个session_id
        if session_id is None:
            session_id = self.jimeng_session_id

        # 从配置文件获取session_id（支持多个）
        jimeng_config = self.config.get("jimeng", {})
        session_ids_str = jimeng_config.get("session_id", session_id)

        # 🔥 老王修复：按照最新API规范构造请求
        url = "http://124.221.30.195:47653/v1/images/generations"

        # 多个sessionid用逗号分隔，服务会自动选择可用的
        headers = {
            "Authorization": f"Bearer {session_ids_str}",
            "Content-Type": "application/json"
        }

        # 🔥 老王修复：使用完整参数，与后端API官方4K标准尺寸兼容
        data = {
            "model": model,  # 支持模型：jimeng-4.0
            "prompt": prompt,  # 提示词，必填
            "width": width,   # 图像宽度 - 后端会自动映射到官方4K标准
            "height": height, # 图像高度 - 后端会自动映射到官方4K标准
            "sample_strength": sample_strength,  # 精细度
            "response_format": "url"  # 响应格式
        }

        # 🔥 老王修复：可选参数只在非空时添加
        if negative_prompt and negative_prompt.strip():
            data["negative_prompt"] = negative_prompt

        logger.info(f"🎨 调用jimeng API生成图片...")
        logger.debug(f"🔑 Session IDs: {session_ids_str[:50]}...")
        logger.debug(f"📝 提示词: {prompt[:100]}...")

        try:
            # 发送POST请求
            response = requests.post(url, headers=headers, json=data, timeout=300)

            logger.debug(f"📡 API响应状态: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                logger.debug(f"📋 API响应内容: {str(result)[:300]}...")

                # 检查返回数据格式
                if not result or not isinstance(result, dict):
                    logger.error(f"❌ API返回数据格式异常: {result}")
                    return {
                        "error": "API返回数据格式异常",
                        "details": f"返回内容: {result}"
                    }

                # 🔥 老王修复：检查登录错误和其他API错误
                if "code" in result and result.get("code") != 0:
                    error_code = result.get("code")
                    error_msg = result.get("message", "未知错误")

                    if error_code == -2001 and "check login error" in error_msg:
                        logger.error(f"❌ jimeng登录验证失败，session_id可能已过期")
                        logger.info(f"当前session_id: {session_ids_str[:50]}...")
                        return {
                            "error": "登录验证失败，session_id可能已过期",
                            "details": f"错误码: {error_code}, 错误信息: {error_msg}",
                            "code": error_code
                        }
                    else:
                        logger.error(f"❌ jimeng API错误: {error_msg} (代码: {error_code})")
                        return {
                            "error": f"API错误: {error_msg}",
                            "details": result,
                            "code": error_code
                        }

                # 检查是否有其他错误信息
                if "error" in result:
                    error_msg = result.get("error", "未知错误")
                    logger.error(f"❌ API返回错误: {error_msg}")
                    return {
                        "error": f"API错误: {error_msg}",
                        "details": result
                    }

                # 检查data字段
                data_list = result.get("data", [])
                created_time = result.get("created", int(time.time()))

                if data_list and isinstance(data_list, list) and len(data_list) > 0:
                    # 提取图片URL
                    image_urls = []
                    for item in data_list:
                        if isinstance(item, dict) and "url" in item:
                            image_urls.append(item["url"])

                    if image_urls:
                        logger.success(f"✅ jimeng生成成功！获得 {len(image_urls)} 张图片")
                        return {
                            "created": created_time,
                            "image_urls": image_urls
                        }
                    else:
                        logger.error(f"❌ 无法从返回数据中提取图片URL: {data_list}")
                        return {
                            "error": "无法提取图片URL",
                            "details": f"data内容: {data_list}"
                        }
                else:
                    # 🔥 老王修复：data为空说明这个API可能不支持异步，或者需要等待更长时间
                    if created_time and isinstance(data_list, list):
                        logger.warning(f"⚠️  API返回空数据，可能是以下原因之一：")
                        logger.warning(f"   1. API不支持异步生成")
                        logger.warning(f"   2. 生成失败但未返回错误信息")
                        logger.warning(f"   3. 需要更长的等待时间")
                        logger.warning(f"   4. session_id权限不足")

                        # 🔥 老王修复：不再进行轮询，因为每次轮询都会提交新任务
                        # 直接返回错误，让上层决定是否重试
                        logger.error(f"❌ jimeng API返回空数据，任务ID: {created_time}")
                        return {
                            "error": "API返回空数据，可能生成失败",
                            "details": f"任务ID: {created_time}, 建议检查session_id权限或稍后重试",
                            "created": created_time
                        }
                    else:
                        logger.error(f"❌ API返回的data字段为空或格式错误: {data_list}")
                        return {
                            "error": "API返回的data字段为空或格式错误",
                            "details": f"data内容: {data_list}, 完整响应: {result}"
                        }
            else:
                error_text = response.text[:500]
                logger.error(f"❌ HTTP错误 {response.status_code}: {error_text}")
                return {
                    "error": f"HTTP错误 {response.status_code}",
                    "details": error_text
                }

        except requests.exceptions.Timeout:
            logger.error(f"❌ API请求超时")
            return {
                "error": "API请求超时",
                "details": "请求超过120秒未响应"
            }
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 网络请求异常: {e}")
            return {
                "error": f"网络请求异常: {e}",
                "details": str(e)
            }
        except Exception as e:
            logger.error(f"❌ jimeng API调用异常: {e}")
            return {
                "error": f"API调用异常: {e}",
                "details": str(e)
            }



    def keling_generate_image(self, prompt, width=9, height=16):
        """可灵绘画服务"""
        try:
            # 导入可灵模块
            from keling.kling_pic import keling_generate_image

            logger.debug(f"调用可灵服务，提示词: {prompt}, 尺寸: {width}x{height}")
            result = keling_generate_image(prompt, width, height)
            logger.debug(f"可灵服务返回结果: {result}, 类型: {type(result)}")

            # 🔥 老王修复：增强可灵返回结果处理，防止None错误
            if result is None:
                logger.warning_status("可灵服务返回None")
                return []

            # 处理字典格式返回值
            if isinstance(result, dict):
                if 'url' in result and result['url']:
                    return [result['url']]
                else:
                    logger.warning_status(f"可灵返回字典格式异常: {result}")
                    return []

            # 处理列表格式返回值
            if isinstance(result, list) and len(result) > 0:
                # 如果返回的是URL列表
                if isinstance(result[0], str) and result[0]:
                    return result
                # 如果返回的是字典格式
                elif isinstance(result[0], dict) and 'url' in result[0] and result[0]['url']:
                    return [result[0]['url']]
                else:
                    logger.warning_status(f"可灵返回列表格式异常: {result}")
                    return []

            # 处理字符串格式返回值
            if isinstance(result, str) and result:
                return [result]

            logger.warning_status(f"可灵服务返回空结果或格式不支持: {result}")
            return []

        except ImportError as e:
            logger.error_status(f"可灵模块导入失败: {e}")
            return []
        except Exception as e:
            logger.error_status(f"可灵绘画服务异常: {e}")
            return []

    def extract_ai_response_data(self, response_text, original_prompt):
        """从AI响应中提取提示词和比例"""
        try:
            ai_image_prompt = original_prompt
            ai_aspect_ratio = "9:16"

            # 提取 image_prompt（原逻辑已支持跨行）
            image_prompt_match = re.search(
                r'image_prompt:(.*?)(?=aspect_ratio:|$)',
                response_text,
                re.DOTALL
            )
            if image_prompt_match:
                ai_image_prompt = image_prompt_match.group(1).strip()

            # 改进后的 aspect_ratio 提取（支持空白符）
            aspect_ratio_match = re.search(
                r'aspect_ratio:\s*([0-9]+:[0-9]+)',  # 关键修改点
                response_text,
                re.DOTALL  # 确保跨行匹配
            )
            if aspect_ratio_match:
                ai_aspect_ratio = aspect_ratio_match.group(1).strip()

            # 如果没有找到格式化的内容，尝试其他解析方式
            if ai_image_prompt == original_prompt:
                lines = response_text.split('\n')
                for line in lines:
                    if "画面描述：" in line or "画面描述:" in line:
                        ai_image_prompt = line.split("：")[-1].split(":")[-1].strip()
                    elif "推荐比例：" in line or "推荐比例:" in line:
                        ai_aspect_ratio = line.split("：")[-1].split(":")[-1].strip()

            # 如果还是没有找到有效的提示词，直接使用原始输入
            if not ai_image_prompt or ai_image_prompt == original_prompt:
                ai_image_prompt = original_prompt

            return ai_image_prompt, ai_aspect_ratio
        except Exception as e:
            logger.error_status(f"解析AI响应异常: {e}")
            return original_prompt, "9:16"


# 单例模式
_instance = None


def get_instance(module_config=None) -> DrawingSkill:
    """
    获取绘画技能实例（单例模式）

    Args:
        module_config: 模块配置，为了兼容性而添加，但不使用

    Returns:
        DrawingSkill: 绘画技能实例
    """
    global _instance
    if _instance is None:
        _instance = DrawingSkill()
    return _instance