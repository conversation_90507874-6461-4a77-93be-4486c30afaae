#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试简单宽高比 - 🔥 老王快速验证
"""

import requests
import json
import time

def test_simple_ratio():
    """测试简单宽高比"""
    print("🔥 老王测试简单宽高比")
    print("=" * 50)
    
    api_url = "http://127.0.0.1:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    # 测试正方形
    data = {
        "model": "jimeng-4.0",
        "prompt": "beautiful square garden",
        "width": 1024,
        "height": 1024,
        "sample_strength": 0.5
    }
    
    print(f"📝 提示词: {data['prompt']}")
    print(f"📐 尺寸: {data['width']}x{data['height']}")
    
    try:
        start_time = time.time()
        print(f"🚀 发送请求...")
        
        response = requests.post(api_url, headers=headers, json=data, timeout=300)
        elapsed_time = time.time() - start_time
        
        print(f"⏱️  请求耗时: {elapsed_time:.2f}秒")
        print(f"📡 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📋 完整响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if "data" in result and result["data"]:
                data_list = result["data"]
                print(f"✅ 成功生成 {len(data_list)} 张图片！")
                for j, item in enumerate(data_list):
                    if isinstance(item, dict) and "url" in item:
                        print(f"   图片 {j+1}: {item['url'][:80]}...")
                return True
            else:
                print(f"⚠️  数据为空")
                
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   错误详情: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
            except:
                print(f"   响应文本: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return False

def main():
    """主函数"""
    print("🔥🔥🔥 老王的简单比例测试 🔥🔥🔥")
    print("=" * 60)
    
    result = test_simple_ratio()
    
    if result:
        print(f"\n🎉 简单比例测试成功！")
    else:
        print(f"\n😤 简单比例测试失败，需要继续调试")

if __name__ == "__main__":
    main()
