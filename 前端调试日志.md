调试日志1
https://jimeng.jianying.com/mweb/v1/aigc_draft/generate?aid=513695&device_platform=web&region=cn&webId=7512668659051988516&da_version=3.3.2&web_component_open_flag=1&web_version=7.5.0&aigc_features=app_lip_sync&msToken=jBNIndNImGAXKMNwwLeEup8gBt4ODWBlYE9fYR2cG9_fEUJkGs3xaS4BDeHGLQTpB4RCuR17T8YCrMS1yNlj7nPs1QmrQfBSG_Qdn15R9_Om15gp_N2TkI-Yd4fDqho%3D&a_bogus=YyMOhcZpMsm1X-Jd%2Fhkz9S5IyGm0YW5zgZENLf2Z9Uqi

Response如下
{
    "ret": "0",
    "errmsg": "success",
    "systime": "1758174364",
    "logid": "20250918134604F44C63501CD07015A183",
    "data": {
        "aigc_data": {
            "generate_type": 1,
            "history_record_id": "25970715096066",
            "origin_history_record_id": null,
            "created_time": 1758174364.318,
            "item_list": [],
            "origin_item_list": [],
            "task": {
                "task_id": "25970715096066",
                "submit_id": "28de960e-c471-4f5d-aabc-6d229955058e",
                "aid": 0,
                "status": 42,
                "finish_time": 0,
                "history_id": "25970715096066",
                "task_payload": null,
                "original_input": null,
                "req_first_frame_image": null,
                "ai_gen_prompt": "",
                "priority": 0,
                "lip_sync_info": null,
                "multi_size_first_frame_image": null,
                "multi_size_end_frame_image": null,
                "process_flows": null,
                "create_time": 0,
                "aigc_image_params": null,
                "ref_item": null,
                "resp_ret": {
                    "ret": ""
                }
            },
            "mode": "workbench",
            "asset_option": {
                "has_favorited": false
            },
            "uid": "317079856886999",
            "aigc_flow": {
                "version": "3.0.2"
            },
            "status": 42,
            "history_group_key_md5": "8e991659af057b79c9057d1fee2ffda4",
            "history_group_key": "扎着高马尾的女孩的照片，嘴唇咬着黑色皮筋，青春靓丽，俏皮可爱",
            "draft_content": "{\"type\":\"draft\",\"id\":\"9bcf479b-7009-48ca-4c4f-dfe6b30b6d23\",\"min_version\":\"3.0.2\",\"min_features\":[],\"is_from_tsn\":true,\"version\":\"3.0.2\",\"main_component_id\":\"38418ba4-92a8-a28e-e9d5-fbac0287ecd1\",\"component_list\":[{\"type\":\"image_base_component\",\"id\":\"38418ba4-92a8-a28e-e9d5-fbac0287ecd1\",\"min_version\":\"3.0.2\",\"aigc_mode\":\"workbench\",\"gen_type\":1,\"metadata\":{\"type\":\"\",\"id\":\"91950438-c476-2792-81e8-dcd83a00dc12\",\"created_platform\":3,\"created_platform_version\":\"\",\"created_time_in_ms\":\"1758174363600\",\"created_did\":\"\"},\"generate_type\":\"generate\",\"abilities\":{\"type\":\"\",\"id\":\"a3cf9a00-7a65-f5e5-50c2-66d214f05fb9\",\"generate\":{\"type\":\"\",\"id\":\"24ce9c0a-cfeb-1b6c-feb0-9178de5a7b92\",\"core_param\":{\"type\":\"\",\"id\":\"32d9acf6-78bc-a20e-680c-9c83681a24f3\",\"model\":\"high_aes_general_v40\",\"prompt\":\"扎着高马尾的女孩的照片，嘴唇咬着黑色皮筋，青春靓丽，俏皮可爱\",\"negative_prompt\":\"\",\"seed\":437044651,\"sample_strength\":0.5,\"image_ratio\":5,\"large_image_info\":{\"type\":\"\",\"id\":\"20f1153f-72e4-4075-49fc-e5ca90cda20e\",\"height\":2560,\"width\":1440,\"resolution_type\":\"2k\"},\"intelligent_ratio\":false}}}}]}",
            "submit_id": "28de960e-c471-4f5d-aabc-6d229955058e",
            "capflow_id": "28de960e-c471-4f5d-aabc-6d229955058e",
            "metrics_extra": "{\"promptSource\":\"custom\",\"generateCount\":1,\"enterFrom\":\"click\",\"generateId\":\"28de960e-c471-4f5d-aabc-6d229955058e\",\"isRegenerate\":false}",
            "generate_id": "20250918134604F44C63501CD07015A183",
            "finish_time": 0,
            "model_info": {
                "icon_url": "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/model4.png~tplv-tb4s082cfz-resize:300:300.webp?lk3s=8e790bc3\u0026x-expires=1789710364\u0026x-signature=%2FgKOaz1suRbJnkVio1JUrAiJ7%2Bo%3D",
                "model_name_starling_key": "dreamina_image4_title",
                "model_tip_starling_key": "dreamina_image4_desc",
                "model_req_key": "high_aes_general_v40",
                "model_name": "图片 4.0",
                "video_model_options": null
            },
            "forecast_generate_cost": 17,
            "forecast_queue_cost": 0,
            "fail_starling_key": "",
            "fail_starling_message": "",
            "min_feats": null,
            "queue_info": {
                "queue_idx": 0,
                "priority": 5,
                "queue_status": 2,
                "queue_length": 0,
                "polling_config": {
                    "interval_seconds": 30,
                    "timeout_seconds": 86400
                },
                "priority_queue_display_threshold": {
                    "vip_queuing_time_threshold": 300,
                    "waiting_time_threshold": 60
                }
            },
            "agent_history_id": null,
            "total_image_count": 0,
            "finished_image_count": 0,
            "confirm_status": 0,
            "confirm_token": "",
            "image_type": 0
        },
        "fail_code": "",
        "fail_starling_key": "",
        "fail_starling_message": ""
    }
}


调试日志2
https://jimeng.jianying.com/mweb/v1/get_history_by_ids?aid=513695&device_platform=web&region=cn&webId=7512668659051988516&da_version=3.3.2&web_version=7.5.0&aigc_features=app_lip_sync&msToken=jBNIndNImGAXKMNwwLeEup8gBt4ODWBlYE9fYR2cG9_fEUJkGs3xaS4BDeHGLQTpB4RCuR17T8YCrMS1yNlj7nPs1QmrQfBSG_Qdn15R9_Om15gp_N2TkI-Yd4fDqho%3D&a_bogus=x7MmvOZpMsm1ZvGd%2Fhkz9nTzyCg0YW5YgZENLfZyHULU

{
    "ret": "0",
    "errmsg": "success",
    "systime": "1758174367",
    "logid": "20250918134607D76238DFE98D805307E7",
    "data": {
        "28de960e-c471-4f5d-aabc-6d229955058e": {
            "generate_type": 1,
            "history_record_id": "25970715096066",
            "origin_history_record_id": null,
            "created_time": 1758174364.318,
            "item_list": [],
            "origin_item_list": [],
            "task": {
                "task_id": "25970715096066",
                "submit_id": "28de960e-c471-4f5d-aabc-6d229955058e",
                "aid": 0,
                "status": 45,
                "finish_time": 0,
                "history_id": "25970715096066",
                "task_payload": null,
                "original_input": null,
                "req_first_frame_image": null,
                "ai_gen_prompt": "",
                "priority": 0,
                "lip_sync_info": null,
                "multi_size_first_frame_image": null,
                "multi_size_end_frame_image": null,
                "process_flows": null,
                "create_time": 0,
                "aigc_image_params": null,
                "ref_item": null,
                "resp_ret": {
                    "ret": ""
                }
            },
            "mode": "workbench",
            "asset_option": {
                "has_favorited": false
            },
            "uid": "317079856886999",
            "aigc_flow": {
                "version": "3.0.2"
            },
            "status": 45,
            "history_group_key_md5": "8e991659af057b79c9057d1fee2ffda4",
            "history_group_key": "扎着高马尾的女孩的照片，嘴唇咬着黑色皮筋，青春靓丽，俏皮可爱",
            "draft_content": "{\"type\":\"draft\",\"id\":\"9bcf479b-7009-48ca-4c4f-dfe6b30b6d23\",\"min_version\":\"3.0.2\",\"min_features\":[],\"is_from_tsn\":true,\"version\":\"3.0.2\",\"main_component_id\":\"38418ba4-92a8-a28e-e9d5-fbac0287ecd1\",\"component_list\":[{\"type\":\"image_base_component\",\"id\":\"38418ba4-92a8-a28e-e9d5-fbac0287ecd1\",\"min_version\":\"3.0.2\",\"aigc_mode\":\"workbench\",\"gen_type\":1,\"metadata\":{\"type\":\"\",\"id\":\"91950438-c476-2792-81e8-dcd83a00dc12\",\"created_platform\":3,\"created_platform_version\":\"\",\"created_time_in_ms\":\"1758174363600\",\"created_did\":\"\"},\"generate_type\":\"generate\",\"abilities\":{\"type\":\"\",\"id\":\"a3cf9a00-7a65-f5e5-50c2-66d214f05fb9\",\"generate\":{\"type\":\"\",\"id\":\"24ce9c0a-cfeb-1b6c-feb0-9178de5a7b92\",\"core_param\":{\"type\":\"\",\"id\":\"32d9acf6-78bc-a20e-680c-9c83681a24f3\",\"model\":\"high_aes_general_v40\",\"prompt\":\"扎着高马尾的女孩的照片，嘴唇咬着黑色皮筋，青春靓丽，俏皮可爱\",\"negative_prompt\":\"\",\"seed\":437044651,\"sample_strength\":0.5,\"image_ratio\":5,\"large_image_info\":{\"type\":\"\",\"id\":\"20f1153f-72e4-4075-49fc-e5ca90cda20e\",\"height\":2560,\"width\":1440,\"resolution_type\":\"2k\"},\"intelligent_ratio\":false}}}}]}",
            "submit_id": "28de960e-c471-4f5d-aabc-6d229955058e",
            "capflow_id": "28de960e-c471-4f5d-aabc-6d229955058e",
            "metrics_extra": "{\"promptSource\":\"custom\",\"generateCount\":1,\"enterFrom\":\"click\",\"generateId\":\"28de960e-c471-4f5d-aabc-6d229955058e\",\"isRegenerate\":false}",
            "generate_id": "20250918134604F44C63501CD07015A183",
            "finish_time": 0,
            "model_info": {
                "icon_url": "https://p9-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/model4.png~tplv-tb4s082cfz-resize:300:300.webp?lk3s=8e790bc3\u0026x-expires=1789710367\u0026x-signature=VfyKPftl9NHURUdiBL3AvhWoXG8%3D",
                "model_name_starling_key": "dreamina_image4_title",
                "model_tip_starling_key": "dreamina_image4_desc",
                "model_req_key": "high_aes_general_v40",
                "model_name": "图片 4.0",
                "video_model_options": null
            },
            "forecast_generate_cost": 17,
            "forecast_queue_cost": 0,
            "fail_starling_key": "",
            "fail_starling_message": "",
            "min_feats": null,
            "queue_info": {
                "queue_idx": 0,
                "priority": 5,
                "queue_status": 2,
                "queue_length": 0,
                "polling_config": {
                    "interval_seconds": 30,
                    "timeout_seconds": 86400
                },
                "priority_queue_display_threshold": {
                    "vip_queuing_time_threshold": 300,
                    "waiting_time_threshold": 60
                }
            },
            "agent_history_id": null,
            "total_image_count": 4,
            "finished_image_count": 0,
            "confirm_status": 2,
            "confirm_token": "",
            "image_type": 3
        }
    }
}

{
    "ret": "0",
    "errmsg": "success",
    "systime": "1758174372",
    "logid": "2025091813461232BE1ECD90016FAD31ED",
    "data": {
        "28de960e-c471-4f5d-aabc-6d229955058e": {
            "generate_type": 1,
            "history_record_id": "25970715096066",
            "origin_history_record_id": null,
            "created_time": 1758174364.318,
            "item_list": [
                {
                    "common_attr": {
                        "id": "7551301412676701466",
                        "effect_id": "7551301412676701466",
                        "effect_type": 9,
                        "title": "",
                        "description": "扎着高马尾的女孩的照片，嘴唇咬着黑色皮筋，青春靓丽，俏皮可爱",
                        "cover_url": "https://p3-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/7b5fe2c53ed74492a669b623d7640fa7~tplv-tb4s082cfz-aigc_resize_mark:640:640.jpeg?lk3s=43402efa\u0026x-expires=1758178800\u0026x-signature=XVmbpjph2Gg0N%2BfVSUJT67B7xJg%3D\u0026format=.jpeg",
                        "item_urls": [
                            ""
                        ],
                        "md5": "",
                        "create_time": 1758174371,
                        "status": 144,
                        "review_info": {
                            "review_status": 1,
                            "review_code_list": []
                        },
                        "aspect_ratio": 0.5625,
                        "publish_source": "user_post_mweb_item",
                        "collection_ids": [],
                        "extra": "",
                        "has_published": false,
                        "cover_url_map": {
                            "1080": "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/7b5fe2c53ed74492a669b623d7640fa7~tplv-tb4s082cfz-aigc_resize_mark:1080:1080.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=fXNo860a12%2BP8lDOoHhtIOUsrbI%3D\u0026format=.webp",
                            "2400": "https://p9-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/7b5fe2c53ed74492a669b623d7640fa7~tplv-tb4s082cfz-aigc_resize_mark:2400:2400.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=xqkG5k9GlbDyWv2uSd05l43o0RQ%3D\u0026format=.webp",
                            "360": "https://p3-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/7b5fe2c53ed74492a669b623d7640fa7~tplv-tb4s082cfz-aigc_resize_mark:360:360.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=OnNhmZSo9jqyddFSQdGqvODVlkk%3D\u0026format=.webp",
                            "4096": "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/7b5fe2c53ed74492a669b623d7640fa7~tplv-tb4s082cfz-aigc_resize_mark:4096:4096.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=G%2Bwr2QCLfkDAG1QB5rwRsqVwgzw%3D\u0026format=.webp",
                            "480": "https://p9-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/7b5fe2c53ed74492a669b623d7640fa7~tplv-tb4s082cfz-aigc_resize_mark:480:480.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=Q2lzxP9QKW%2BRdUJtQzGZLu6Za9M%3D\u0026format=.webp",
                            "720": "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/7b5fe2c53ed74492a669b623d7640fa7~tplv-tb4s082cfz-aigc_resize_mark:720:720.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=x%2Bvbadm7wsyK9ZhGFK995YZGPtc%3D\u0026format=.webp"
                        },
                        "local_item_id": "7551301412676701466",
                        "web_extra": "{\"mweb_abtags\":[\"high_aes_general_v40s_stream\"]}",
                        "update_time": 0,
                        "cover_uri": "tos-cn-i-tb4s082cfz/7b5fe2c53ed74492a669b623d7640fa7",
                        "smart_crop_loc": null,
                        "cover_height": 640,
                        "cover_width": 360
                    },
                    "author": null,
                    "image": {
                        "format": "png",
                        "large_images": [
                            {
                                "image_uri": "tos-cn-i-tb4s082cfz/7b5fe2c53ed74492a669b623d7640fa7",
                                "image_url": "https://p9-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/7b5fe2c53ed74492a669b623d7640fa7~tplv-tb4s082cfz-aigc_resize_mark:0:0.png?lk3s=43402efa\u0026x-expires=1758178800\u0026x-signature=BlZSJ7CXOpy2hJnAf6B25lQr82s%3D\u0026format=.png",
                                "width": 1440,
                                "height": 2560,
                                "format": "png",
                                "smart_crop_loc": null
                            }
                        ]
                    },
                    "aigc_image_params": {
                        "generate_type": 1,
                        "first_generate_type": 1,
                        "text2video_params": null,
                        "text2image_params": {
                            "image_ratio": 5,
                            "model_config": {
                                "icon_url": "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/model4.png~tplv-tb4s082cfz-resize:300:300.webp?lk3s=8e790bc3\u0026x-expires=1789710371\u0026x-signature=3StxXJkt%2FqnyvJkypIxgg%2B25JHc%3D",
                                "model_name_starling_key": "dreamina_image4_title",
                                "model_tip_starling_key": "dreamina_image4_desc",
                                "model_req_key": "high_aes_general_v40",
                                "is_new_model": true,
                                "sample_steps": {
                                    "steps": 16,
                                    "min_steps": 10,
                                    "max_steps": 41
                                },
                                "blend_enable": {
                                    "face_swap": false,
                                    "bg_paint": false,
                                    "canny": false,
                                    "depth": false,
                                    "pose": false
                                },
                                "feats": [
                                    "new_model",
                                    "default_scene",
                                    "t2i",
                                    "byte_edit",
                                    "refuse_image",
                                    "smart_scale",
                                    "per_piece",
                                    "think_then_draw",
                                    "byte_edit_with_empty_prompt",
                                    "byte_edit_with_custom_ratio",
                                    "etta"
                                ],
                                "model_name": "图片 4.0",
                                "model_tip": "支持多参考图、系列组图生成",
                                "feature_key": "",
                                "generation_category_name_starling_key": "dreamina_app_modalswitch_image_4_0",
                                "generation_category_name": "图片・4.0",
                                "duration_option": null,
                                "lens_motion_type_option": null,
                                "motion_speed_option": null,
                                "camera_strength_option": null,
                                "video_aspect_ratio_option": null,
                                "fps": 0,
                                "feat_config": {
                                    "canny": {
                                        "strength": 0.6
                                    },
                                    "depth": {
                                        "strength": 0.6
                                    },
                                    "pose": {
                                        "strength": 0.6
                                    },
                                    "style_reference": {
                                        "strength": 0.8
                                    }
                                },
                                "extra": {
                                    "model_source": "by Seedream 4.0",
                                    "raw_model_source": "Seedream 4.0"
                                },
                                "feats_cant_combine": [],
                                "model_bg_prompt_starling_key": "",
                                "resolution_map": {
                                    "2k": {
                                        "image_ratio_sizes": [
                                            {
                                                "ratio_type": 1,
                                                "width": 2048,
                                                "height": 2048
                                            },
                                            {
                                                "ratio_type": 2,
                                                "width": 1728,
                                                "height": 2304
                                            },
                                            {
                                                "ratio_type": 3,
                                                "width": 2560,
                                                "height": 1440
                                            },
                                            {
                                                "ratio_type": 4,
                                                "width": 2304,
                                                "height": 1728
                                            },
                                            {
                                                "ratio_type": 5,
                                                "width": 1440,
                                                "height": 2560
                                            },
                                            {
                                                "ratio_type": 6,
                                                "width": 1664,
                                                "height": 2496
                                            },
                                            {
                                                "ratio_type": 7,
                                                "width": 2496,
                                                "height": 1664
                                            },
                                            {
                                                "ratio_type": 8,
                                                "width": 3024,
                                                "height": 1296
                                            }
                                        ],
                                        "image_range_config": {
                                            "min_length": 1296,
                                            "max_length": 3024,
                                            "max_pixel_num": 4194304
                                        },
                                        "resolution_name": "高清 2K"
                                    }
                                }
                            },
                            "prompt": "扎着高马尾的女孩的照片，嘴唇咬着黑色皮筋，青春靓丽，俏皮可爱",
                            "sample_steps": 25,
                            "seed": 437044651,
                            "user_negative_prompt": "",
                            "large_image_info": {
                                "height": 2560,
                                "width": 1440,
                                "resolution_type": "2k"
                            },
                            "sample_strength": 0.5
                        },
                        "template_id": "0",
                        "request_id": "68bb32a6e37ab9b960850355d997d96196d2777bbe8040a12e8afe61342c8415",
                        "generate_id": "20250918134604F44C63501CD07015A183",
                        "origin_request_id": "68bb32a6e37ab9b960850355d997d96196d2777bbe8040a12e8afe61342c8415",
                        "aigc_cnt_list": [
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0
                        ],
                        "aigc_mode": "workbench",
                        "insta_drag_params": {
                            "origin_item_id": null
                        },
                        "hide_ref_images": false,
                        "reference_prompt": "青春摄影风格，自然光，高饱和度，浅景深，特写镜头，扎着高马尾的亚洲女孩，嘴唇轻咬黑色皮筋，发丝微卷，眼神灵动，背景虚化，柔光处理，清新自然，俏皮可爱氛围。",
                        "image_type": 3
                    },
                    "statistic": {
                        "feedback_status": 0
                    },
                    "category_id_list": [],
                    "aigc_flow": {
                        "version": "3.0.2"
                    },
                    "aigc_draft": {
                        "version": "3.0.2",
                        "uri": "aigc-draft/7839431507714",
                        "content": "",
                        "update_time": 0,
                        "last_preview_time": 0,
                        "resource_type": "",
                        "public_uri": "",
                        "variables": null,
                        "resource_width": 0,
                        "resource_height": 0,
                        "node_keys": null,
                        "cost": 0
                    },
                    "gen_result_data": {
                        "result_code": 0,
                        "result_msg": "Success"
                    },
                    "extra": {
                        "template_type": "image",
                        "ai_feature": "text_generate_image"
                    },
                    "ai_feature": {
                        "features": [
                            {
                                "type": "text_generate_image"
                            }
                        ],
                        "is_merged": true
                    },
                    "sharing_info": {
                        "share_status": 2
                    },
                    "metadata_param": "{\"effect_id\":\"gen_image\",\"effect_type\":\"tool\"}",
                    "has_intention_info": false
                },
                {
                    "common_attr": {
                        "id": "7551301401561812274",
                        "effect_id": "7551301401561812274",
                        "effect_type": 9,
                        "title": "",
                        "description": "扎着高马尾的女孩的照片，嘴唇咬着黑色皮筋，青春靓丽，俏皮可爱",
                        "cover_url": "https://p9-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/df04663e5bf54b81ae0c4c381a2e04c9~tplv-tb4s082cfz-aigc_resize_mark:640:640.jpeg?lk3s=43402efa\u0026x-expires=1758178800\u0026x-signature=d2fTAbPOBJxKMik8tNosc2XVA5w%3D\u0026format=.jpeg",
                        "item_urls": [
                            ""
                        ],
                        "md5": "",
                        "create_time": 1758174371,
                        "status": 144,
                        "review_info": {
                            "review_status": 1,
                            "review_code_list": []
                        },
                        "aspect_ratio": 0.5625,
                        "publish_source": "user_post_mweb_item",
                        "collection_ids": [],
                        "extra": "",
                        "has_published": false,
                        "cover_url_map": {
                            "1080": "https://p3-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/df04663e5bf54b81ae0c4c381a2e04c9~tplv-tb4s082cfz-aigc_resize_mark:1080:1080.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=vE%2BRm7hHo%2FLLeibr7jpkZ8DqyiA%3D\u0026format=.webp",
                            "2400": "https://p9-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/df04663e5bf54b81ae0c4c381a2e04c9~tplv-tb4s082cfz-aigc_resize_mark:2400:2400.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=o5%2BC5xfjmDbnwGp14WfxVkpAijM%3D\u0026format=.webp",
                            "360": "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/df04663e5bf54b81ae0c4c381a2e04c9~tplv-tb4s082cfz-aigc_resize_mark:360:360.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=pGGb0RlMH%2BTky9KEHzKgp2S707Q%3D\u0026format=.webp",
                            "4096": "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/df04663e5bf54b81ae0c4c381a2e04c9~tplv-tb4s082cfz-aigc_resize_mark:4096:4096.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=LaB%2BZBUel1h%2FsMc7JUnKFgqCcmA%3D\u0026format=.webp",
                            "480": "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/df04663e5bf54b81ae0c4c381a2e04c9~tplv-tb4s082cfz-aigc_resize_mark:480:480.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=VUZX0ol%2FqisqqGQTetPO51ky6zQ%3D\u0026format=.webp",
                            "720": "https://p3-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/df04663e5bf54b81ae0c4c381a2e04c9~tplv-tb4s082cfz-aigc_resize_mark:720:720.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=mZdfDCQOQP7cfACrk1NuuWJEQ98%3D\u0026format=.webp"
                        },
                        "local_item_id": "7551301401561812274",
                        "web_extra": "{\"mweb_abtags\":[\"high_aes_general_v40s_stream\"]}",
                        "update_time": 0,
                        "cover_uri": "tos-cn-i-tb4s082cfz/df04663e5bf54b81ae0c4c381a2e04c9",
                        "smart_crop_loc": null,
                        "cover_height": 640,
                        "cover_width": 360
                    },
                    "author": null,
                    "image": {
                        "format": "png",
                        "large_images": [
                            {
                                "image_uri": "tos-cn-i-tb4s082cfz/df04663e5bf54b81ae0c4c381a2e04c9",
                                "image_url": "https://p9-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/df04663e5bf54b81ae0c4c381a2e04c9~tplv-tb4s082cfz-aigc_resize_mark:0:0.png?lk3s=43402efa\u0026x-expires=1758178800\u0026x-signature=9se2PwA8Ia48%2FhkMKzRZ4oJwooI%3D\u0026format=.png",
                                "width": 1440,
                                "height": 2560,
                                "format": "png",
                                "smart_crop_loc": null
                            }
                        ]
                    },
                    "aigc_image_params": {
                        "generate_type": 1,
                        "first_generate_type": 1,
                        "text2video_params": null,
                        "text2image_params": {
                            "image_ratio": 5,
                            "model_config": {
                                "icon_url": "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/model4.png~tplv-tb4s082cfz-resize:300:300.webp?lk3s=8e790bc3\u0026x-expires=1789710371\u0026x-signature=3StxXJkt%2FqnyvJkypIxgg%2B25JHc%3D",
                                "model_name_starling_key": "dreamina_image4_title",
                                "model_tip_starling_key": "dreamina_image4_desc",
                                "model_req_key": "high_aes_general_v40",
                                "is_new_model": true,
                                "sample_steps": {
                                    "steps": 16,
                                    "min_steps": 10,
                                    "max_steps": 41
                                },
                                "blend_enable": {
                                    "face_swap": false,
                                    "bg_paint": false,
                                    "canny": false,
                                    "depth": false,
                                    "pose": false
                                },
                                "feats": [
                                    "new_model",
                                    "default_scene",
                                    "t2i",
                                    "byte_edit",
                                    "refuse_image",
                                    "smart_scale",
                                    "per_piece",
                                    "think_then_draw",
                                    "byte_edit_with_empty_prompt",
                                    "byte_edit_with_custom_ratio",
                                    "etta"
                                ],
                                "model_name": "图片 4.0",
                                "model_tip": "支持多参考图、系列组图生成",
                                "feature_key": "",
                                "generation_category_name_starling_key": "dreamina_app_modalswitch_image_4_0",
                                "generation_category_name": "图片・4.0",
                                "duration_option": null,
                                "lens_motion_type_option": null,
                                "motion_speed_option": null,
                                "camera_strength_option": null,
                                "video_aspect_ratio_option": null,
                                "fps": 0,
                                "feat_config": {
                                    "canny": {
                                        "strength": 0.6
                                    },
                                    "depth": {
                                        "strength": 0.6
                                    },
                                    "pose": {
                                        "strength": 0.6
                                    },
                                    "style_reference": {
                                        "strength": 0.8
                                    }
                                },
                                "extra": {
                                    "model_source": "by Seedream 4.0",
                                    "raw_model_source": "Seedream 4.0"
                                },
                                "feats_cant_combine": [],
                                "model_bg_prompt_starling_key": "",
                                "resolution_map": {
                                    "2k": {
                                        "image_ratio_sizes": [
                                            {
                                                "ratio_type": 1,
                                                "width": 2048,
                                                "height": 2048
                                            },
                                            {
                                                "ratio_type": 2,
                                                "width": 1728,
                                                "height": 2304
                                            },
                                            {
                                                "ratio_type": 3,
                                                "width": 2560,
                                                "height": 1440
                                            },
                                            {
                                                "ratio_type": 4,
                                                "width": 2304,
                                                "height": 1728
                                            },
                                            {
                                                "ratio_type": 5,
                                                "width": 1440,
                                                "height": 2560
                                            },
                                            {
                                                "ratio_type": 6,
                                                "width": 1664,
                                                "height": 2496
                                            },
                                            {
                                                "ratio_type": 7,
                                                "width": 2496,
                                                "height": 1664
                                            },
                                            {
                                                "ratio_type": 8,
                                                "width": 3024,
                                                "height": 1296
                                            }
                                        ],
                                        "image_range_config": {
                                            "min_length": 1296,
                                            "max_length": 3024,
                                            "max_pixel_num": 4194304
                                        },
                                        "resolution_name": "高清 2K"
                                    }
                                }
                            },
                            "prompt": "扎着高马尾的女孩的照片，嘴唇咬着黑色皮筋，青春靓丽，俏皮可爱",
                            "sample_steps": 25,
                            "seed": 437044651,
                            "user_negative_prompt": "",
                            "large_image_info": {
                                "height": 2560,
                                "width": 1440,
                                "resolution_type": "2k"
                            },
                            "sample_strength": 0.5
                        },
                        "template_id": "0",
                        "request_id": "0cebc708c11d8794bb032628978bf57ef40fe492295445fb117653dd28e9be54",
                        "generate_id": "20250918134604F44C63501CD07015A183",
                        "origin_request_id": "0cebc708c11d8794bb032628978bf57ef40fe492295445fb117653dd28e9be54",
                        "aigc_cnt_list": [
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0
                        ],
                        "aigc_mode": "workbench",
                        "insta_drag_params": {
                            "origin_item_id": null
                        },
                        "hide_ref_images": false,
                        "reference_prompt": "青春摄影风格，自然光，高饱和度，浅景深，特写镜头，扎着高马尾的亚洲女孩，嘴唇轻咬黑色皮筋，发丝微卷，眼神灵动，背景虚化，柔光处理，清新自然，俏皮可爱氛围。",
                        "image_type": 3
                    },
                    "statistic": {
                        "feedback_status": 0
                    },
                    "category_id_list": [],
                    "aigc_flow": {
                        "version": "3.0.2"
                    },
                    "aigc_draft": {
                        "version": "3.0.2",
                        "uri": "aigc-draft/7839431507714",
                        "content": "",
                        "update_time": 0,
                        "last_preview_time": 0,
                        "resource_type": "",
                        "public_uri": "",
                        "variables": null,
                        "resource_width": 0,
                        "resource_height": 0,
                        "node_keys": null,
                        "cost": 0
                    },
                    "gen_result_data": {
                        "result_code": 0,
                        "result_msg": "Success"
                    },
                    "extra": {
                        "template_type": "image",
                        "ai_feature": "text_generate_image"
                    },
                    "ai_feature": {
                        "features": [
                            {
                                "type": "text_generate_image"
                            }
                        ],
                        "is_merged": true
                    },
                    "sharing_info": {
                        "share_status": 2
                    },
                    "metadata_param": "{\"effect_id\":\"gen_image\",\"effect_type\":\"tool\"}",
                    "has_intention_info": false
                },
                {
                    "common_attr": {
                        "id": "7551301410227326217",
                        "effect_id": "7551301410227326217",
                        "effect_type": 9,
                        "title": "",
                        "description": "扎着高马尾的女孩的照片，嘴唇咬着黑色皮筋，青春靓丽，俏皮可爱",
                        "cover_url": "https://p3-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/93b6da2de1e64bcb853a359ec1a9c1ed~tplv-tb4s082cfz-aigc_resize_mark:640:640.jpeg?lk3s=43402efa\u0026x-expires=1758178800\u0026x-signature=%2FrP%2BadijsppyGHO29zboXdvH4pA%3D\u0026format=.jpeg",
                        "item_urls": [
                            ""
                        ],
                        "md5": "",
                        "create_time": 1758174371,
                        "status": 144,
                        "review_info": {
                            "review_status": 1,
                            "review_code_list": []
                        },
                        "aspect_ratio": 0.5625,
                        "publish_source": "user_post_mweb_item",
                        "collection_ids": [],
                        "extra": "",
                        "has_published": false,
                        "cover_url_map": {
                            "1080": "https://p3-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/93b6da2de1e64bcb853a359ec1a9c1ed~tplv-tb4s082cfz-aigc_resize_mark:1080:1080.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=P86uMOV85KblLVMh62UOT4seHH4%3D\u0026format=.webp",
                            "2400": "https://p9-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/93b6da2de1e64bcb853a359ec1a9c1ed~tplv-tb4s082cfz-aigc_resize_mark:2400:2400.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=YfZtXH%2BKfN91FqvQLNy2%2B5c33zA%3D\u0026format=.webp",
                            "360": "https://p3-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/93b6da2de1e64bcb853a359ec1a9c1ed~tplv-tb4s082cfz-aigc_resize_mark:360:360.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=gAzfeeF1GfnR6o7EpQ5CjxMlW8U%3D\u0026format=.webp",
                            "4096": "https://p9-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/93b6da2de1e64bcb853a359ec1a9c1ed~tplv-tb4s082cfz-aigc_resize_mark:4096:4096.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=ZQ8cec3GjoyV2rgBuL7szI1Cnk8%3D\u0026format=.webp",
                            "480": "https://p9-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/93b6da2de1e64bcb853a359ec1a9c1ed~tplv-tb4s082cfz-aigc_resize_mark:480:480.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=CAJKOA8y95Jyqr3IbJ4uFjmkLcw%3D\u0026format=.webp",
                            "720": "https://p9-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/93b6da2de1e64bcb853a359ec1a9c1ed~tplv-tb4s082cfz-aigc_resize_mark:720:720.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=1a%2FKwxpVLpQOZz%2FCViGaeXA4j4M%3D\u0026format=.webp"
                        },
                        "local_item_id": "7551301410227326217",
                        "web_extra": "{\"mweb_abtags\":[\"high_aes_general_v40s_stream\"]}",
                        "update_time": 0,
                        "cover_uri": "tos-cn-i-tb4s082cfz/93b6da2de1e64bcb853a359ec1a9c1ed",
                        "smart_crop_loc": null,
                        "cover_height": 640,
                        "cover_width": 360
                    },
                    "author": null,
                    "image": {
                        "format": "png",
                        "large_images": [
                            {
                                "image_uri": "tos-cn-i-tb4s082cfz/93b6da2de1e64bcb853a359ec1a9c1ed",
                                "image_url": "https://p9-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/93b6da2de1e64bcb853a359ec1a9c1ed~tplv-tb4s082cfz-aigc_resize_mark:0:0.png?lk3s=43402efa\u0026x-expires=1758178800\u0026x-signature=2VoYV9s7gMzZnTkzdC8PVMuIHW4%3D\u0026format=.png",
                                "width": 1440,
                                "height": 2560,
                                "format": "png",
                                "smart_crop_loc": null
                            }
                        ]
                    },
                    "aigc_image_params": {
                        "generate_type": 1,
                        "first_generate_type": 1,
                        "text2video_params": null,
                        "text2image_params": {
                            "image_ratio": 5,
                            "model_config": {
                                "icon_url": "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/model4.png~tplv-tb4s082cfz-resize:300:300.webp?lk3s=8e790bc3\u0026x-expires=1789710371\u0026x-signature=3StxXJkt%2FqnyvJkypIxgg%2B25JHc%3D",
                                "model_name_starling_key": "dreamina_image4_title",
                                "model_tip_starling_key": "dreamina_image4_desc",
                                "model_req_key": "high_aes_general_v40",
                                "is_new_model": true,
                                "sample_steps": {
                                    "steps": 16,
                                    "min_steps": 10,
                                    "max_steps": 41
                                },
                                "blend_enable": {
                                    "face_swap": false,
                                    "bg_paint": false,
                                    "canny": false,
                                    "depth": false,
                                    "pose": false
                                },
                                "feats": [
                                    "new_model",
                                    "default_scene",
                                    "t2i",
                                    "byte_edit",
                                    "refuse_image",
                                    "smart_scale",
                                    "per_piece",
                                    "think_then_draw",
                                    "byte_edit_with_empty_prompt",
                                    "byte_edit_with_custom_ratio",
                                    "etta"
                                ],
                                "model_name": "图片 4.0",
                                "model_tip": "支持多参考图、系列组图生成",
                                "feature_key": "",
                                "generation_category_name_starling_key": "dreamina_app_modalswitch_image_4_0",
                                "generation_category_name": "图片・4.0",
                                "duration_option": null,
                                "lens_motion_type_option": null,
                                "motion_speed_option": null,
                                "camera_strength_option": null,
                                "video_aspect_ratio_option": null,
                                "fps": 0,
                                "feat_config": {
                                    "canny": {
                                        "strength": 0.6
                                    },
                                    "depth": {
                                        "strength": 0.6
                                    },
                                    "pose": {
                                        "strength": 0.6
                                    },
                                    "style_reference": {
                                        "strength": 0.8
                                    }
                                },
                                "extra": {
                                    "model_source": "by Seedream 4.0",
                                    "raw_model_source": "Seedream 4.0"
                                },
                                "feats_cant_combine": [],
                                "model_bg_prompt_starling_key": "",
                                "resolution_map": {
                                    "2k": {
                                        "image_ratio_sizes": [
                                            {
                                                "ratio_type": 1,
                                                "width": 2048,
                                                "height": 2048
                                            },
                                            {
                                                "ratio_type": 2,
                                                "width": 1728,
                                                "height": 2304
                                            },
                                            {
                                                "ratio_type": 3,
                                                "width": 2560,
                                                "height": 1440
                                            },
                                            {
                                                "ratio_type": 4,
                                                "width": 2304,
                                                "height": 1728
                                            },
                                            {
                                                "ratio_type": 5,
                                                "width": 1440,
                                                "height": 2560
                                            },
                                            {
                                                "ratio_type": 6,
                                                "width": 1664,
                                                "height": 2496
                                            },
                                            {
                                                "ratio_type": 7,
                                                "width": 2496,
                                                "height": 1664
                                            },
                                            {
                                                "ratio_type": 8,
                                                "width": 3024,
                                                "height": 1296
                                            }
                                        ],
                                        "image_range_config": {
                                            "min_length": 1296,
                                            "max_length": 3024,
                                            "max_pixel_num": 4194304
                                        },
                                        "resolution_name": "高清 2K"
                                    }
                                }
                            },
                            "prompt": "扎着高马尾的女孩的照片，嘴唇咬着黑色皮筋，青春靓丽，俏皮可爱",
                            "sample_steps": 25,
                            "seed": 437044651,
                            "user_negative_prompt": "",
                            "large_image_info": {
                                "height": 2560,
                                "width": 1440,
                                "resolution_type": "2k"
                            },
                            "sample_strength": 0.5
                        },
                        "template_id": "0",
                        "request_id": "ac37352434f167b5f44629cd59451449ddfda3cc229588e6e3afa12724b6db04",
                        "generate_id": "20250918134604F44C63501CD07015A183",
                        "origin_request_id": "ac37352434f167b5f44629cd59451449ddfda3cc229588e6e3afa12724b6db04",
                        "aigc_cnt_list": [
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0
                        ],
                        "aigc_mode": "workbench",
                        "insta_drag_params": {
                            "origin_item_id": null
                        },
                        "hide_ref_images": false,
                        "reference_prompt": "青春摄影风格，自然光，高饱和度，浅景深，特写镜头，扎着高马尾的亚洲女孩，嘴唇轻咬黑色皮筋，发丝微卷，眼神灵动，背景虚化，柔光处理，清新自然，俏皮可爱氛围。",
                        "image_type": 3
                    },
                    "statistic": {
                        "feedback_status": 0
                    },
                    "category_id_list": [],
                    "aigc_flow": {
                        "version": "3.0.2"
                    },
                    "aigc_draft": {
                        "version": "3.0.2",
                        "uri": "aigc-draft/7839431507714",
                        "content": "",
                        "update_time": 0,
                        "last_preview_time": 0,
                        "resource_type": "",
                        "public_uri": "",
                        "variables": null,
                        "resource_width": 0,
                        "resource_height": 0,
                        "node_keys": null,
                        "cost": 0
                    },
                    "gen_result_data": {
                        "result_code": 0,
                        "result_msg": "Success"
                    },
                    "extra": {
                        "template_type": "image",
                        "ai_feature": "text_generate_image"
                    },
                    "ai_feature": {
                        "features": [
                            {
                                "type": "text_generate_image"
                            }
                        ],
                        "is_merged": true
                    },
                    "sharing_info": {
                        "share_status": 2
                    },
                    "metadata_param": "{\"effect_id\":\"gen_image\",\"effect_type\":\"tool\"}",
                    "has_intention_info": false
                },
                {
                    "common_attr": {
                        "id": "7551301402111266086",
                        "effect_id": "7551301402111266086",
                        "effect_type": 9,
                        "title": "",
                        "description": "扎着高马尾的女孩的照片，嘴唇咬着黑色皮筋，青春靓丽，俏皮可爱",
                        "cover_url": "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/4b2fc1272c7d4e25b2460242ea5190b6~tplv-tb4s082cfz-aigc_resize_mark:640:640.jpeg?lk3s=43402efa\u0026x-expires=1758178800\u0026x-signature=g7z0lUk85BZ57Np1FmBWYu20CYk%3D\u0026format=.jpeg",
                        "item_urls": [
                            ""
                        ],
                        "md5": "",
                        "create_time": 1758174371,
                        "status": 144,
                        "review_info": {
                            "review_status": 1,
                            "review_code_list": []
                        },
                        "aspect_ratio": 0.5625,
                        "publish_source": "user_post_mweb_item",
                        "collection_ids": [],
                        "extra": "",
                        "has_published": false,
                        "cover_url_map": {
                            "1080": "https://p9-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/4b2fc1272c7d4e25b2460242ea5190b6~tplv-tb4s082cfz-aigc_resize_mark:1080:1080.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=f0dZlhooIFdmkZaISU3RvDIGNDo%3D\u0026format=.webp",
                            "2400": "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/4b2fc1272c7d4e25b2460242ea5190b6~tplv-tb4s082cfz-aigc_resize_mark:2400:2400.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=XSFF1Qs7DPhlppzNAYl53W7%2Fzqo%3D\u0026format=.webp",
                            "360": "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/4b2fc1272c7d4e25b2460242ea5190b6~tplv-tb4s082cfz-aigc_resize_mark:360:360.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=jJgq3%2Fws3DytdFqkzsyXN0c8ZM8%3D\u0026format=.webp",
                            "4096": "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/4b2fc1272c7d4e25b2460242ea5190b6~tplv-tb4s082cfz-aigc_resize_mark:4096:4096.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=KNQPQj88rlfeOBHKHivdgp16WXE%3D\u0026format=.webp",
                            "480": "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/4b2fc1272c7d4e25b2460242ea5190b6~tplv-tb4s082cfz-aigc_resize_mark:480:480.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=mkNdIo1vi1RrbezY5iJ4tLlkHFI%3D\u0026format=.webp",
                            "720": "https://p9-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/4b2fc1272c7d4e25b2460242ea5190b6~tplv-tb4s082cfz-aigc_resize_mark:720:720.webp?lk3s=43402efa\u0026x-expires=1759968000\u0026x-signature=X1p8Oep1dRA4%2FPqntAkeu6GB0Zo%3D\u0026format=.webp"
                        },
                        "local_item_id": "7551301402111266086",
                        "web_extra": "{\"mweb_abtags\":[\"high_aes_general_v40s_stream\"]}",
                        "update_time": 0,
                        "cover_uri": "tos-cn-i-tb4s082cfz/4b2fc1272c7d4e25b2460242ea5190b6",
                        "smart_crop_loc": null,
                        "cover_height": 640,
                        "cover_width": 360
                    },
                    "author": null,
                    "image": {
                        "format": "png",
                        "large_images": [
                            {
                                "image_uri": "tos-cn-i-tb4s082cfz/4b2fc1272c7d4e25b2460242ea5190b6",
                                "image_url": "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/4b2fc1272c7d4e25b2460242ea5190b6~tplv-tb4s082cfz-aigc_resize_mark:0:0.png?lk3s=43402efa\u0026x-expires=1758178800\u0026x-signature=XbhpblrzQdmuSeGAJM%2Fb910HCbQ%3D\u0026format=.png",
                                "width": 1440,
                                "height": 2560,
                                "format": "png",
                                "smart_crop_loc": null
                            }
                        ]
                    },
                    "aigc_image_params": {
                        "generate_type": 1,
                        "first_generate_type": 1,
                        "text2video_params": null,
                        "text2image_params": {
                            "image_ratio": 5,
                            "model_config": {
                                "icon_url": "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/model4.png~tplv-tb4s082cfz-resize:300:300.webp?lk3s=8e790bc3\u0026x-expires=1789710371\u0026x-signature=3StxXJkt%2FqnyvJkypIxgg%2B25JHc%3D",
                                "model_name_starling_key": "dreamina_image4_title",
                                "model_tip_starling_key": "dreamina_image4_desc",
                                "model_req_key": "high_aes_general_v40",
                                "is_new_model": true,
                                "sample_steps": {
                                    "steps": 16,
                                    "min_steps": 10,
                                    "max_steps": 41
                                },
                                "blend_enable": {
                                    "face_swap": false,
                                    "bg_paint": false,
                                    "canny": false,
                                    "depth": false,
                                    "pose": false
                                },
                                "feats": [
                                    "new_model",
                                    "default_scene",
                                    "t2i",
                                    "byte_edit",
                                    "refuse_image",
                                    "smart_scale",
                                    "per_piece",
                                    "think_then_draw",
                                    "byte_edit_with_empty_prompt",
                                    "byte_edit_with_custom_ratio",
                                    "etta"
                                ],
                                "model_name": "图片 4.0",
                                "model_tip": "支持多参考图、系列组图生成",
                                "feature_key": "",
                                "generation_category_name_starling_key": "dreamina_app_modalswitch_image_4_0",
                                "generation_category_name": "图片・4.0",
                                "duration_option": null,
                                "lens_motion_type_option": null,
                                "motion_speed_option": null,
                                "camera_strength_option": null,
                                "video_aspect_ratio_option": null,
                                "fps": 0,
                                "feat_config": {
                                    "canny": {
                                        "strength": 0.6
                                    },
                                    "depth": {
                                        "strength": 0.6
                                    },
                                    "pose": {
                                        "strength": 0.6
                                    },
                                    "style_reference": {
                                        "strength": 0.8
                                    }
                                },
                                "extra": {
                                    "model_source": "by Seedream 4.0",
                                    "raw_model_source": "Seedream 4.0"
                                },
                                "feats_cant_combine": [],
                                "model_bg_prompt_starling_key": "",
                                "resolution_map": {
                                    "2k": {
                                        "image_ratio_sizes": [
                                            {
                                                "ratio_type": 1,
                                                "width": 2048,
                                                "height": 2048
                                            },
                                            {
                                                "ratio_type": 2,
                                                "width": 1728,
                                                "height": 2304
                                            },
                                            {
                                                "ratio_type": 3,
                                                "width": 2560,
                                                "height": 1440
                                            },
                                            {
                                                "ratio_type": 4,
                                                "width": 2304,
                                                "height": 1728
                                            },
                                            {
                                                "ratio_type": 5,
                                                "width": 1440,
                                                "height": 2560
                                            },
                                            {
                                                "ratio_type": 6,
                                                "width": 1664,
                                                "height": 2496
                                            },
                                            {
                                                "ratio_type": 7,
                                                "width": 2496,
                                                "height": 1664
                                            },
                                            {
                                                "ratio_type": 8,
                                                "width": 3024,
                                                "height": 1296
                                            }
                                        ],
                                        "image_range_config": {
                                            "min_length": 1296,
                                            "max_length": 3024,
                                            "max_pixel_num": 4194304
                                        },
                                        "resolution_name": "高清 2K"
                                    }
                                }
                            },
                            "prompt": "扎着高马尾的女孩的照片，嘴唇咬着黑色皮筋，青春靓丽，俏皮可爱",
                            "sample_steps": 25,
                            "seed": 437044651,
                            "user_negative_prompt": "",
                            "large_image_info": {
                                "height": 2560,
                                "width": 1440,
                                "resolution_type": "2k"
                            },
                            "sample_strength": 0.5
                        },
                        "template_id": "0",
                        "request_id": "af895d7bb7fd41bb32cf4d90f396ba4294e5166949a90a24bf4559bf118fdd4b",
                        "generate_id": "20250918134604F44C63501CD07015A183",
                        "origin_request_id": "af895d7bb7fd41bb32cf4d90f396ba4294e5166949a90a24bf4559bf118fdd4b",
                        "aigc_cnt_list": [
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0,
                            0
                        ],
                        "aigc_mode": "workbench",
                        "insta_drag_params": {
                            "origin_item_id": null
                        },
                        "hide_ref_images": false,
                        "reference_prompt": "青春摄影风格，自然光，高饱和度，浅景深，特写镜头，扎着高马尾的亚洲女孩，嘴唇轻咬黑色皮筋，发丝微卷，眼神灵动，背景虚化，柔光处理，清新自然，俏皮可爱氛围。",
                        "image_type": 3
                    },
                    "statistic": {
                        "feedback_status": 0
                    },
                    "category_id_list": [],
                    "aigc_flow": {
                        "version": "3.0.2"
                    },
                    "aigc_draft": {
                        "version": "3.0.2",
                        "uri": "aigc-draft/7839431507714",
                        "content": "",
                        "update_time": 0,
                        "last_preview_time": 0,
                        "resource_type": "",
                        "public_uri": "",
                        "variables": null,
                        "resource_width": 0,
                        "resource_height": 0,
                        "node_keys": null,
                        "cost": 0
                    },
                    "gen_result_data": {
                        "result_code": 0,
                        "result_msg": "Success"
                    },
                    "extra": {
                        "template_type": "image",
                        "ai_feature": "text_generate_image"
                    },
                    "ai_feature": {
                        "features": [
                            {
                                "type": "text_generate_image"
                            }
                        ],
                        "is_merged": true
                    },
                    "sharing_info": {
                        "share_status": 2
                    },
                    "metadata_param": "{\"effect_id\":\"gen_image\",\"effect_type\":\"tool\"}",
                    "has_intention_info": false
                }
            ],
            "origin_item_list": [],
            "task": {
                "task_id": "25970715096066",
                "submit_id": "28de960e-c471-4f5d-aabc-6d229955058e",
                "aid": 0,
                "status": 50,
                "finish_time": 1758174372,
                "history_id": "25970715096066",
                "task_payload": null,
                "original_input": null,
                "req_first_frame_image": null,
                "ai_gen_prompt": "",
                "priority": 0,
                "lip_sync_info": null,
                "multi_size_first_frame_image": null,
                "multi_size_end_frame_image": null,
                "process_flows": null,
                "create_time": 0,
                "aigc_image_params": {
                    "generate_type": 1,
                    "first_generate_type": 1,
                    "text2video_params": null,
                    "text2image_params": {
                        "image_ratio": 5,
                        "model_config": {
                            "icon_url": "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/model4.png~tplv-tb4s082cfz-resize:300:300.webp?lk3s=8e790bc3\u0026x-expires=1789710371\u0026x-signature=3StxXJkt%2FqnyvJkypIxgg%2B25JHc%3D",
                            "model_name_starling_key": "dreamina_image4_title",
                            "model_tip_starling_key": "dreamina_image4_desc",
                            "model_req_key": "high_aes_general_v40",
                            "is_new_model": true,
                            "sample_steps": {
                                "steps": 16,
                                "min_steps": 10,
                                "max_steps": 41
                            },
                            "blend_enable": {
                                "face_swap": false,
                                "bg_paint": false,
                                "canny": false,
                                "depth": false,
                                "pose": false
                            },
                            "feats": [
                                "new_model",
                                "default_scene",
                                "t2i",
                                "byte_edit",
                                "refuse_image",
                                "smart_scale",
                                "per_piece",
                                "think_then_draw",
                                "byte_edit_with_empty_prompt",
                                "byte_edit_with_custom_ratio",
                                "etta"
                            ],
                            "model_name": "图片 4.0",
                            "model_tip": "支持多参考图、系列组图生成",
                            "feature_key": "",
                            "generation_category_name_starling_key": "dreamina_app_modalswitch_image_4_0",
                            "generation_category_name": "图片・4.0",
                            "duration_option": null,
                            "lens_motion_type_option": null,
                            "motion_speed_option": null,
                            "camera_strength_option": null,
                            "video_aspect_ratio_option": null,
                            "fps": 0,
                            "feat_config": {
                                "canny": {
                                    "strength": 0.6
                                },
                                "depth": {
                                    "strength": 0.6
                                },
                                "pose": {
                                    "strength": 0.6
                                },
                                "style_reference": {
                                    "strength": 0.8
                                }
                            },
                            "extra": {
                                "model_source": "by Seedream 4.0",
                                "raw_model_source": "Seedream 4.0"
                            },
                            "feats_cant_combine": [],
                            "model_bg_prompt_starling_key": "",
                            "resolution_map": {
                                "2k": {
                                    "image_ratio_sizes": [
                                        {
                                            "ratio_type": 1,
                                            "width": 2048,
                                            "height": 2048
                                        },
                                        {
                                            "ratio_type": 2,
                                            "width": 1728,
                                            "height": 2304
                                        },
                                        {
                                            "ratio_type": 3,
                                            "width": 2560,
                                            "height": 1440
                                        },
                                        {
                                            "ratio_type": 4,
                                            "width": 2304,
                                            "height": 1728
                                        },
                                        {
                                            "ratio_type": 5,
                                            "width": 1440,
                                            "height": 2560
                                        },
                                        {
                                            "ratio_type": 6,
                                            "width": 1664,
                                            "height": 2496
                                        },
                                        {
                                            "ratio_type": 7,
                                            "width": 2496,
                                            "height": 1664
                                        },
                                        {
                                            "ratio_type": 8,
                                            "width": 3024,
                                            "height": 1296
                                        }
                                    ],
                                    "image_range_config": {
                                        "min_length": 1296,
                                        "max_length": 3024,
                                        "max_pixel_num": 4194304
                                    },
                                    "resolution_name": "高清 2K"
                                }
                            }
                        },
                        "prompt": "扎着高马尾的女孩的照片，嘴唇咬着黑色皮筋，青春靓丽，俏皮可爱",
                        "sample_steps": 25,
                        "seed": 437044651,
                        "user_negative_prompt": "",
                        "large_image_info": {
                            "height": 2560,
                            "width": 1440,
                            "resolution_type": "2k"
                        },
                        "sample_strength": 0.5
                    },
                    "template_id": "0",
                    "request_id": "68bb32a6e37ab9b960850355d997d96196d2777bbe8040a12e8afe61342c8415",
                    "generate_id": "20250918134604F44C63501CD07015A183",
                    "origin_request_id": "68bb32a6e37ab9b960850355d997d96196d2777bbe8040a12e8afe61342c8415",
                    "aigc_cnt_list": [
                        0,
                        0,
                        0,
                        0,
                        0,
                        0,
                        0,
                        0,
                        0,
                        0,
                        0
                    ],
                    "aigc_mode": "workbench",
                    "insta_drag_params": {
                        "origin_item_id": null
                    },
                    "hide_ref_images": false,
                    "reference_prompt": "青春摄影风格，自然光，高饱和度，浅景深，特写镜头，扎着高马尾的亚洲女孩，嘴唇轻咬黑色皮筋，发丝微卷，眼神灵动，背景虚化，柔光处理，清新自然，俏皮可爱氛围。",
                    "image_type": 3
                },
                "ref_item": null,
                "resp_ret": {
                    "ret": ""
                }
            },
            "mode": "workbench",
            "asset_option": {
                "has_favorited": false
            },
            "uid": "317079856886999",
            "aigc_flow": {
                "version": "3.0.2"
            },
            "status": 50,
            "history_group_key_md5": "8e991659af057b79c9057d1fee2ffda4",
            "history_group_key": "扎着高马尾的女孩的照片，嘴唇咬着黑色皮筋，青春靓丽，俏皮可爱",
            "draft_content": "{\"type\":\"draft\",\"id\":\"9bcf479b-7009-48ca-4c4f-dfe6b30b6d23\",\"min_version\":\"3.0.2\",\"min_features\":[],\"is_from_tsn\":true,\"version\":\"3.0.2\",\"main_component_id\":\"38418ba4-92a8-a28e-e9d5-fbac0287ecd1\",\"component_list\":[{\"type\":\"image_base_component\",\"id\":\"38418ba4-92a8-a28e-e9d5-fbac0287ecd1\",\"min_version\":\"3.0.2\",\"aigc_mode\":\"workbench\",\"gen_type\":1,\"metadata\":{\"type\":\"\",\"id\":\"91950438-c476-2792-81e8-dcd83a00dc12\",\"created_platform\":3,\"created_platform_version\":\"\",\"created_time_in_ms\":\"1758174363600\",\"created_did\":\"\"},\"generate_type\":\"generate\",\"abilities\":{\"type\":\"\",\"id\":\"a3cf9a00-7a65-f5e5-50c2-66d214f05fb9\",\"generate\":{\"type\":\"\",\"id\":\"24ce9c0a-cfeb-1b6c-feb0-9178de5a7b92\",\"core_param\":{\"type\":\"\",\"id\":\"32d9acf6-78bc-a20e-680c-9c83681a24f3\",\"model\":\"high_aes_general_v40\",\"prompt\":\"扎着高马尾的女孩的照片，嘴唇咬着黑色皮筋，青春靓丽，俏皮可爱\",\"negative_prompt\":\"\",\"seed\":437044651,\"sample_strength\":0.5,\"image_ratio\":5,\"large_image_info\":{\"type\":\"\",\"id\":\"20f1153f-72e4-4075-49fc-e5ca90cda20e\",\"height\":2560,\"width\":1440,\"resolution_type\":\"2k\"},\"intelligent_ratio\":false}}}}]}",
            "fail_code": "0",
            "submit_id": "28de960e-c471-4f5d-aabc-6d229955058e",
            "capflow_id": "28de960e-c471-4f5d-aabc-6d229955058e",
            "metrics_extra": "{\"promptSource\":\"custom\",\"generateCount\":1,\"enterFrom\":\"click\",\"generateId\":\"28de960e-c471-4f5d-aabc-6d229955058e\",\"isRegenerate\":false}",
            "fail_msg": "Success",
            "generate_id": "20250918134604F44C63501CD07015A183",
            "finish_time": 1758174372,
            "model_info": {
                "icon_url": "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/model4.png~tplv-tb4s082cfz-resize:300:300.webp?lk3s=8e790bc3\u0026x-expires=1789710371\u0026x-signature=3StxXJkt%2FqnyvJkypIxgg%2B25JHc%3D",
                "model_name_starling_key": "dreamina_image4_title",
                "model_tip_starling_key": "dreamina_image4_desc",
                "model_req_key": "high_aes_general_v40",
                "model_name": "图片 4.0",
                "video_model_options": null
            },
            "forecast_generate_cost": 22,
            "forecast_queue_cost": 0,
            "fail_starling_key": "",
            "fail_starling_message": "",
            "min_feats": null,
            "queue_info": {
                "queue_idx": 0,
                "priority": 5,
                "queue_status": 3,
                "queue_length": 0,
                "polling_config": {
                    "interval_seconds": 30,
                    "timeout_seconds": 86400
                },
                "priority_queue_display_threshold": {
                    "vip_queuing_time_threshold": 300,
                    "waiting_time_threshold": 60
                }
            },
            "agent_history_id": null,
            "total_image_count": 4,
            "finished_image_count": 4,
            "confirm_status": 2,
            "confirm_token": "",
            "image_type": 3
        }
    }
}

其中，提交的需求选择的尺寸比例是9:16，提交的参数是height:2560,width:1440