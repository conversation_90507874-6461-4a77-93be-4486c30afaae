2025-09-18 15:01:16,840 - Digital - [main.chat_message_api] - ℹ️  ====API聊天请求====: user_id=zhang<PERSON>, user_name=🔆, user_sex=1, isgroup=1, session_id = <PERSON>@San, is_Segment=0, _token=f10244, _appid=0yW1q0, message=@嫣然  你长什么样？ 生成一张图片来让大家看看
2025-09-18 15:01:16,840 - Digital - [main.enhanced_process_message] - ℹ️  🚀 [req_e1bc0dad4a2f] 开始处理用户请求: user_id=zhang<PERSON>, thread=140149091145472
2025-09-18 15:01:16,840 - Digital - [main.enhanced_process_message] - ℹ️  📥 [req_e1bc0dad4a2f] 接收到用户请求: user_id=zhang<PERSON>, user_name=🔆
2025-09-18 15:01:16,840 - Digital - [main._validate_api_data] - ℹ️  ✅ 接收API数据: user_id=z<PERSON><PERSON>, user_name=🔆, user_sex=1
2025-09-18 15:01:16,840 - Digital - [unified_user_manager.process_user_request] - ℹ️  🔄 [umgr_77d64bc4] Thread-140149091145472 开始处理用户请求: user_id=zhangsan
2025-09-18 15:01:17,066 - Digital - [unified_user_manager._create_user] - ℹ️  ✅ 使用API传入的有效用户名: 🔆
2025-09-18 15:01:17,292 - Digital - [unified_user_manager.get_or_create_user] - ℹ️  创建新用户: zhangsan
2025-09-18 15:01:17,292 - Digital - [unified_user_manager._create_session_with_id] - ℹ️  创建指定ID会话: Zhang@San (用户: zhangsan)
2025-09-18 15:01:17,292 - Digital - [unified_user_manager.process_user_request] - ℹ️  🔗 [umgr_77d64bc4] 创建指定ID会话: Zhang@San
2025-09-18 15:01:17,292 - Digital - [unified_user_manager.success] - ✅ ✅ [umgr_77d64bc4] 用户请求处理完成: zhangsan (🔆)
2025-09-18 15:01:17,294 - Digital - [main.enhanced_process_message] - ℹ️  ✅ 使用API传入的用户名: 🔆
2025-09-18 15:01:17,294 - Digital - [main.enhanced_process_message] - ℹ️  ✅ 确定的最终用户名: 🔆
2025-09-18 15:01:17,295 - Digital - [main._verify_saved_data] - ℹ️  ✅ contacts.json验证通过: zhangsan -> 🔆
2025-09-18 15:01:17,296 - Digital - [main._verify_saved_data] - ℹ️  ✅ unified_user_manager验证通过: zhangsan -> 🔆
2025-09-18 15:01:17,297 - Digital - [main._verify_saved_data] - ℹ️  ✅ 数据保存验证通过: zhangsan -> 🔆
2025-09-18 15:01:17,297 - Digital - [main.success] - ✅ 统一用户管理: zhangsan (🔆) - 现有用户, 新会话
2025-09-18 15:01:17,297 - Digital - [main.enhanced_process_message] - ℹ️  收到用户消息: @嫣然  你长什么样？ 生成一张图片来让大家看看... [用户:zhangsan/🔆]
2025-09-18 15:01:17,298 - Digital - [main.enhanced_process_message] - ℹ️  🔗 使用传入的session_id: Zhang@San
2025-09-18 15:01:17,334 - Digital - [UnifiedDataFlowManager._monitor_data_flow_once] - ℹ️  数据流监控: 活跃0, 成功0, 失败0
2025-09-18 15:01:17,336 - Digital - [UnifiedDataFlowManager.__init__] - ℹ️  统一数据流管理器初始化完成
2025-09-18 15:01:17,346 - Digital - [singleton_manager.register] - ℹ️  ✅ 已注册实例: 'unified_data_flow_manager'
2025-09-18 15:01:17,347 - Digital - [cognitive_modules.decision.yanran_response_decision.success] - ✅ 初始化林嫣然智能响应决策系统...
2025-09-18 15:01:17,347 - Digital - [cognitive_modules.decision.yanran_response_decision.success] - ✅ 林嫣然智能响应决策系统初始化完成
2025-09-18 15:01:17,348 - Digital - [cognitive_modules.decision.yanran_response_decision.handle_delayed_response_decision] - ℹ️  🔍 开始处理延迟回复决策 (用户: zhangsan)
2025-09-18 15:01:17,756 - Digital - [ContactsManager._load_contacts] - ℹ️  加载联系人数据: 63个用户
2025-09-18 15:01:18,200 - Digital - [neural_consciousness.save_model] - ℹ️  神经网络模型已保存到: /root/yanran_digital_life/data/neural_models/consciousness_enhancer.pkl
2025-09-18 15:01:18,227 - Digital - [advanced_neural_consciousness.save_memory] - ℹ️  💾 记忆网络数据已保存到: /root/yanran_digital_life/data/neural_models/consciousness_memory.json
2025-09-18 15:01:18,424 - Digital - [advanced_neural_consciousness.save_memory] - ℹ️  💾 记忆网络数据已保存到: /root/yanran_digital_life/data/neural_models/consciousness_memory.json
2025-09-18 15:01:18,948 - Digital - [main.chat_message_api] - ℹ️  ====API聊天请求====: user_id=lisi, user_name=《AI魔法詹学院》群友：AI画中画, user_sex=0, isgroup=1, session_id = Li@Si, is_Segment=0, _token=f10244, _appid=0yW1q0, message=快手光合文旅X画爷爷工作室，锅气里的甘肃，趁热，请！（制作：AI画中画、山雨、干饭的星辰；剪辑：Pp）m
2025-09-18 15:01:18,948 - Digital - [main.enhanced_process_message] - ℹ️  🚀 [req_8aabc967a2b8] 开始处理用户请求: user_id=lisi, thread=140149007218432
2025-09-18 15:01:18,949 - Digital - [main.enhanced_process_message] - ℹ️  📥 [req_8aabc967a2b8] 接收到用户请求: user_id=lisi, user_name=《AI魔法詹学院》群友：AI画中画
2025-09-18 15:01:18,949 - Digital - [main._validate_api_data] - ℹ️  ✅ 接收API数据: user_id=lisi, user_name=《AI魔法詹学院》群友：AI画中画, user_sex=0
2025-09-18 15:01:18,949 - Digital - [unified_user_manager.process_user_request] - ℹ️  🔄 [umgr_90fbc3ff] Thread-140149007218432 开始处理用户请求: user_id=lisi
2025-09-18 15:01:19,177 - Digital - [unified_user_manager._create_user] - ℹ️  ✅ 使用API传入的有效用户名: 《AI魔法詹学院》群友：AI画中画
2025-09-18 15:01:19,178 - Digital - [ContactsManager.ensure_user_exists] - ℹ️  🔄 检测到用户信息变更，更新contacts.json: lisi
2025-09-18 15:01:19,178 - Digital - [ContactsManager.ensure_user_exists] - ℹ️     昵称: 《AI魔法詹学院》群友：AI动漫_小强 -> 《AI魔法詹学院》群友：AI画中画
2025-09-18 15:01:19,179 - Digital - [ContactsManager.ensure_user_exists] - ℹ️     性别: 1 -> 1
2025-09-18 15:01:19,406 - Digital - [unified_user_manager.get_or_create_user] - ℹ️  创建新用户: lisi
2025-09-18 15:01:19,406 - Digital - [unified_user_manager._create_session_with_id] - ℹ️  创建指定ID会话: Li@Si (用户: lisi)
2025-09-18 15:01:19,407 - Digital - [unified_user_manager.process_user_request] - ℹ️  🔗 [umgr_90fbc3ff] 创建指定ID会话: Li@Si
2025-09-18 15:01:19,407 - Digital - [unified_user_manager.success] - ✅ ✅ [umgr_90fbc3ff] 用户请求处理完成: lisi (《AI魔法詹学院》群友：AI画中画)
2025-09-18 15:01:19,407 - Digital - [main.enhanced_process_message] - ℹ️  ✅ 使用API传入的用户名: 《AI魔法詹学院》群友：AI画中画
2025-09-18 15:01:19,407 - Digital - [main.enhanced_process_message] - ℹ️  ✅ 确定的最终用户名: 《AI魔法詹学院》群友：AI画中画
2025-09-18 15:01:19,408 - Digital - [main._verify_saved_data] - ℹ️  ✅ contacts.json验证通过: lisi -> 《AI魔法詹学院》群友：AI画中画
2025-09-18 15:01:19,408 - Digital - [main._verify_saved_data] - ℹ️  ✅ unified_user_manager验证通过: lisi -> 《AI魔法詹学院》群友：AI画中画
2025-09-18 15:01:19,409 - Digital - [main._verify_saved_data] - ℹ️  ✅ 数据保存验证通过: lisi -> 《AI魔法詹学院》群友：AI画中画
2025-09-18 15:01:19,409 - Digital - [main.success] - ✅ 统一用户管理: lisi (《AI魔法詹学院》群友：AI画中画) - 现有用户, 新会话
2025-09-18 15:01:19,409 - Digital - [main.enhanced_process_message] - ℹ️  收到用户消息: 快手光合文旅X画爷爷工作室，锅气里的甘肃，趁热，请！（制作：... [用户:lisi/《AI魔法詹学院》群友：AI画中画]
2025-09-18 15:01:19,409 - Digital - [main.enhanced_process_message] - ℹ️  🔗 使用传入的session_id: Li@Si
2025-09-18 15:01:19,410 - Digital - [cognitive_modules.decision.yanran_response_decision.handle_delayed_response_decision] - ℹ️  🔍 开始处理延迟回复决策 (用户: lisi)
2025-09-18 15:01:20,569 - Digital - [feedback_learning._load_weights] - ℹ️  从 data/feedback_learning/weights.json 加载权重
2025-09-18 15:01:20,570 - Digital - [feedback_learning.success] - ✅ 反馈学习模块初始化完成
2025-09-18 15:01:20,576 - Digital - [feedback_learning.get_instance] - ℹ️  ✅ 反馈学习模块已注册到singleton_manager
2025-09-18 15:01:20,576 - Digital - [cognitive_modules.decision.yanran_response_decision.should_respond_to_message] - ℹ️  ✅ 完成响应决策 (用户: zhangsan): ResponseDecision.REPLY_NOW
2025-09-18 15:01:20,578 - Digital - [main.enhanced_process_message] - ℹ️  ⚙️ 开始初始化器官系统...
2025-09-18 15:01:20,580 - Digital - [organ_system_manager.initialize_organ_system] - ℹ️  ⚙️ 开始初始化器官系统...
2025-09-18 15:01:20,581 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: safety_protection_organ
2025-09-18 15:01:20,581 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 safety_protection_organ 激活成功 (优先级: CRITICAL)
2025-09-18 15:01:20,581 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: world_perception_organ
2025-09-18 15:01:20,582 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 world_perception_organ 激活成功 (优先级: HIGH)
2025-09-18 15:01:20,582 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: proactive_expression_organ
2025-09-18 15:01:20,583 - Digital - [ProactiveExpressionOrgan.activate] - ℹ️  💬 🚀 主动表达器官正在激活...
2025-09-18 15:01:20,583 - Digital - [ProactiveExpressionOrgan.activate] - ℹ️  💬 ✅ 主动表达器官已激活，监控系统运行中
2025-09-18 15:01:20,584 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 proactive_expression_organ 激活成功
2025-09-18 15:01:20,584 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 proactive_expression_organ 激活成功 (优先级: HIGH)
2025-09-18 15:01:20,584 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: creative_expression_organ
2025-09-18 15:01:20,585 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 creative_expression_organ 激活成功 (优先级: NORMAL)
2025-09-18 15:01:20,585 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: wealth_management_organ
2025-09-18 15:01:20,585 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 wealth_management_organ 激活成功 (优先级: LOW)
2025-09-18 15:01:20,586 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 注册器官: data_perception_organ
2025-09-18 15:01:20,586 - Digital - [organ_system_manager._register_organ_sync] - ℹ️  ⚙️ 器官 data_perception_organ 激活成功 (优先级: LOW)
2025-09-18 15:01:20,586 - Digital - [organ_system_manager._start_coordination_loop_sync] - ℹ️  ⚙️ 器官协调循环已在现有事件循环中启动
2025-09-18 15:01:20,586 - Digital - [organ_system_manager.initialize_organ_system] - ℹ️  ⚙️ 器官系统初始化完成 - 注册了 6 个器官
2025-09-18 15:01:20,587 - Digital - [organ_system_manager.coordination_loop] - ℹ️  ⚙️ 器官协调循环开始运行
2025-09-18 15:01:20,589 - Digital - [main.enhanced_process_message] - ℹ️  ⚙️ 器官系统初始化成功
2025-09-18 15:01:20,589 - Digital - [main.enhanced_process_message] - ℹ️  ⚙️ 器官系统状态: active - 活跃器官: 6
2025-09-18 15:01:20,612 - Digital - [main.enhanced_process_message] - ℹ️  🛡️ 开始安全防护检查...
2025-09-18 15:01:20,614 - Digital - [ai_safety_filter._init_ai_service] - ℹ️  🛡️ 通过AI服务适配器获取AI服务成功
2025-09-18 15:01:20,615 - Digital - [ai_safety_filter._init_ai_service] - ℹ️  🛡️ AI服务连接成功
2025-09-18 15:01:20,615 - Digital - [ai_safety_filter.__init__] - ℹ️  🛡️ AI安全过滤器初始化完成
2025-09-18 15:01:20,616 - Digital - [ai_safety_filter.filter_input] - ℹ️  🛡️ 开始安全过滤用户输入: @嫣然  你长什么样？ 生成一张图片来让大家看看...
2025-09-18 15:01:21,562 - Digital - [cognitive_modules.decision.yanran_response_decision.should_respond_to_message] - ℹ️  ✅ 完成响应决策 (用户: lisi): ResponseDecision.REPLY_NOW
2025-09-18 15:01:21,564 - Digital - [main.enhanced_process_message] - ℹ️  ⚙️ 器官系统状态: active - 活跃器官: 6
2025-09-18 15:01:21,565 - Digital - [main.enhanced_process_message] - ℹ️  🛡️ 开始安全防护检查...
2025-09-18 15:01:21,567 - Digital - [ai_safety_filter.filter_input] - ℹ️  🛡️ 开始安全过滤用户输入: 快手光合文旅X画爷爷工作室，锅气里的甘肃，趁热，请！（制作：AI画中画、山雨、干饭的星辰；剪辑：Pp...
2025-09-18 15:01:23,066 - Digital - [ai_safety_filter.filter_input] - ℹ️  🛡️ 安全过滤完成 - 风险等级: safe, 安全: True
2025-09-18 15:01:23,067 - Digital - [main.enhanced_process_message] - ℹ️  🛡️ 安全检查完成 - 风险等级: safe, 安全: True
2025-09-18 15:01:23,068 - Digital - [cognitive_modules.memory.semantic_memory.search_by_content] - ℹ️  使用文本搜索: @嫣然  你长什么样？ 生成一张图片来让大家看看
2025-09-18 15:01:23,068 - Digital - [digital_life.process_input] - ℹ️  🔄 数字生命体处理输入: @嫣然  你长什么样？ 生成一张图片来让大家看看...
2025-09-18 15:01:23,073 - Digital - [neural_data_flow_monitor.__init__] - ℹ️  🔥 神经网络数据流监控器初始化完成
2025-09-18 15:01:23,075 - Digital - [cognitive_modules.memory.semantic_memory.search_by_content] - ℹ️  找到 0 个记忆文件
2025-09-18 15:01:23,091 - Digital - [neural_consciousness.save_model] - ℹ️  神经网络模型已保存到: /root/yanran_digital_life/data/neural_models/consciousness_enhancer.pkl
2025-09-18 15:01:23,226 - Digital - [thinking_chain.process] - ℹ️  开始思维链路处理: Zhang@San
2025-09-18 15:01:23,355 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[perception]: 感知处理
2025-09-18 15:01:23,356 - Digital - [intent_recognition.process] - ℹ️  开始意图识别: @嫣然  你长什么样？ 生成一张图片来让大家看看...
2025-09-18 15:01:23,356 - Digital - [intent_recognition.analyze_intent] - ℹ️  🧠 开始两层意图识别: @嫣然  你长什么样？ 生成一张图片来让大家看看...
2025-09-18 15:01:23,356 - Digital - [intent_recognition.analyze_intent] - ℹ️  🔍 第一层：关键词匹配识别
2025-09-18 15:01:23,357 - Digital - [intent_recognition.analyze_intent] - ℹ️  🤖 第一层未匹配，启动第二层：AI语义分析
2025-09-18 15:01:23,357 - Digital - [cognitive_modules.perception.intention_system.success] - ✅ AI服务适配器获取完成
2025-09-18 15:01:23,357 - Digital - [cognitive_modules.perception.intention_system.success] - ✅ 意图分析系统初始化完成
2025-09-18 15:01:23,804 - Digital - [advanced_neural_consciousness.save_memory] - ℹ️  💾 记忆网络数据已保存到: /root/yanran_digital_life/data/neural_models/consciousness_memory.json
2025-09-18 15:01:24,038 - Digital - [ai_safety_filter.filter_input] - ℹ️  🛡️ 安全过滤完成 - 风险等级: safe, 安全: True
2025-09-18 15:01:24,038 - Digital - [main.enhanced_process_message] - ℹ️  🛡️ 安全检查完成 - 风险等级: safe, 安全: True
2025-09-18 15:01:24,040 - Digital - [cognitive_modules.memory.semantic_memory.search_by_content] - ℹ️  使用文本搜索: 快手光合文旅X画爷爷工作室，锅气里的甘肃，趁热，请！（制作：AI画中画、山雨、干饭的星辰；剪辑：Pp）m
2025-09-18 15:01:24,040 - Digital - [cognitive_modules.memory.semantic_memory.search_by_content] - ℹ️  找到 0 个记忆文件
2025-09-18 15:01:24,042 - Digital - [digital_life.process_input] - ℹ️  🔄 数字生命体处理输入: 快手光合文旅X画爷爷工作室，锅气里的甘肃，趁热，请！（制作：AI画中画、山雨、干饭的星辰；剪辑：Pp...
2025-09-18 15:01:24,176 - Digital - [thinking_chain.process] - ℹ️  开始思维链路处理: Li@Si
2025-09-18 15:01:24,305 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[perception]: 感知处理
2025-09-18 15:01:24,306 - Digital - [intent_recognition.process] - ℹ️  开始意图识别: 快手光合文旅X画爷爷工作室，锅气里的甘肃，趁热，请！（制作：...
2025-09-18 15:01:24,307 - Digital - [intent_recognition.analyze_intent] - ℹ️  🧠 开始两层意图识别: 快手光合文旅X画爷爷工作室，锅气里的甘肃，趁热，请！（制作：...
2025-09-18 15:01:24,307 - Digital - [intent_recognition.analyze_intent] - ℹ️  🔍 第一层：关键词匹配识别
2025-09-18 15:01:24,307 - Digital - [intent_recognition.analyze_intent] - ℹ️  🤖 第一层未匹配，启动第二层：AI语义分析
2025-09-18 15:01:25,580 - Digital - [intent_recognition.success] - ✅ ✅ 第二层AI分析成功 - 意图: 绘画创作
2025-09-18 15:01:25,580 - Digital - [intent_recognition._finalize_intent_result] - ℹ️  🎯 意图识别完成: drawing - 绘画创作 (置信度: 1.00) [ai_semantic_analysis]
2025-09-18 15:01:25,582 - Digital - [intent_context_manager.success] - ✅ Redis连接成功 (数据库 1)
2025-09-18 15:01:25,582 - Digital - [intent_context_manager.success] - ✅ 意图上下文管理器初始化完成
2025-09-18 15:01:25,583 - Digital - [intent_context_manager.add_intent_context] - ℹ️  添加意图上下文: 用户=zhangsan, 意图=绘画创作
2025-09-18 15:01:25,583 - Digital - [intent_recognition._finalize_intent_result] - ℹ️  绘画意图识别完成，将通过技能执行阶段统一处理，避免重复调用
2025-09-18 15:01:25,583 - Digital - [drawing_skill.execute] - ❌ 🎨 [drawing_d41d8cd9] 绘画提示词为空，无法执行绘画
2025-09-18 15:01:25,584 - Digital - [intent_recognition.success] - ✅ 意图识别完成: drawing - 绘画创作 (置信度: 1.00)
2025-09-18 15:01:25,584 - Digital - [thinking_chain.success] - ✅ 步骤[perception]执行完成
2025-09-18 15:01:25,584 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[initial_decision]: 初步决策
2025-09-18 15:01:25,585 - Digital - [thinking_chain.success] - ✅ 步骤[initial_decision]执行完成
2025-09-18 15:01:25,585 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[skill_execution]: 技能执行
2025-09-18 15:01:25,586 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  🚀 为用户 lisi 构建增强动态上下文...
2025-09-18 15:01:27,092 - Digital - [adapters.legacy.success] - ✅ 从向量库获取到 5 条用户消息, 5 条助手消息
2025-09-18 15:01:27,365 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  ✅ 最终确定的用户名: 《AI魔法詹学院》群友：AI画中画
2025-09-18 15:01:27,950 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  🚀 开始构建增强动态上下文2.0...
2025-09-18 15:01:27,954 - Digital - [adapters.enhanced_context_builder.success] - ✅ ✅ 增强动态上下文2.0构建成功！
2025-09-18 15:01:27,954 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  📊 组件可用性: 10/13 个组件可用
2025-09-18 15:01:30,604 - Digital - [neural_trigger_system._execute_trigger] - ℹ️  🔥 执行触发器: low_load_threshold - advanced_neural_learning
2025-09-18 15:01:31,796 - Digital - [intent_recognition.success] - ✅ ✅ 第二层AI分析成功 - 意图: 艺术或音乐相关请求
2025-09-18 15:01:31,796 - Digital - [intent_recognition._finalize_intent_result] - ℹ️  🎯 意图识别完成: music - 艺术或音乐相关请求 (置信度: 0.90) [ai_semantic_analysis]
2025-09-18 15:01:31,797 - Digital - [intent_context_manager.add_intent_context] - ℹ️  添加意图上下文: 用户=lisi, 意图=艺术或音乐相关请求
2025-09-18 15:01:31,797 - Digital - [skill_manager.warning_status] - ⚠️  未找到对应意图的技能: 艺术或音乐相关请求
2025-09-18 15:01:31,797 - Digital - [intent_recognition.success] - ✅ 意图识别完成: music - 艺术或音乐相关请求 (置信度: 0.90)
2025-09-18 15:01:31,798 - Digital - [thinking_chain.success] - ✅ 步骤[perception]执行完成
2025-09-18 15:01:31,798 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[initial_decision]: 初步决策
2025-09-18 15:01:31,799 - Digital - [thinking_chain.success] - ✅ 步骤[initial_decision]执行完成
2025-09-18 15:01:31,799 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[skill_execution]: 技能执行
2025-09-18 15:01:31,799 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  🚀 为用户 lisi 构建增强动态上下文...
2025-09-18 15:01:33,194 - Digital - [adapters.legacy.success] - ✅ 从向量库获取到 5 条用户消息, 5 条助手消息
2025-09-18 15:01:33,458 - Digital - [adapters.enhanced_context_builder.build_context_from_user_input] - ℹ️  ✅ 最终确定的用户名: 《AI魔法詹学院》群友：AI画中画
2025-09-18 15:01:33,909 - Digital - [thinking_chain.success] - ✅ 步骤[skill_execution]执行完成
2025-09-18 15:01:33,909 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[entity_extraction]: 实体提取
2025-09-18 15:01:33,909 - Digital - [cognitive_modules.perception.entity_extraction.process] - ℹ️  执行实体提取处理
2025-09-18 15:01:33,913 - Digital - [cognitive_modules.perception.entity_extraction.process] - ℹ️  提取实体 1 个
2025-09-18 15:01:33,914 - Digital - [thinking_chain.success] - ✅ 步骤[entity_extraction]执行完成
2025-09-18 15:01:33,914 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[memory_retrieval]: 记忆检索
2025-09-18 15:01:33,915 - Digital - [cognitive_modules.memory.retrieval.process] - ℹ️  执行记忆检索处理
2025-09-18 15:01:33,915 - Digital - [cognitive_modules.memory.retrieval._retrieve_memories] - ℹ️  未找到相关记忆，返回基础记忆
2025-09-18 15:01:33,915 - Digital - [cognitive_modules.memory.retrieval.process] - ℹ️  检索到 2 条相关记忆
2025-09-18 15:01:33,915 - Digital - [thinking_chain.success] - ✅ 步骤[memory_retrieval]执行完成
2025-09-18 15:01:33,915 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[memory_association]: 记忆关联
2025-09-18 15:01:33,916 - Digital - [cognitive_modules.memory.association.process] - ℹ️  执行记忆关联处理
2025-09-18 15:01:33,916 - Digital - [cognitive_modules.memory.association.process] - ℹ️  构建了 1 个记忆关联
2025-09-18 15:01:33,916 - Digital - [thinking_chain.success] - ✅ 步骤[memory_association]执行完成
2025-09-18 15:01:33,916 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[emotion_analysis]: 情感分析
2025-09-18 15:01:33,917 - Digital - [cognitive_modules.emotion.state_analysis.process] - ℹ️  执行情感状态分析处理
2025-09-18 15:01:33,917 - Digital - [cognitive_modules.emotion.state_analysis.process] - ℹ️  情感分析结果: neutral, 强度: 0.10
2025-09-18 15:01:33,917 - Digital - [thinking_chain.success] - ✅ 步骤[emotion_analysis]执行完成
2025-09-18 15:01:33,917 - Digital - [thinking_chain._execute_steps_async] - ℹ️  步骤[cognitive_reasoning]满足跳过条件，跳过执行
2025-09-18 15:01:33,917 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[emotion_modulation]: 情感调节
2025-09-18 15:01:33,918 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  执行情感响应调节处理
2025-09-18 15:01:33,918 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  没有情感状态信息，使用默认中性情感
2025-09-18 15:01:33,918 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  情感调节结果: balanced_moderate
2025-09-18 15:01:33,918 - Digital - [thinking_chain.success] - ✅ 步骤[emotion_modulation]执行完成
2025-09-18 15:01:33,918 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[final_decision]: 最终决策
2025-09-18 15:01:33,919 - Digital - [autonomy.conscious_decision.success] - ✅ ✅ 检测到有效技能结果: chat_skill, 内容长度: 19
2025-09-18 15:01:33,919 - Digital - [autonomy.conscious_decision.success] - ✅ 聊天技能 chat_skill 已执行成功，检查结果类型
2025-09-18 15:01:33,919 - Digital - [autonomy.conscious_decision.success] - ✅ 聊天技能返回AI响应: 锅气里的甘肃听着就香 这组合有点东西啊...
2025-09-18 15:01:33,919 - Digital - [thinking_chain.success] - ✅ 步骤[final_decision]执行完成
2025-09-18 15:01:33,919 - Digital - [thinking_chain.success] - ✅ 思维链路执行成功完成
2025-09-18 15:01:33,919 - Digital - [thinking_chain._finalize_execution] - ℹ️  思维链路执行耗时: 9.88秒
2025-09-18 15:01:33,920 - Digital - [thinking_chain.success] - ✅ 思维链路处理完成: Zhang@San
2025-09-18 15:01:33,920 - Digital - [digital_life.success] - ✅ ✅ 数字生命体处理完成
2025-09-18 15:01:33,920 - Digital - [main.enhanced_process_message] - ℹ️  💬 使用process_input方法处理完成
2025-09-18 15:01:33,921 - Digital - [main.success] - ✅ 🎯 [req_e1bc0dad4a2f] 请求处理完成统计:
2025-09-18 15:01:33,921 - Digital - [main.success] - ✅    - 用户: zhangsan (🔆)
2025-09-18 15:01:33,921 - Digital - [main.success] - ✅    - 线程: 140149091145472
2025-09-18 15:01:33,921 - Digital - [main.success] - ✅    - 处理时间: 10.85秒
2025-09-18 15:01:33,921 - Digital - [main.success] - ✅    - 响应长度: 19字符
2025-09-18 15:01:33,921 - Digital - [main.success] - ✅    - 会话: Zhang@San
2025-09-18 15:01:33,921 - Digital - [main.chat_message_api] - ℹ️  API聊天响应: 锅气里的甘肃听着就香 这组合有点东西啊...
2025-09-18 15:01:33,922 - Digital - [werkzeug._log] - ℹ️  127.0.0.1 - - [18/Sep/2025 15:01:33] "POST /api/chat/message HTTP/1.1" 200 -
2025-09-18 15:01:34,091 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  🚀 开始构建增强动态上下文2.0...
2025-09-18 15:01:34,095 - Digital - [adapters.enhanced_context_builder.success] - ✅ ✅ 增强动态上下文2.0构建成功！
2025-09-18 15:01:34,095 - Digital - [adapters.enhanced_context_builder.build_enhanced_dynamic_context] - ℹ️  📊 组件可用性: 10/13 个组件可用
2025-09-18 15:01:40,180 - Digital - [thinking_chain.success] - ✅ 步骤[skill_execution]执行完成
2025-09-18 15:01:40,180 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[entity_extraction]: 实体提取
2025-09-18 15:01:40,181 - Digital - [cognitive_modules.perception.entity_extraction.process] - ℹ️  执行实体提取处理
2025-09-18 15:01:40,181 - Digital - [cognitive_modules.perception.entity_extraction.process] - ℹ️  提取实体 1 个
2025-09-18 15:01:40,182 - Digital - [thinking_chain.success] - ✅ 步骤[entity_extraction]执行完成
2025-09-18 15:01:40,182 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[memory_retrieval]: 记忆检索
2025-09-18 15:01:40,182 - Digital - [cognitive_modules.memory.retrieval.process] - ℹ️  执行记忆检索处理
2025-09-18 15:01:40,182 - Digital - [cognitive_modules.memory.retrieval._retrieve_memories] - ℹ️  未找到相关记忆，返回基础记忆
2025-09-18 15:01:40,183 - Digital - [cognitive_modules.memory.retrieval.process] - ℹ️  检索到 2 条相关记忆
2025-09-18 15:01:40,183 - Digital - [thinking_chain.success] - ✅ 步骤[memory_retrieval]执行完成
2025-09-18 15:01:40,183 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[memory_association]: 记忆关联
2025-09-18 15:01:40,183 - Digital - [cognitive_modules.memory.association.process] - ℹ️  执行记忆关联处理
2025-09-18 15:01:40,184 - Digital - [cognitive_modules.memory.association.process] - ℹ️  构建了 1 个记忆关联
2025-09-18 15:01:40,184 - Digital - [thinking_chain.success] - ✅ 步骤[memory_association]执行完成
2025-09-18 15:01:40,184 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[emotion_analysis]: 情感分析
2025-09-18 15:01:40,184 - Digital - [cognitive_modules.emotion.state_analysis.process] - ℹ️  执行情感状态分析处理
2025-09-18 15:01:40,184 - Digital - [cognitive_modules.emotion.state_analysis.process] - ℹ️  情感分析结果: neutral, 强度: 0.10
2025-09-18 15:01:40,185 - Digital - [thinking_chain.success] - ✅ 步骤[emotion_analysis]执行完成
2025-09-18 15:01:40,185 - Digital - [thinking_chain._execute_steps_async] - ℹ️  步骤[cognitive_reasoning]满足跳过条件，跳过执行
2025-09-18 15:01:40,185 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[emotion_modulation]: 情感调节
2025-09-18 15:01:40,186 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  执行情感响应调节处理
2025-09-18 15:01:40,186 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  没有情感状态信息，使用默认中性情感
2025-09-18 15:01:40,186 - Digital - [cognitive_modules.emotion.response_modulation.process] - ℹ️  情感调节结果: balanced_moderate
2025-09-18 15:01:40,186 - Digital - [thinking_chain.success] - ✅ 步骤[emotion_modulation]执行完成
2025-09-18 15:01:40,187 - Digital - [thinking_chain._execute_steps_async] - ℹ️  开始执行步骤[final_decision]: 最终决策
2025-09-18 15:01:40,187 - Digital - [autonomy.conscious_decision.success] - ✅ ✅ 检测到有效技能结果: chat_skill, 内容长度: 26
2025-09-18 15:01:40,187 - Digital - [autonomy.conscious_decision.success] - ✅ 聊天技能 chat_skill 已执行成功，检查结果类型
2025-09-18 15:01:40,187 - Digital - [autonomy.conscious_decision.success] - ✅ 聊天技能返回AI响应: 甘肃的锅气隔着屏幕都窜鼻子 你们这波把烟火气熬出魂了...
2025-09-18 15:01:40,187 - Digital - [thinking_chain.success] - ✅ 步骤[final_decision]执行完成
2025-09-18 15:01:40,188 - Digital - [thinking_chain._finalize_execution] - ℹ️  思维链路执行耗时: 16.15秒
2025-09-18 15:01:40,188 - Digital - [thinking_chain.success] - ✅ 思维链路处理完成: Li@Si
2025-09-18 15:01:40,188 - Digital - [digital_life.success] - ✅ ✅ 数字生命体处理完成
2025-09-18 15:01:40,188 - Digital - [main.enhanced_process_message] - ℹ️  💬 使用process_input方法处理完成
2025-09-18 15:01:40,189 - Digital - [main.success] - ✅ 🎯 [req_8aabc967a2b8] 请求处理完成统计:
2025-09-18 15:01:40,189 - Digital - [main.success] - ✅    - 用户: lisi (《AI魔法詹学院》群友：AI画中画)
2025-09-18 15:01:40,189 - Digital - [main.success] - ✅    - 线程: 140149007218432
2025-09-18 15:01:40,189 - Digital - [main.success] - ✅    - 处理时间: 16.15秒
2025-09-18 15:01:40,189 - Digital - [main.success] - ✅    - 响应长度: 26字符
2025-09-18 15:01:40,189 - Digital - [main.success] - ✅    - 会话: Li@Si
2025-09-18 15:01:40,190 - Digital - [main.chat_message_api] - ℹ️  API聊天响应: 甘肃的锅气隔着屏幕都窜鼻子 你们这波把烟火气熬出魂了..