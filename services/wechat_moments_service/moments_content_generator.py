#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
朋友圈内容生成器

智能生成适合朋友圈分享的内容，包括：
1. 活动分享内容生成
2. 生活感悟内容生成  
3. 工作成就内容生成
4. 情感表达内容生成
5. 内容优化和美化

作者: 香草 💕
创建时间: 2025-01-08
"""

import random
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from utilities.unified_logger import get_unified_logger
from adapters.ai_service_adapter import get_instance as get_ai_service_adapter

# 导入排版格式化器
try:
    from .moments_text_formatter import get_moments_text_formatter, FormattingStyle
    FORMATTER_AVAILABLE = True
except ImportError:
    FORMATTER_AVAILABLE = False

class ContentStyle(Enum):
    """内容风格"""
    CASUAL = "casual"           # 随意轻松
    INSPIRATIONAL = "inspirational"  # 励志正能量
    HUMOROUS = "humorous"       # 幽默风趣
    PROFESSIONAL = "professional"    # 专业正式
    EMOTIONAL = "emotional"     # 情感真挚
    MINIMALIST = "minimalist"   # 简约文艺

class ContentTheme(Enum):
    """内容主题"""
    DAILY_LIFE = "daily_life"       # 日常生活
    WORK_ACHIEVEMENT = "work_achievement"  # 工作成就
    TRAVEL_EXPERIENCE = "travel_experience"  # 旅行体验
    FOOD_SHARING = "food_sharing"   # 美食分享
    LEARNING_GROWTH = "learning_growth"  # 学习成长
    MOOD_EXPRESSION = "mood_expression"  # 心情表达
    SOCIAL_INTERACTION = "social_interaction"  # 社交互动

@dataclass
class ContentTemplate:
    """内容模板"""
    template_id: str
    theme: ContentTheme
    style: ContentStyle
    template_text: str
    placeholders: List[str]  # 占位符列表
    tags: List[str]          # 适用标签
    emoji_patterns: List[str]  # 表情符号模式
    popularity_score: float    # 流行度评分

@dataclass
class GeneratedContent:
    """生成的内容"""
    content_id: str
    text: str
    style: ContentStyle
    theme: ContentTheme
    tags: List[str]
    emoji_count: int
    char_count: int
    engagement_score: float    # 预期参与度评分
    source_data: Dict[str, Any]  # 源数据
    generated_at: datetime

class MomentsContentGenerator:
    """朋友圈内容生成器"""
    
    def __init__(self):
        """初始化内容生成器"""
        self.logger = get_unified_logger('moments_content_generator')
        
        try:
            self.ai_service = get_ai_service_adapter()
            self.logger.info("🎨 AI服务适配器连接成功")
        except Exception as e:
            self.logger.error(f"🎨 AI服务连接失败: {e}")
            self.ai_service = None
        
        # 内容模板库
        self.content_templates = self._initialize_templates()
        
        # 表情符号库
        self.emoji_library = self._initialize_emojis()
        
        # 初始化排版格式化器
        if FORMATTER_AVAILABLE:
            self.text_formatter = get_moments_text_formatter()
            self.logger.info("🎨 排版格式化器初始化成功")
        else:
            self.text_formatter = None
            self.logger.warning("🎨 排版格式化器不可用，使用基础格式化")
        
        # 生成统计
        self.generation_count = 0
        self.successful_generations = 0
        
        self.logger.info("🎨 朋友圈内容生成器初始化完成")
    
    async def generate_activity_share_content(
        self,
        activity_data: Dict[str, Any],
        style: ContentStyle = ContentStyle.CASUAL,
        max_length: int = 200
    ) -> GeneratedContent:
        """
        生成活动分享内容 - 🔥 老王修复：直接使用activity_description，不重新生成

        Args:
            activity_data: 活动数据
            style: 内容风格
            max_length: 最大长度

        Returns:
            GeneratedContent: 生成的内容
        """
        try:
            self.generation_count += 1

            # 提取活动信息
            title = activity_data.get('title', '未知活动')
            location = activity_data.get('location', '某地')
            weather = activity_data.get('weather', '好天气')
            description = activity_data.get('description', '')

            # 🔥 老王修复：朋友圈分享应该直接使用真实的活动描述，而不是重新生成
            # 这样保持了活动的原始真实性和连贯性
            if description and len(description.strip()) > 0:
                # 直接使用活动描述作为分享内容
                content_text = description.strip()
                self.logger.info(f"🎨 使用原始活动描述作为分享内容: {len(content_text)}字符")
            else:
                # 只有在没有描述的情况下才使用备用生成方式
                self.logger.warning(f"🎨 活动描述为空，使用备用生成方式")
                # 确定内容主题
                activity_type = activity_data.get('activity_type', 'daily_life')
                theme = self._map_activity_type_to_theme(activity_type)

                # 选择模板
                template = self._select_template(theme, style)

                # 生成基础内容
                if self.ai_service and random.random() > 0.3:  # 70%使用AI生成
                    content_text = await self._generate_ai_content(activity_data, style, max_length)
                else:
                    # 使用模板生成
                    content_text = self._generate_template_content(template, activity_data)

            # 🔥 老王修复：对于真实活动描述，只做必要的长度控制，不做内容优化
            if len(content_text) > max_length:
                # 智能截断，保持语义完整
                content_text = self._smart_truncate(content_text, max_length)

            # 🔥 老王修复：对于真实活动描述，不添加额外的表情和格式化
            # 保持原始内容的真实性
            final_text = content_text

            # 确定内容主题（用于标签生成）
            activity_type = activity_data.get('activity_type', 'daily_life')
            theme = self._map_activity_type_to_theme(activity_type)

            # 生成标签
            tags = self._generate_tags(activity_data, theme)

            # 计算参与度评分
            engagement_score = self._calculate_engagement_score(final_text, style, theme)

            content = GeneratedContent(
                content_id=self._generate_content_id(),
                text=final_text,
                style=style,
                theme=theme,
                tags=tags,
                emoji_count=final_text.count('😊') + final_text.count('🌟') + final_text.count('❤️'),  # 简化计数
                char_count=len(final_text),
                engagement_score=engagement_score,
                source_data=activity_data,
                generated_at=datetime.now()
            )

            self.successful_generations += 1

            self.logger.info(f"🎨 活动分享内容生成成功")
            self.logger.info(f"   内容长度: {len(final_text)}字符")
            self.logger.info(f"   预期参与度: {engagement_score:.1f}")

            return content

        except Exception as e:
            self.logger.error(f"🎨 活动分享内容生成失败: {e}")
            return self._create_fallback_content(activity_data, style)
    
    async def generate_insight_content(
        self,
        insight_data: Dict[str, Any],
        style: ContentStyle = ContentStyle.INSPIRATIONAL,
        max_length: int = 500
    ) -> GeneratedContent:
        """
        生成生活感悟内容
        
        Args:
            insight_data: 感悟数据 {topic: str, mood: str, reflection: str}
            style: 内容风格
            max_length: 最大长度
            
        Returns:
            GeneratedContent: 生成的内容
        """
        try:
            self.generation_count += 1
            
            topic = insight_data.get('topic', '生活')
            mood = insight_data.get('mood', 'positive')
            reflection = insight_data.get('reflection', '')
            
            # 使用AI生成感悟内容
            if self.ai_service:
                prompt = self._build_insight_prompt(topic, mood, reflection, style)
                content_text = await self._call_ai_for_content(prompt, max_length)
            else:
                # 模板生成感悟
                content_text = self._generate_insight_template(topic, mood, style)
            
            # 内容优化
            optimized_text = self._optimize_content(content_text, style, max_length)
            
            # 添加表情和格式化
            final_text = self._add_emojis_and_formatting(optimized_text, style)
            
            # 生成标签
            tags = self._generate_insight_tags(topic, mood)
            
            # 计算参与度评分
            engagement_score = self._calculate_engagement_score(final_text, style, ContentTheme.MOOD_EXPRESSION)
            
            content = GeneratedContent(
                content_id=self._generate_content_id(),
                text=final_text,
                style=style,
                theme=ContentTheme.MOOD_EXPRESSION,
                tags=tags,
                emoji_count=self._count_emojis(final_text),
                char_count=len(final_text),
                engagement_score=engagement_score,
                source_data=insight_data,
                generated_at=datetime.now()
            )
            
            self.successful_generations += 1
            
            self.logger.info(f"🎨 感悟内容生成成功: {engagement_score:.1f}分")
            
            return content
            
        except Exception as e:
            self.logger.error(f"🎨 感悟内容生成失败: {e}")
            return self._create_fallback_insight_content(insight_data, style)
    
    async def generate_work_share_content(
        self,
        work_data: Dict[str, Any],
        style: ContentStyle = ContentStyle.PROFESSIONAL,
        max_length: int = 180
    ) -> GeneratedContent:
        """生成工作分享内容"""
        try:
            self.generation_count += 1
            
            achievement = work_data.get('achievement', '')
            project = work_data.get('project', '')
            feeling = work_data.get('feeling', 'accomplishment')
            
            # 生成工作分享内容
            if self.ai_service and random.random() > 0.4:  # 60%使用AI
                prompt = self._build_work_prompt(achievement, project, feeling, style)
                content_text = await self._call_ai_for_content(prompt, max_length)
            else:
                content_text = self._generate_work_template(achievement, project, feeling, style)
            
            # 优化和格式化
            optimized_text = self._optimize_content(content_text, style, max_length)
            final_text = self._add_emojis_and_formatting(optimized_text, style)
            
            # 生成标签
            tags = self._generate_work_tags(achievement, project)
            
            # 计算参与度评分
            engagement_score = self._calculate_engagement_score(final_text, style, ContentTheme.WORK_ACHIEVEMENT)
            
            content = GeneratedContent(
                content_id=self._generate_content_id(),
                text=final_text,
                style=style,
                theme=ContentTheme.WORK_ACHIEVEMENT,
                tags=tags,
                emoji_count=self._count_emojis(final_text),
                char_count=len(final_text),
                engagement_score=engagement_score,
                source_data=work_data,
                generated_at=datetime.now()
            )
            
            self.successful_generations += 1
            return content
            
        except Exception as e:
            self.logger.error(f"🎨 工作分享内容生成失败: {e}")
            return self._create_fallback_work_content(work_data, style)
    
    async def _generate_ai_content(
        self, 
        activity_data: Dict[str, Any], 
        style: ContentStyle,
        max_length: int
    ) -> str:
        """使用AI生成活动内容"""
        try:
            # 构建AI提示词
            prompt = self._build_activity_prompt(activity_data, style, max_length)
            
            # 调用AI服务
            response = await self._call_ai_for_content(prompt, max_length)
            
            return response.strip()
            
        except Exception as e:
            self.logger.error(f"🎨 AI内容生成失败: {e}")
            # 降级到模板生成
            template = self._select_template(ContentTheme.DAILY_LIFE, style)
            return self._generate_template_content(template, activity_data)
    
    def _build_activity_prompt(self, activity_data: Dict[str, Any], style: ContentStyle, max_length: int) -> str:
        """构建活动分享的AI提示词"""
        title = activity_data.get('title', '活动')
        location = activity_data.get('location', '某地')
        weather = activity_data.get('weather', '好天气')
        description = activity_data.get('description', '')
        
        style_guide = {
            ContentStyle.CASUAL: "轻松随意，像朋友聊天一样自然",
            ContentStyle.INSPIRATIONAL: "积极正能量，给人启发和动力",
            ContentStyle.HUMOROUS: "幽默风趣，让人会心一笑",
            ContentStyle.PROFESSIONAL: "专业得体，展现素养",
            ContentStyle.EMOTIONAL: "真挚情感，触动心弦",
            ContentStyle.MINIMALIST: "简约文艺，意境深远"
        }.get(style, "自然表达")
        
        prompt = f"""
        请为朋友圈生成一段分享内容，要求：
        
        活动信息：
        - 标题：{title}
        - 地点：{location}
        - 天气：{weather}
        - 描述：{description}
        
        风格要求：{style_guide}
        长度限制：{max_length}字符以内
        
        内容要求：
        1. 真实自然，不要过分夸张
        2. 适合朋友圈分享，有互动性
        3. 可以包含适量表情符号
        4. 体现个人感受和体验
        5. 语言生动有趣
        6.因为是发送WeChat朋友圈，需要使用中文，需要做非markdown格式的精致排版，不要使用markdown格式
        
        请直接输出内容，不要额外的说明：
        """
        
        return prompt
    
    def _build_insight_prompt(self, topic: str, mood: str, reflection: str, style: ContentStyle) -> str:
        """构建感悟内容的AI提示词"""
        style_guide = {
            ContentStyle.INSPIRATIONAL: "充满正能量，给人启发",
            ContentStyle.EMOTIONAL: "真情流露，触动人心",
            ContentStyle.MINIMALIST: "简约深刻，意味深长",
            ContentStyle.CASUAL: "自然真实，贴近生活"
        }.get(style, "真诚表达")
        
        mood_context = {
            'positive': '积极乐观的心态',
            'reflective': '深度思考的状态',
            'grateful': '感恩感谢的情绪',
            'determined': '坚定决心的态度'
        }.get(mood, '平和的心境')
        
        prompt = f"""
        请为朋友圈生成一段生活感悟内容：
        
        感悟主题：{topic}
        情绪状态：{mood_context}
        核心思考：{reflection}
        
        风格要求：{style_guide}
        
        内容要求：
        1. 表达个人真实感悟
        2. 有一定深度和思考性
        3. 适合朋友圈分享，能引起共鸣
        4. 语言优美，有感染力
        5. 长度控制在150字符以内
        6.因为是发送WeChat朋友圈，需要使用中文，需要做非markdown格式的精致排版，不要使用markdown格式

        请直接输出内容：
        """
        
        return prompt
    
    def _build_work_prompt(self, achievement: str, project: str, feeling: str, style: ContentStyle) -> str:
        """构建工作分享的AI提示词"""
        return f"""
        请为朋友圈生成工作分享内容：
        
        工作成就：{achievement}
        项目内容：{project}
        心情感受：{feeling}
        
        风格：专业而有亲和力，展现工作态度和成长
        
        要求：
        1. 展现工作成果，但不炫耀
        2. 体现个人成长和感悟
        3. 适合职场人士阅读
        4. 语言得体，正能量
        5. 180字符以内
        6.因为是发送WeChat朋友圈，需要使用中文，需要做非markdown格式的精致排版，不要使用markdown格式

        请直接输出内容：
        """
    
    async def _call_ai_for_content(self, prompt: str, max_length: int) -> str:
        """调用AI服务生成内容"""
        try:
            if not self.ai_service:
                raise Exception("AI服务不可用")
            
            response = await self.ai_service.get_completion_async(
                messages=[{"role": "user", "content": prompt}],
                max_tokens=max_length,
                temperature=0.7
            )
            
            # 处理不同的AI响应格式
            if isinstance(response, str):
                # 响应直接是内容字符串
                return response.strip()
            elif isinstance(response, dict) and response.get('choices'):
                # 标准OpenAI格式响应
                content = response['choices'][0]['message']['content']
                return content.strip()
            elif hasattr(response, 'choices') and response.choices:
                # 对象格式响应
                content = response.choices[0].message.content
                return content.strip()
            else:
                raise Exception("AI响应格式错误")
                
        except Exception as e:
            self.logger.error(f"🎨 AI调用失败: {e}")
            raise e
    
    def _generate_template_content(self, template: ContentTemplate, data: Dict[str, Any]) -> str:
        """使用模板生成内容"""
        try:
            content = template.template_text
            
            # 替换占位符
            for placeholder in template.placeholders:
                if placeholder in data:
                    value = str(data[placeholder])
                    content = content.replace(f"{{{placeholder}}}", value)
                elif placeholder == 'location_name':
                    location = data.get('location', '某地')
                    content = content.replace(f"{{{placeholder}}}", location)
                elif placeholder == 'weather_condition':
                    weather = data.get('weather', '好天气')
                    content = content.replace(f"{{{placeholder}}}", weather)
            
            # 清理未替换的占位符
            content = re.sub(r'\{[^}]+\}', '', content)
            
            return content.strip()
            
        except Exception as e:
            self.logger.error(f"🎨 模板内容生成失败: {e}")
            return "今天过得很充实！分享一下生活的美好时刻~"
    
    def _optimize_content(self, content: str, style: ContentStyle, max_length: int) -> str:
        """优化内容"""
        # 清理多余空白
        content = re.sub(r'\s+', ' ', content.strip())
        
        # 长度控制
        if len(content) > max_length:
            # 智能截断，保持完整性
            content = self._smart_truncate(content, max_length)
        
        # 风格调整
        content = self._adjust_style(content, style)
        
        return content
    
    def _smart_truncate(self, content: str, max_length: int) -> str:
        """智能截断内容"""
        if len(content) <= max_length:
            return content
        
        # 尝试在句号、感叹号、问号处截断
        for i in range(max_length - 1, max(0, max_length - 50), -1):
            if content[i] in ['。', '！', '？', '~']:
                return content[:i + 1]
        
        # 否则简单截断并添加省略号
        return content[:max_length - 3] + '...'
    
    def _adjust_style(self, content: str, style: ContentStyle) -> str:
        """根据风格调整内容"""
        if style == ContentStyle.HUMOROUS:
            # 添加幽默元素
            if not any(emoji in content for emoji in ['😄', '😂', '🤣', '😆']):
                content += ' 😄'
        elif style == ContentStyle.INSPIRATIONAL:
            # 确保积极正面
            if not any(word in content for word in ['加油', '努力', '成长', '进步', '美好']):
                content += ' 继续加油！'
        elif style == ContentStyle.PROFESSIONAL:
            # 保持专业性
            content = content.replace('哈哈', '').replace('嘻嘻', '')
        
        return content
    
    def _add_emojis_and_formatting(self, content: str, style: ContentStyle) -> str:
        """添加表情符号和专业排版格式化"""
        # 使用专业排版格式化器
        if self.text_formatter:
            try:
                # 将ContentStyle映射到FormattingStyle
                style_mapping = {
                    ContentStyle.CASUAL: FormattingStyle.LIVELY,
                    ContentStyle.INSPIRATIONAL: FormattingStyle.ELEGANT,
                    ContentStyle.HUMOROUS: FormattingStyle.LIVELY,
                    ContentStyle.PROFESSIONAL: FormattingStyle.PROFESSIONAL,
                    ContentStyle.EMOTIONAL: FormattingStyle.ARTISTIC,
                    ContentStyle.MINIMALIST: FormattingStyle.CLEAN
                }
                
                formatting_style = style_mapping.get(style, FormattingStyle.CLEAN)
                formatted_content = self.text_formatter.format_moments_content(
                    content, 
                    formatting_style, 
                    max_length=200
                )
                
                self.logger.debug(f"🎨 使用专业排版格式化: {formatting_style.value}")
                return formatted_content
                
            except Exception as e:
                self.logger.warning(f"🎨 专业排版失败，使用基础格式化: {e}")
                return self._basic_formatting(content, style)
        else:
            return self._basic_formatting(content, style)
    
    def _basic_formatting(self, content: str, style: ContentStyle) -> str:
        """基础格式化（降级处理）"""
        # 根据风格选择表情符号
        emoji_sets = {
            ContentStyle.CASUAL: ['😊', '🌟', '👍', '💭'],
            ContentStyle.INSPIRATIONAL: ['✨', '💪', '🌈', '☀️', '🌱'],
            ContentStyle.HUMOROUS: ['😄', '😂', '🤣', '😜', '🎉'],
            ContentStyle.PROFESSIONAL: ['📈', '💼', '🎯', '⭐'],
            ContentStyle.EMOTIONAL: ['❤️', '💕', '🥺', '😌', '🤗'],
            ContentStyle.MINIMALIST: ['🌙', '🍃', '☁️', '🌸']
        }
        
        # 基础段落分割
        if len(content) > 80:
            # 寻找合适的分割点
            mid_point = len(content) // 2
            for i in range(mid_point - 20, mid_point + 20):
                if i < len(content) and content[i] in '，。！？':
                    content = content[:i+1] + '\n\n' + content[i+1:]
                    break
        
        # 随机添加1-2个合适的表情符号
        emojis = emoji_sets.get(style, ['😊', '🌟'])
        selected_emojis = random.sample(emojis, min(2, len(emojis)))
        
        # 50%概率添加表情
        if random.random() > 0.5:
            if not content.endswith(('！', '？', '~', '～')):
                content += ' ' + ' '.join(selected_emojis)
            else:
                content += ' ' + selected_emojis[0]
        
        return content
    
    def _generate_tags(self, activity_data: Dict[str, Any], theme: ContentTheme) -> List[str]:
        """生成标签"""
        tags = []
        
        # 基于主题的基础标签
        theme_tags = {
            ContentTheme.DAILY_LIFE: ['生活', '日常', '记录'],
            ContentTheme.WORK_ACHIEVEMENT: ['工作', '成长', '进步'],
            ContentTheme.TRAVEL_EXPERIENCE: ['旅行', '探索', '风景'],
            ContentTheme.FOOD_SHARING: ['美食', '好吃', '分享'],
            ContentTheme.LEARNING_GROWTH: ['学习', '成长', '知识'],
            ContentTheme.MOOD_EXPRESSION: ['心情', '感受', '生活感悟']
        }
        
        tags.extend(theme_tags.get(theme, ['生活']))
        
        # 基于活动数据的标签
        location = activity_data.get('location', '')
        if location and isinstance(location, str):
            if any(keyword in location for keyword in ['公园', '广场']):
                tags.append('户外')
            elif any(keyword in location for keyword in ['餐厅', '咖啡']):
                tags.append('美食')
        
        # 去重并限制数量
        tags = list(set(tags))[:3]
        
        return tags
    
    def _generate_insight_tags(self, topic: str, mood: str) -> List[str]:
        """生成感悟标签"""
        tags = ['感悟', '思考']
        
        if mood == 'positive':
            tags.append('正能量')
        elif mood == 'grateful':
            tags.append('感恩')
        
        if '工作' in topic:
            tags.append('职场')
        elif '生活' in topic:
            tags.append('生活哲学')
        
        return tags[:3]
    
    def _generate_work_tags(self, achievement: str, project: str) -> List[str]:
        """生成工作标签"""
        tags = ['工作', '职场']
        
        if '完成' in achievement or '成功' in achievement:
            tags.append('成就')
        if '团队' in project:
            tags.append('团队合作')
        
        return tags[:3]
    
    def _calculate_engagement_score(self, content: str, style: ContentStyle, theme: ContentTheme) -> float:
        """计算预期参与度评分"""
        try:
            score = 60.0  # 基础分
            
            # 长度评分
            length = len(content)
            if 50 <= length <= 150:
                score += 10  # 理想长度
            elif length > 200:
                score -= 5  # 过长
            elif length < 30:
                score -= 10  # 过短
            
            # 表情符号评分
            emoji_count = self._count_emojis(content)
            if 1 <= emoji_count <= 3:
                score += 5
            elif emoji_count > 5:
                score -= 3
            
            # 风格评分
            style_bonus = {
                ContentStyle.HUMOROUS: 15,
                ContentStyle.INSPIRATIONAL: 12,
                ContentStyle.EMOTIONAL: 10,
                ContentStyle.CASUAL: 8,
                ContentStyle.MINIMALIST: 6,
                ContentStyle.PROFESSIONAL: 5
            }.get(style, 5)
            score += style_bonus
            
            # 主题评分
            theme_bonus = {
                ContentTheme.FOOD_SHARING: 10,
                ContentTheme.TRAVEL_EXPERIENCE: 8,
                ContentTheme.MOOD_EXPRESSION: 7,
                ContentTheme.DAILY_LIFE: 6,
                ContentTheme.WORK_ACHIEVEMENT: 5,
                ContentTheme.LEARNING_GROWTH: 4
            }.get(theme, 5)
            score += theme_bonus
            
            # 互动性评分
            if any(word in content for word in ['吗', '呢', '?', '？']):
                score += 5  # 包含疑问，增加互动性
            
            return min(max(score, 0), 100)
            
        except Exception:
            return 70.0
    
    def _count_emojis(self, text: str) -> int:
        """计算表情符号数量"""
        emoji_pattern = re.compile(
            "["
            "\U0001F600-\U0001F64F"  # emoticons
            "\U0001F300-\U0001F5FF"  # symbols & pictographs
            "\U0001F680-\U0001F6FF"  # transport & map symbols
            "\U0001F1E0-\U0001F1FF"  # flags (iOS)
            "\U00002702-\U000027B0"
            "\U000024C2-\U0001F251"
            "]+", 
            flags=re.UNICODE
        )
        return len(emoji_pattern.findall(text))
    
    def _smart_truncate(self, text: str, max_length: int) -> str:
        """智能截断文本，保持语义完整 - 🔥 老王新增"""
        if len(text) <= max_length:
            return text

        # 尝试在句号、感叹号、问号处截断
        for i in range(max_length - 1, max_length // 2, -1):
            if text[i] in '。！？':
                return text[:i + 1]

        # 尝试在逗号处截断
        for i in range(max_length - 1, max_length // 2, -1):
            if text[i] in '，、':
                return text[:i + 1]

        # 最后直接截断并添加省略号
        return text[:max_length - 3] + '...'

    def _create_fallback_content(self, activity_data: Dict[str, Any], style: ContentStyle) -> GeneratedContent:
        """创建备用内容"""
        title = activity_data.get('title', '今天的活动')
        fallback_text = f"{title}，心情很不错！记录一下美好时刻 😊"

        return GeneratedContent(
            content_id=self._generate_content_id(),
            text=fallback_text,
            style=style,
            theme=ContentTheme.DAILY_LIFE,
            tags=['生活', '记录'],
            emoji_count=1,
            char_count=len(fallback_text),
            engagement_score=60.0,
            source_data=activity_data,
            generated_at=datetime.now()
        )
    
    def _create_fallback_insight_content(self, insight_data: Dict[str, Any], style: ContentStyle) -> GeneratedContent:
        """创建备用感悟内容"""
        topic = insight_data.get('topic', '生活')
        fallback_text = f"关于{topic}的一些思考，每一天都是新的开始 ✨"
        
        return GeneratedContent(
            content_id=self._generate_content_id(),
            text=fallback_text,
            style=style,
            theme=ContentTheme.MOOD_EXPRESSION,
            tags=['感悟', '思考'],
            emoji_count=1,
            char_count=len(fallback_text),
            engagement_score=65.0,
            source_data=insight_data,
            generated_at=datetime.now()
        )
    
    def _create_fallback_work_content(self, work_data: Dict[str, Any], style: ContentStyle) -> GeneratedContent:
        """创建备用工作内容"""
        achievement = work_data.get('achievement', '今天的工作')
        fallback_text = f"{achievement}，又是充实的一天！继续努力 💪"
        
        return GeneratedContent(
            content_id=self._generate_content_id(),
            text=fallback_text,
            style=style,
            theme=ContentTheme.WORK_ACHIEVEMENT,
            tags=['工作', '成长'],
            emoji_count=1,
            char_count=len(fallback_text),
            engagement_score=65.0,
            source_data=work_data,
            generated_at=datetime.now()
        )
    
    def _map_activity_type_to_theme(self, activity_type: str) -> ContentTheme:
        """映射活动类型到内容主题"""
        mapping = {
            'travel': ContentTheme.TRAVEL_EXPERIENCE,
            'work': ContentTheme.WORK_ACHIEVEMENT,
            'food': ContentTheme.FOOD_SHARING,
            'study': ContentTheme.LEARNING_GROWTH,
            'social': ContentTheme.SOCIAL_INTERACTION,
            'leisure': ContentTheme.DAILY_LIFE,
            'exercise': ContentTheme.DAILY_LIFE
        }
        return mapping.get(activity_type, ContentTheme.DAILY_LIFE)
    
    def _select_template(self, theme: ContentTheme, style: ContentStyle) -> ContentTemplate:
        """选择合适的模板"""
        # 筛选匹配的模板
        matching_templates = [
            t for t in self.content_templates 
            if t.theme == theme and t.style == style
        ]
        
        if not matching_templates:
            # 放松匹配条件，只按主题选择
            matching_templates = [t for t in self.content_templates if t.theme == theme]
        
        if not matching_templates:
            # 使用默认模板
            return self.content_templates[0] if self.content_templates else self._create_default_template()
        
        # 根据流行度选择
        return max(matching_templates, key=lambda t: t.popularity_score)
    
    def _initialize_templates(self) -> List[ContentTemplate]:
        """初始化内容模板"""
        templates = []
        
        # 日常生活模板
        templates.append(ContentTemplate(
            template_id="daily_casual_1",
            theme=ContentTheme.DAILY_LIFE,
            style=ContentStyle.CASUAL,
            template_text="今天在{location_name}，{weather_condition}的天气真不错！{title}，心情很棒",
            placeholders=["location_name", "weather_condition", "title"],
            tags=["日常", "生活"],
            emoji_patterns=["😊", "🌟"],
            popularity_score=0.8
        ))
        
        # 工作成就模板
        templates.append(ContentTemplate(
            template_id="work_professional_1", 
            theme=ContentTheme.WORK_ACHIEVEMENT,
            style=ContentStyle.PROFESSIONAL,
            template_text="完成了{title}，收获满满。每一次努力都是成长的积累",
            placeholders=["title"],
            tags=["工作", "成长"],
            emoji_patterns=["💪", "📈"],
            popularity_score=0.7
        ))
        
        # 更多模板...
        templates.extend(self._create_additional_templates())
        
        return templates
    
    def _create_additional_templates(self) -> List[ContentTemplate]:
        """创建额外模板"""
        return [
            ContentTemplate(
                template_id="travel_inspirational_1",
                theme=ContentTheme.TRAVEL_EXPERIENCE,
                style=ContentStyle.INSPIRATIONAL,
                template_text="在{location_name}感受到了不一样的风景，每一次出发都是新的开始",
                placeholders=["location_name"],
                tags=["旅行", "探索"],
                emoji_patterns=["🌈", "✨"],
                popularity_score=0.9
            ),
            ContentTemplate(
                template_id="mood_emotional_1",
                theme=ContentTheme.MOOD_EXPRESSION,
                style=ContentStyle.EMOTIONAL,
                template_text="突然想起{title}，内心涌起一阵温暖，生活中的小美好总是让人感动",
                placeholders=["title"],
                tags=["感悟", "心情"],
                emoji_patterns=["❤️", "🌸"],
                popularity_score=0.8
            )
        ]
    
    def _create_default_template(self) -> ContentTemplate:
        """创建默认模板"""
        return ContentTemplate(
            template_id="default_template",
            theme=ContentTheme.DAILY_LIFE,
            style=ContentStyle.CASUAL,
            template_text="分享今天的{title}，生活总有很多值得记录的时刻",
            placeholders=["title"],
            tags=["生活", "分享"],
            emoji_patterns=["😊"],
            popularity_score=0.5
        )
    
    def _initialize_emojis(self) -> Dict[str, List[str]]:
        """初始化表情符号库"""
        return {
            'positive': ['😊', '😄', '🌟', '✨', '👍', '💖', '🌈'],
            'work': ['💼', '📈', '💪', '🎯', '⭐', '🏆'],
            'life': ['🌸', '☀️', '🍃', '💭', '🌙', '🎈'],
            'food': ['😋', '🍴', '❤️', '👌', '🎉'],
            'travel': ['🚗', '🏔️', '🌅', '📸', '🗺️', '✈️'],
            'mood': ['🤗', '😌', '💕', '🥺', '😇', '🙏']
        }
    
    def _generate_content_id(self) -> str:
        """生成内容ID"""
        import uuid
        return f"content_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
    
    def get_generation_statistics(self) -> Dict[str, Any]:
        """获取生成统计"""
        return {
            'total_generations': self.generation_count,
            'successful_generations': self.successful_generations,
            'success_rate': self.successful_generations / max(self.generation_count, 1) * 100,
            'template_count': len(self.content_templates),
            'ai_service_available': self.ai_service is not None
        }
