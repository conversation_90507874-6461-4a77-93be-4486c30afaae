#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ChromaDB桥接模块 - 严格按照老板示例代码实现

该模块提供与旧代码兼容的search_history接口，
专注于远程ChromaDB向量搜索功能。

作者: Claude
创建日期: 2024-06-10
版本: 1.5
"""

from utilities.unified_logger import get_unified_logger, setup_unified_logging
import requests
import json
import time
from typing import Dict, List, Any, Optional

# 严格按照老板示例代码设置API密钥
API_KEY = "sk-lJ1R156TicBrrU9G2a789f03F5C14515993256Fe306d4691"
EMBEDDING_API_URL = "https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding"
BASE_URL = 'http://124.221.30.195:5001'
CHROMADB_API_KEY = "sk-92bf1dcc3b5d4d9da90b72cd9327c0f2"

# 🔥 老王修复：ChromaDB 1.0.15+ 使用v2 API
API_VERSION = "v2"  # 新版本使用v2 API，v1已废弃

# 全局常量
ChromaDB_database = None

# 配置日志
setup_unified_logging()
logger = get_unified_logger("legacy_chroma_bridge")

def get_embedding(text):
    """获取文本的嵌入向量"""
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {CHROMADB_API_KEY}"
    }
    payload = {
        "model": "text-embedding-v2",
        "input": {
            "texts": [text]
        }
    }
    try:
        response = requests.post(EMBEDDING_API_URL, json=payload, headers=headers)
        response_data = response.json()

        if response.status_code == 200:
            return response_data["output"]["embeddings"][0]["embedding"]
        else:
            logger.error_status(f"Error response: {response.text}")
            return None
    except Exception as e:
        logger.error_status(f"获取嵌入向量失败: {e}")
        return None

def query_collection(collection_id, query_embedding, n_results, role, user_id=None):
    """查询集合 - 🔥 P0修复：添加用户ID隔离"""
    if not query_embedding:
        logger.error_status("嵌入向量为空，无法查询")
        return create_empty_result(n_results)

    try:
        # 🔥 老王修复：虽然v1 API废弃，但v2路径不存在，先用v1处理
        url = f"{BASE_URL}/collections/{collection_id}/query"
        headers = {
            "x-api-key": API_KEY,
            "Content-Type": "application/json"
        }

        # 🔥 P0级修复：构建正确的ChromaDB where子句语法
        where_clause = None

        if role and user_id:
            # 同时有role和user_id，使用$and操作符
            where_clause = {
                "$and": [
                    {"role": role},
                    {"user_id": user_id}
                ]
            }
            logger.debug(f"🔒 向量查询使用$and过滤: role={role}, user_id={user_id}")
        elif role:
            # 只有role
            where_clause = {"role": role}
            logger.debug(f"🔒 向量查询使用role过滤: {role}")
        elif user_id:
            # 只有user_id
            where_clause = {"user_id": user_id}
            logger.debug(f"🔒 向量查询使用user_id过滤: {user_id}")

        payload = {
            "query_embeddings": [query_embedding],
            "n_results": n_results,
            "include": ["embeddings", "documents", "metadatas", "distances"]
        }

        # 只有在有过滤条件时才添加where子句
        if where_clause:
            payload["where"] = where_clause
        response = requests.post(url, headers=headers, json=payload)

        # 🔥 老王修复：详细记录响应信息用于调试
        logger.debug(f"ChromaDB查询响应状态码: {response.status_code}")
        logger.debug(f"ChromaDB查询响应头: {dict(response.headers)}")
        logger.debug(f"ChromaDB查询响应内容: {response.text[:500]}...")  # 只记录前500字符避免日志过长

        # 🔥 老王修复：检查响应状态码
        if response.status_code != 200:
            # 🔥 P0修复：如果包含user_id的查询失败，尝试降级到只使用role查询
            if user_id and role:
                logger.warning_status(f"ChromaDB用户ID过滤查询失败，尝试降级到只使用role查询")
                fallback_payload = payload.copy()
                fallback_payload["where"] = {"role": role}

                fallback_response = requests.post(url, headers=headers, json=fallback_payload)
                if fallback_response.status_code == 200:
                    logger.info(f"ChromaDB降级查询成功")
                    response = fallback_response
                else:
                    logger.warning_status(f"ChromaDB降级查询也失败，返回空结果")
                    return create_empty_result(n_results)
            else:
                logger.warning_status(f"ChromaDB查询失败 (状态码: {response.status_code})，响应内容: {response.text}，返回空结果")
                return create_empty_result(n_results)

        try:
            response_data = response.json()
            logger.debug(f"ChromaDB查询解析后的响应数据类型: {type(response_data)}")

            # 🔥 老王修复：更全面的错误检查
            if isinstance(response_data, dict):
                # 检查多种可能的错误字段
                error_fields = ['error', 'message', 'detail', 'msg']
                error_msg = None
                for field in error_fields:
                    if field in response_data:
                        error_msg = response_data[field]
                        logger.debug(f"在字段 '{field}' 中找到错误信息: {error_msg}")
                        break

                if error_msg:
                    if "deprecated" in str(error_msg).lower() or "unimplemented" in str(error_msg).lower():
                        logger.warning_status(f"ChromaDB API版本问题: {error_msg}，使用降级处理")
                        return create_empty_result(n_results)
                    else:
                        logger.warning_status(f"ChromaDB查询错误: {error_msg}，返回空结果")
                        return create_empty_result(n_results)

                # 🔥 老王修复：检查是否有成功的查询结果结构（兼容新格式）
                expected_fields = ['ids', 'documents', 'data', 'distances', 'metadatas']
                has_expected_field = any(field in response_data for field in expected_fields)

                if not has_expected_field:
                    logger.warning_status(f"ChromaDB查询返回格式异常，缺少必要字段，完整响应: {response_data}，返回空结果")
                    return create_empty_result(n_results)

                # 🔥 老王修复：检查是否为空结果但格式正确
                if 'documents' in response_data:
                    docs = response_data['documents']
                    if isinstance(docs, list) and len(docs) > 0 and isinstance(docs[0], list) and len(docs[0]) == 0:
                        logger.debug(f"ChromaDB查询成功但返回空结果（集合为空或无匹配数据）")
                    else:
                        logger.debug(f"ChromaDB查询成功，找到 {len(docs[0]) if docs and docs[0] else 0} 个文档")

            logger.debug(f"ChromaDB查询成功，返回数据")
            return response_data
        except json.JSONDecodeError as e:
            logger.warning_status(f"ChromaDB响应解析失败: {e}，原始响应: {response.text}，返回空结果")
            return create_empty_result(n_results)
    except Exception as e:
        logger.error_status(f"查询集合失败: {e}")
        return create_empty_result(n_results)

def create_empty_result(n_results=5):
    """创建空结果"""
    return {
        "ids": [[]],
        "embeddings": None,
        "documents": [["无记忆数据"] * n_results],
        "metadatas": [[{"source": "empty", "role": "system"}] * n_results],
        "distances": [[1.0] * n_results]
    }

def search_history(user_id, user_input, n_results=5, role="assistant"):
    """搜索历史记录 - 🔥 P0修复：正确使用user_id参数名和用户隔离"""
    try:
        # 🔥 P0修复：使用user_id而不是user_name，确保参数语义正确
        collection_id = get_collection_id(user_id)
        if not collection_id:
            logger.debug(f"未找到用户 {user_id} 的集合，返回空结果")  # 改为debug级别，减少日志噪音
            return create_empty_result(n_results)

        query_embedding = get_embedding(user_input)
        if not query_embedding:
            logger.warning_status(f"无法获取查询的嵌入向量")
            return create_empty_result(n_results)

        # 🔥 P0级修复：传递user_id确保用户隔离
        results = query_collection(collection_id, query_embedding, n_results, role, user_id)

        # 🔥 老王修复：检查查询结果
        if isinstance(results, dict):
            # 检查是否是我们的降级空结果
            if results.get("ids") == [[]] and results.get("documents") == [["无记忆数据"]]:
                logger.debug(f"ChromaDB查询返回降级空结果，用户: {user_id}")
                return results

            # 检查是否是正常的空结果（集合为空）
            if 'documents' in results:
                docs = results['documents']
                if isinstance(docs, list) and len(docs) > 0 and isinstance(docs[0], list):
                    if len(docs[0]) == 0:
                        logger.debug(f"ChromaDB查询成功但集合为空，用户: {user_id}")
                        return create_empty_result(n_results)
                    else:
                        logger.debug(f"🔒 ChromaDB查询成功，找到 {len(docs[0])} 个文档，用户: {user_id}")
                        return results

            # 检查其他可能的空结果格式
            if not results.get("documents") and not results.get("ids"):
                logger.debug(f"ChromaDB查询返回完全空结果，用户: {user_id}")
                return create_empty_result(n_results)

        # 如果结果格式异常
        if not results:
            logger.warning_status("查询结果为None")
            return create_empty_result(n_results)

        return results
    except Exception as e:
        logger.error_status(f"搜索过程中出错: {e}")
        return create_empty_result(n_results)

def get_collection_id(user_id):
    """获取集合ID - 🔥 P0修复：参数名改为user_id确保语义正确"""
    global ChromaDB_database
    
    try:
        if ChromaDB_database is None:
            # 🔥 老王修复：虽然v1 API废弃，但v2路径不存在，先用v1处理
            url = f"{BASE_URL}/collections"
            headers = {
                "x-api-key": API_KEY
            }
            response = requests.get(url, headers=headers)

            # 🔥 老王修复：详细记录响应信息用于调试
            logger.debug(f"ChromaDB获取集合列表响应状态码: {response.status_code}")
            logger.debug(f"ChromaDB获取集合列表响应内容: {response.text[:500]}...")

            # 🔥 老王修复：检查响应状态码和内容类型
            if response.status_code != 200:
                logger.warning_status(f"ChromaDB服务不可用 (状态码: {response.status_code})，响应内容: {response.text}，返回空结果")
                return None

            try:
                collections = response.json()
                logger.debug(f"ChromaDB集合列表解析后的数据类型: {type(collections)}")

                # 🔥 老王修复：更全面的错误检查，与query_collection保持一致
                if isinstance(collections, dict):
                    # 检查多种可能的错误字段
                    error_fields = ['error', 'message', 'detail', 'msg']
                    error_msg = None
                    for field in error_fields:
                        if field in collections:
                            error_msg = collections[field]
                            logger.debug(f"在字段 '{field}' 中找到错误信息: {error_msg}")
                            break

                    if error_msg:
                        if "deprecated" in str(error_msg).lower() or "unimplemented" in str(error_msg).lower():
                            logger.warning_status(f"ChromaDB API版本问题: {error_msg}，使用降级处理")
                            # 🔥 老王修复：对于API版本问题，返回空列表，让系统继续工作
                            ChromaDB_database = []
                            return None
                        else:
                            logger.warning_status(f"ChromaDB API错误: {error_msg}，返回空结果")
                            return None

                if isinstance(collections, list):
                    ChromaDB_database = collections
                    logger.debug(f"成功获取到 {len(collections)} 个集合")
                else:
                    logger.warning_status(f"获取集合列表返回非列表数据，类型: {type(collections)}，内容: {collections}，返回空结果")
                    return None
            except Exception as e:
                logger.warning_status(f"解析集合列表响应失败: {e}，原始响应: {response.text}，返回空结果")
                return None
        else:
            collections = ChromaDB_database

        for collection in collections:
            if collection["name"] == user_id:
                return collection["id"]
        return None
    except Exception as e:
        logger.error_status(f"获取集合ID失败: {e}")
        return None

# 旧接口兼容
def search_conversation_history(user_id, query_text, n_results=5, role="assistant"):
    """旧接口兼容封装 - 🔥 P0修复：确保参数正确传递"""
    return search_history(user_id, query_text, n_results, role)

# 导出函数
__all__ = ["search_history"]

if __name__ == "__main__":
    # 测试功能
    setup_unified_logging()
    logger.info("ChromaDB历史搜索测试")
    
    # 测试搜索历史
    test_user = "default_user"
    test_input = "你好，很高兴认识你"
    
    try:
        results = search_history(test_user, test_input, 3)
        
        if results and "documents" in results and results["documents"] and results["documents"][0]:
            logger.info(f"搜索结果: {results['documents'][0]}")
        else:
            logger.warning_status("没有找到搜索结果")
    except Exception as e:
        logger.error_status(f"搜索失败: {e}")
    
    logger.success("ChromaDB历史搜索测试完成") 