#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单测试 jimeng API - 🔥 老王调试专用
"""

import requests
import json
import time

def test_simple_request():
    """测试简单的同步请求"""
    print("🔥 老王的简单 jimeng API 测试")
    print("=" * 40)
    
    url = "http://124.221.30.195:47653/v1/images/generations"
    
    # 使用有效的 session_id
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    # 简单的请求数据
    data = {
        "model": "jimeng-4.0",
        "prompt": "一朵红色的玫瑰花",
        "negative_prompt": "",
        "width": 512,
        "height": 512,
        "sample_strength": 0.5,
        "response_format": "url"
    }
    
    print(f"📡 请求URL: {url}")
    print(f"🔑 Session ID: {session_id[:20]}...")
    print(f"📝 提示词: {data['prompt']}")
    print(f"📋 请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
    
    try:
        print(f"\n⏳ 发送请求...")
        start_time = time.time()
        response = requests.post(url, headers=headers, json=data, timeout=180)
        elapsed_time = time.time() - start_time
        
        print(f"⏱️  请求耗时: {elapsed_time:.2f}秒")
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📋 完整响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            # 分析响应
            if "code" in result:
                code = result.get("code")
                message = result.get("message", "")
                print(f"\n🔍 分析:")
                print(f"   错误码: {code}")
                print(f"   错误信息: {message}")
                
                if code == 0:
                    print(f"   ✅ 请求成功")
                elif code == -2001:
                    print(f"   ❌ 登录验证失败")
                else:
                    print(f"   ⚠️  其他错误")
            
            if "data" in result:
                data_list = result.get("data", [])
                print(f"   Data 长度: {len(data_list)}")
                
                if data_list:
                    print(f"   ✅ 有数据返回")
                    for i, item in enumerate(data_list):
                        if isinstance(item, dict) and "url" in item:
                            print(f"      图片 {i+1}: {item['url']}")
                else:
                    print(f"   ⚠️  Data 为空")
            
            if "created" in result:
                created = result.get("created")
                print(f"   创建时间戳: {created}")
                
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"   响应内容: {response.text[:500]}")
            
    except requests.exceptions.Timeout:
        print(f"❌ 请求超时")
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_different_params():
    """测试不同参数组合"""
    print(f"\n🧪 测试不同参数组合")
    print("=" * 40)
    
    url = "http://124.221.30.195:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    test_cases = [
        {
            "name": "最小参数",
            "data": {
                "model": "jimeng-4.0",
                "prompt": "猫"
            }
        },
        {
            "name": "标准参数",
            "data": {
                "model": "jimeng-4.0",
                "prompt": "一只可爱的小猫",
                "width": 512,
                "height": 512,
                "response_format": "url"
            }
        },
        {
            "name": "完整参数",
            "data": {
                "model": "jimeng-4.0",
                "prompt": "一只可爱的小猫咪在花园里玩耍",
                "negative_prompt": "低质量",
                "width": 1024,
                "height": 1024,
                "sample_strength": 0.5,
                "response_format": "url"
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {test_case['name']} ---")
        
        try:
            response = requests.post(url, headers=headers, json=test_case['data'], timeout=60)
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                # 简化输出
                if "code" in result:
                    print(f"错误码: {result.get('code')}")
                    if result.get('code') != 0:
                        print(f"错误信息: {result.get('message', '')}")
                
                if "data" in result:
                    data_list = result.get("data", [])
                    print(f"Data 长度: {len(data_list)}")
                    
                    if data_list and len(data_list) > 0:
                        print(f"✅ 成功生成图片")
                        for j, item in enumerate(data_list):
                            if isinstance(item, dict) and "url" in item:
                                print(f"   图片 {j+1}: {item['url'][:50]}...")
                    else:
                        print(f"⚠️  Data 为空")
                        
                if "created" in result:
                    print(f"创建时间: {result.get('created')}")
            else:
                print(f"❌ HTTP错误: {response.text[:100]}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
        
        time.sleep(2)  # 避免请求过快

if __name__ == "__main__":
    test_simple_request()
    test_different_params()
