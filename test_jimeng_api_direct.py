#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 老王测试jimeng API轮询机制
解决"网页端已生成，但API返回空data"的问题
"""

import requests
import json
import time
from datetime import datetime

def test_jimeng_with_polling():
    """测试jimeng API的轮询机制"""
    
    print("🔥 老王测试jimeng API轮询机制")
    print("=" * 70)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # API配置
    url = "http://**************:47653/v1/images/generations"
    session_id = "1e6aa5b4800b56a3e345bacacfaa7c09"
    
    headers = {
        "Authorization": f"Bearer {session_id}",
        "Content-Type": "application/json"
    }
    
    # 测试数据
    data = {
        "model": "jimeng-4.0",
        "prompt": "一只可爱的小猫",
        "negative_prompt": "低质量",
        "width": 1080,
        "height": 1920,
        "sample_strength": 0.8,
        "response_format": "url"
    }
    
    print(f"📤 发送初始请求...")
    print(f"🔗 URL: {url}")
    print(f"🔑 Session: {session_id[:20]}...")
    print(f"📋 请求体: {json.dumps(data, indent=2, ensure_ascii=False)}")
    print("-" * 50)
    
    try:
        # 发送初始请求
        start_time = time.time()
        response = requests.post(url, headers=headers, json=data, timeout=60)
        
        print(f"📥 初始响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📋 初始响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            created_time = result.get("created")
            data_list = result.get("data", [])
            
            print(f"🔍 创建时间: {created_time}")
            print(f"🔍 初始data长度: {len(data_list)}")
            
            # 如果data为空，开始轮询
            if not data_list and created_time:
                print("\n🔄 data为空，开始轮询等待生成完成...")
                
                max_polls = 20  # 最多轮询20次
                poll_interval = 15  # 每15秒轮询一次
                
                for poll_count in range(1, max_polls + 1):
                    print(f"\n🔄 轮询 {poll_count}/{max_polls}...")
                    time.sleep(poll_interval)
                    
                    # 重新请求相同的任务
                    poll_response = requests.post(url, headers=headers, json=data, timeout=60)
                    
                    if poll_response.status_code == 200:
                        poll_result = poll_response.json()
                        poll_data = poll_result.get("data", [])
                        poll_created = poll_result.get("created")
                        
                        print(f"   📥 轮询响应: created={poll_created}, data长度={len(poll_data)}")
                        
                        # 检查是否是同一个任务的结果
                        if poll_created == created_time and poll_data:
                            print(f"   ✅ 找到原任务结果！包含 {len(poll_data)} 张图片")
                            print(f"   🖼️ 图片URL:")
                            for i, item in enumerate(poll_data, 1):
                                if isinstance(item, dict) and "url" in item:
                                    print(f"      {i}. {item['url'][:50]}...")
                            
                            elapsed_time = time.time() - start_time
                            print(f"\n🎉 轮询成功！总耗时: {elapsed_time:.2f}秒")
                            return True
                        
                        # 检查是否有新的任务结果
                        elif poll_data:
                            print(f"   ✅ 发现新任务结果！包含 {len(poll_data)} 张图片")
                            print(f"   🖼️ 图片URL:")
                            for i, item in enumerate(poll_data, 1):
                                if isinstance(item, dict) and "url" in item:
                                    print(f"      {i}. {item['url'][:50]}...")
                            
                            elapsed_time = time.time() - start_time
                            print(f"\n🎉 获取到结果！总耗时: {elapsed_time:.2f}秒")
                            return True
                        else:
                            print(f"   ⏳ 仍在生成中...")
                    else:
                        print(f"   ❌ 轮询失败: {poll_response.status_code}")
                
                print(f"\n⏰ 轮询超时，已尝试 {max_polls} 次")
                return False
            
            elif data_list:
                print(f"✅ 立即获得结果！包含 {len(data_list)} 张图片")
                for i, item in enumerate(data_list, 1):
                    if isinstance(item, dict) and "url" in item:
                        print(f"   {i}. {item['url'][:50]}...")
                return True
            else:
                print("❌ 无创建时间，请求可能失败")
                return False
        else:
            print(f"❌ 初始请求失败: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"💥 异常: {e}")
        return False

def test_multiple_sessions_with_polling():
    """测试多个session的轮询机制"""
    
    print("\n" + "=" * 70)
    print("🔧 测试多个session的轮询机制...")
    
    session_ids = [
        "1e6aa5b4800b56a3e345bacacfaa7c09",
        "96425b141fffc2443d5abdafb494c184", 
        "661e6f442106e10947d9e838a656293b"
    ]
    
    for i, session_id in enumerate(session_ids, 1):
        print(f"\n🧪 测试Session {i}: {session_id[:20]}...")
        
        # 简单测试每个session
        url = "http://**************:47653/v1/images/generations"
        headers = {
            "Authorization": f"Bearer {session_id}",
            "Content-Type": "application/json"
        }
        data = {
            "model": "jimeng-4.0",
            "prompt": "测试",
            "width": 1024,
            "height": 1024,
            "response_format": "url"
        }
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=30)
            if response.status_code == 200:
                result = response.json()
                data_list = result.get("data", [])
                created = result.get("created")
                print(f"   ✅ 响应成功: created={created}, data长度={len(data_list)}")
                if data_list:
                    return session_id  # 返回有效的session_id
            else:
                print(f"   ❌ 失败: {response.status_code}")
        except Exception as e:
            print(f"   💥 异常: {e}")
    
    return None

if __name__ == "__main__":
    print("🚀 开始jimeng API轮询测试...")
    
    # 测试1: 轮询机制
    success = test_jimeng_with_polling()
    
    # 测试2: 多session测试
    if not success:
        print("\n🔧 主测试失败，尝试其他session...")
        valid_session = test_multiple_sessions_with_polling()
        if valid_session:
            print(f"✅ 找到有效session: {valid_session}")
        else:
            print("❌ 所有session都无效")
    
    print("\n" + "=" * 70)
    print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔥 轮询测试完成！")
